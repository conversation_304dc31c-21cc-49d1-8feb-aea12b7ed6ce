import {
  <PERSON>,
  Get,
  HttpStatus,
  Logger,
  NotFoundException,
  Param,
  Query,
  Req,
  UnauthorizedException,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  Rule,
  RuleOrderBy,
  RuleStatus,
  RuleWithUserAndGroup,
} from '../rules/models/rule.model';
import { UserGroupsService } from '../auth/services/user-groups.service';
import { FgaService } from '../auth/fga/fga.service';
import { FGARelation, FGAType } from '../auth/fga/fga.enums';
import { getWorkspaceId, setConfigService } from '../common/config-helpers';
import {
  getVisibleResources,
  WithVisibleResources,
} from '../auth/decorators/with-visible-resources.decorator';
import { ConfigService } from '@nestjs/config';
import { RequireFgaPermission } from '../auth/decorators/require-fga-permission.decorator';
import {
  ApiPaginationParams,
  createPaginatedApiResponse,
  PaginatedApiResponseDto,
} from '../common/dto/pagination.dto';
import { EnrichmentHelper } from '../common/enrichment-helper';
import { DetailedUserResponse } from '../auth/interfaces/user-response.interface';
import { FeedType } from './models/feed.model';
import { getPaginationSizeAndPage } from '../common/helpers/pagination-helpers';
import { SearchService } from '../search/search.service';
import { SortOrder } from '../search/models/sort-order.enum';
import {
  PagedSearchResponseDto,
  PageSearchOptionsDto,
} from '../search/dtos/page-search-options.dto';
import { SearchFiltersDto } from '../search/dtos/search-filters.dto';
import { RulesService } from '../rules/rules.service';
import { mapOpenSearchRuleToRule } from '../common/helpers/rule-mappers';
import { PaginationMode } from '../search/models/pagination-mode.enum';
import { getSortParams } from '../common/helpers/sort-helpers';

// should include for all environments
const BROADCAST_GROUP_IDS = [
  '2c8daa9f-6c82-488e-8618-0a91971d533b',
  '0fe53599-c572-47d9-b675-050ab6e1c10d',
  'db21d501-84b8-4754-94cd-d066dd8af432',
  'baac6d08-72dc-41a7-8528-f3cfaebdfc50',
  '8f9f952d-f357-4baa-a2ea-9266bfbab682',
  '2f720ae1-fd2d-47cd-8e1c-444763a3df24',
  '76cef69f-9780-4188-822d-f448c605ceae',
  '9eef2331-73ea-4a05-910d-77bf70fd6bc9',
  'de065607-9e8c-4ea5-9fa1-678856897005',
];

/**
 * Controller for feed management endpoints
 */
@ApiTags('Feeds')
@ApiBearerAuth()
@Controller('api/v1/feeds')
export class FeedController {
  private readonly logger = new Logger(FeedController.name);
  constructor(
    private readonly rulesService: RulesService,
    private readonly userService: UserGroupsService,
    private readonly fgaService: FgaService,
    private readonly configService: ConfigService,
    private readonly enrichmentHelper: EnrichmentHelper,
    private readonly searchService: SearchService,
  ) {}

  onModuleInit() {
    setConfigService(this.configService);
  }

  /**
   * Get a general feed of rules
   * @param ruleType Optional rule type filter
   * @param tag Optional tag filter
   * @returns List of rules
   */
  @ApiOperation({ summary: 'Get a general feed of rules' })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'size',
    description: 'Number of rules per page',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Feed of rules returned successfully.',
    type: () => PaginatedApiResponseDto.getType(RuleWithUserAndGroup),
  })
  @ApiQuery({
    name: 'feed_type',
    required: false,
    description: 'Filter feed by type',
    enum: FeedType,
    enumName: 'FeedType',
  })
  @Get()
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,
    (req) => getWorkspaceId(),
  )
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_CONTENT_VIEWER)
  async getFeed(
    @Req() req: any,
    @Query('feed_type') feedType?: FeedType,
    @Query() paginationParams?: ApiPaginationParams,
  ): Promise<PaginatedApiResponseDto<RuleWithUserAndGroup>> {
    const visibleGroupContent = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_CONTENT_VIEWER,
    );

    const { size, page } = getPaginationSizeAndPage(paginationParams, 20);

    const authHeader = this.getAuthorizationHeader(req);

    const finalFeedType = feedType || FeedType.FEATURED;

    // 7 days ago in YYYY-MM-DD format
    const sevenDaysAgo = new Date(
      Date.now() - 1000 * 60 * 60 * 24 * 7,
    ).toISOString();

    let result: { items: Rule[]; total: number };
    if (finalFeedType.toUpperCase() === FeedType.TRENDING.toUpperCase()) {
      const response: PagedSearchResponseDto = await this.getTrendingRules(
        req,
        page,
        size,
      );
      result = {
        items: response ? response.data.map(mapOpenSearchRuleToRule) : [],
        total: response ? response.meta.total : 0,
      };
    } else if (
      finalFeedType.toUpperCase() === FeedType.FOLLOWING.toUpperCase()
    ) {
      const followedGroupIds =
        await this.userService.getFollowedGroupIds(authHeader);
      const followedUserIds =
        await this.userService.getFollowedUserIds(authHeader);
      result = await this.rulesService.findFollowedRules(
        followedUserIds,
        followedGroupIds,
        visibleGroupContent,
        page,
        size,
      );
    } else {
      const rankedUsers = await this.getRankedUsers();
      this.logger.log(
        `Success getting rankedUser ${JSON.stringify(rankedUsers)}`,
      );
      const response: PagedSearchResponseDto = await this.getPagedRankedRules(
        req,
        rankedUsers,
        page,
        size,
      );
      result = {
        items: response ? response.data.map(mapOpenSearchRuleToRule) : [],
        total: response ? response.meta.total : 0,
      };
    }

    // Enrich rules with user and group details and bookmark information
    const internalUser = await this.getInternalUserFromRequest(req);
    const enrichedRules = await this.enrichmentHelper.enrichRulesWithDetails(
      result.items,
      authHeader,
      internalUser.id,
    );

    return createPaginatedApiResponse(enrichedRules, result.total, page, size);
  }

  async getTrendingRules(req, page, size): Promise<PagedSearchResponseDto> {
    // TODO: replace ID filtering below with an is_broadcast_rule flag that can be set on ingestion
    const sort = {
      _script: {
        type: 'number',
        order: 'desc',
        script: {
          lang: 'painless',
          source: `
int likes_weight = 10;
int downloads_weight = 20;
int recent_weight = 500;
int score = 0;

if (doc['owner_id'].size() != 0 && params['broadcast_group_ids'].contains(doc['owner_id'].value)) {
  return score;
}

if (params.containsKey("weights")) {
  HashMap weights = params["weights"];
  likes_weight = weights.containsKey("likes") ? weights.get("likes") : likes_weight;
  downloads_weight = weights.containsKey("downloads") ? weights.get("downloads") : downloads_weight;
}

score += doc['likes'].value * likes_weight;
score += doc['downloads'].value * downloads_weight;

long year = (365 * 24 * 60 * 60 * 1000);
if (doc['published_at'].size() != 0) {
  long recent = doc["published_at"].value.toInstant().toEpochMilli() - (params["now_timestamp_millis"] - year);
  if (recent > 0) {
    double factor = recent/(double)year;
    score += (int)(recent_weight * factor);
  }
}
`,
          params: {
            now_timestamp_millis: Date.now(),
            broadcast_group_ids: BROADCAST_GROUP_IDS,
          },
        },
      },
    };

    const options: PageSearchOptionsDto = {
      size: size || 20,
      page: page || 1,
    };
    const visibleGroupContent = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_CONTENT_VIEWER,
    );
    const filters: SearchFiltersDto = {
      owner_ids: visibleGroupContent,
      statuses: [RuleStatus.PUBLISHED],
    };
    return await this.searchService.pagedCustomSortSearch(
      sort,
      undefined,
      filters,
      options,
    );
  }

  async getPagedRankedRules(
    req: any,
    userInfo: object,
    page: number,
    size: number,
    cursor?: undefined,
  ): Promise<PagedSearchResponseDto> {
    // TODO: replace ID filtering below with an is_broadcast_rule flag that can be set on ingestion
    const sort = {
      _script: {
        type: 'number',
        order: 'desc',
        script: {
          lang: 'painless',
          source: `
int likes_weight = 2;
int downloads_weight = 2;
int recent_weight = 500;
int score = 0;

if (doc['owner_id'].size() != 0 && params['broadcast_group_ids'].contains(doc['owner_id'].value)) {
  return score;
}

if (params.containsKey("weights")) {
  HashMap weights = params["weights"];
  likes_weight = weights.containsKey("likes") ? weights.get("likes") : likes_weight;
  downloads_weight = weights.containsKey("downloads") ? weights.get("downloads") : downloads_weight;
}

score += doc['likes'].value * likes_weight;
score += doc['downloads'].value * downloads_weight;
if (params.containsKey("users") && !params["users"].isEmpty()) {
  HashMap users = params["users"];
  if (users.containsKey(doc['created_by'].value)) {
    score += users[doc['created_by'].value] * 10;
  }
}

long year = (365 * 24 * 60 * 60 * 1000);
if (doc['published_at'].size() != 0) {
  long recent = doc["published_at"].value.toInstant().toEpochMilli() - (params["now_timestamp_millis"] - year);
  if (recent > 0) {
    double factor = recent/(double)year;
    score += (int)(recent_weight * factor);
  }
}

return score;
`,
          params: {
            now_timestamp_millis: Date.now(),
            users: userInfo,
            broadcast_group_ids: BROADCAST_GROUP_IDS,
          },
        },
      },
    };

    const options: PageSearchOptionsDto = {
      size: size || 20,
      page: page || 1,
      cursor: cursor,
    };
    const visibleGroupContent = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_CONTENT_VIEWER,
    );
    const filters: SearchFiltersDto = {
      owner_ids: visibleGroupContent,
      statuses: [RuleStatus.PUBLISHED],
    };
    const result = await this.searchService.pagedCustomSortSearch(
      sort,
      undefined,
      filters,
      options,
    );
    return result;
  }

  async getRankedUsers() {
    const indexName = `${this.configService.get<string>('OPENSEARCH_INDEX_PREFIX') || ''}_users`;
    const query = {
      bool: {
        must: [
          {
            match: {
              _index: indexName,
            },
          },
        ],
      },
    };
    const sort = {
      _script: {
        type: 'number',
        order: 'desc',
        script: {
          lang: 'painless',
          source: `
int likes_weight = 2;
int downloads_weight = 2;
int followers_weight = 10;
int bookmarks_weight = 4;
int detections_weight = 1;

if (params.containsKey("weights")) {
  HashMap weights = params["weights"];
  likes_weight = weights.containsKey("likes") ? weights.get("likes") : likes_weight;
  downloads_weight = weights.containsKey("downloads") ? weights.get("downloads") : downloads_weight;
  bookmarks_weight = weights.containsKey("bookmarks") ? weights.get("bookmarks") : bookmarks_weight;
  }
int score = 0;
if (doc.containsKey('likes_count') && doc['likes_count'].size() != 0) {
  score += doc['likes_count'].value * likes_weight;
}
if (doc.containsKey('likes_count') && doc['downloads_count'].size() != 0) {
  score += doc['downloads_count'].value * downloads_weight;
}
if (doc.containsKey('likes_count') && doc['followers_count'].size() != 0) {
  score += doc['followers_count'].value * followers_weight;
}
if (doc.containsKey('likes_count') && doc['bookmarks_count'].size() != 0) {
  score += doc['bookmarks_count'].value * bookmarks_weight;
}
if (doc.containsKey('likes_count') && doc['detections_count'].size() != 0) {
  long det_score = doc['detections_count'].value * detections_weight;
  score += Math.min(det_score, 50);
}
if (doc.containsKey('likes_count') && doc['featured_modifier'].size() != 0) {
  score += doc['featured_modifier'].value;
}

return score;
`,
        },
      },
    };
    const rankedUsers = {};
    const result = await this.searchService.simpleRawSearch(
      'users',
      query,
      sort,
      100,
    );
    const hits = result?.hits?.hits;
    if (hits === undefined) {
      return rankedUsers;
    }

    this.logger.log(`Success getting rankedUser ${JSON.stringify(result)}`);
    for (const hit of hits) {
      const userId = hit['_source']?.['id'];
      const sortValue = hit['sort']?.[0];
      if (userId && sortValue !== undefined) {
        rankedUsers[userId] = sortValue;
      }
    }
    return rankedUsers;
  }

  /**
   * Get a feed of rules for a specific group
   * @param groupId Group ID to filter by
   * @param ruleType Optional rule type filter
   * @param tag Optional tag filter
   * @returns List of rules
   */
  @ApiOperation({ summary: 'Get a group-specific feed of rules' })
  @ApiQuery({
    name: 'q',
    required: false,
    description: 'Search query text. If not provided, returns all rules.',
    example: 'process execution',
  })
  @ApiQuery({
    name: 'sort_by',
    required: false,
    description: 'Order rules by',
    enum: RuleOrderBy,
    enumName: 'RuleOrderBy',
    example: RuleOrderBy.PUBLISHED_AT,
  })
  @ApiQuery({
    name: 'sort_order',
    required: false,
    description: 'Sort order',
    enum: SortOrder,
    enumName: 'SortOrder',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'size',
    description: 'Number of rules per page',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Group feed of rules returned successfully.',
    type: () => PaginatedApiResponseDto.getType(RuleWithUserAndGroup),
  })
  @ApiParam({
    name: 'groupId',
    description: 'Group ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @Get('group/:groupId')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.GROUP_CONTENT_VIEWER,
    FGAType.GROUP,
    (req) => req.params.groupId,
  )
  async getGroupFeed(
    @Req() req: any,
    @Param('groupId') groupId: string,
    @Query('q') q?: string,
    @Query('sort_by') sortBy?: RuleOrderBy,
    @Query('sort_order') sortOrder?: SortOrder,
    @Query() paginationParams?: ApiPaginationParams,
  ): Promise<PaginatedApiResponseDto<RuleWithUserAndGroup>> {
    const { size, page } = getPaginationSizeAndPage(paginationParams, 20);

    const { orderByField, orderByDirection } = getSortParams(sortBy, sortOrder);

    const options: PageSearchOptionsDto = {
      size: size,
      page: page,
      sort_by: orderByField,
      sort_order: orderByDirection,
      pagination_mode: PaginationMode.OFFSET,
    };
    const filters: SearchFiltersDto = {
      owner_ids: [groupId],
    };
    const response: PagedSearchResponseDto =
      await this.searchService.pagedSearch(q, filters, options);
    const result = {
      items: response.data.map(mapOpenSearchRuleToRule),
      total: response.meta.total,
    };

    // Enrich rules with user and group details and bookmark information
    const authorizationHeader = this.getAuthorizationHeader(req);
    const internalUser = await this.getInternalUserFromRequest(req);
    const enrichedRules = await this.enrichmentHelper.enrichRulesWithDetails(
      result.items,
      authorizationHeader,
      internalUser.id,
    );

    return createPaginatedApiResponse(enrichedRules, result.total, page, size);
  }

  /**
   * Get a feed of rules created by a specific user
   * @param userId User ID to filter by
   * @param ruleType Optional rule type filter
   * @param tag Optional tag filter
   * @returns List of rules
   */
  @ApiOperation({ summary: 'Get a user-specific feed of rules' })
  @ApiQuery({
    name: 'q',
    required: false,
    description: 'Search query text. If not provided, returns all rules.',
    example: 'process execution',
  })
  @ApiQuery({
    name: 'sort_by',
    required: false,
    description: 'Order rules by',
    enum: RuleOrderBy,
    enumName: 'RuleOrderBy',
    example: RuleOrderBy.PUBLISHED_AT,
  })
  @ApiQuery({
    name: 'sort_order',
    required: false,
    description: 'Sort order',
    enum: SortOrder,
    enumName: 'SortOrder',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'size',
    description: 'Number of rules per page',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User feed of rules returned successfully.',
    type: () => PaginatedApiResponseDto.getType(RuleWithUserAndGroup),
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @Get('user/:userId')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,
    (req) => getWorkspaceId(),
  )
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_CONTENT_VIEWER)
  async getUserFeed(
    @Req() req: any,
    @Param('userId') userId: string,
    @Query('q') q?: string,
    @Query('sort_by') sortBy?: RuleOrderBy,
    @Query('sort_order') sortOrder?: SortOrder,
    @Query() paginationParams?: ApiPaginationParams,
  ): Promise<PaginatedApiResponseDto<RuleWithUserAndGroup>> {
    const users = await this.userService.getBulkUserDetails(
      [userId],
      this.getAuthorizationHeader(req),
    );
    if (users.length !== 1 || !users[0].username) {
      throw new NotFoundException('User not found');
    }

    const visibleGroupContent = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_CONTENT_VIEWER,
    );

    const { size, page } = getPaginationSizeAndPage(paginationParams, 100);

    const { orderByField, orderByDirection } = getSortParams(sortBy, sortOrder);

    const options: PageSearchOptionsDto = {
      size: size,
      page: page,
      sort_by: orderByField,
      sort_order: orderByDirection,
      pagination_mode: PaginationMode.OFFSET,
    };
    const filters: SearchFiltersDto = {
      created_by: [userId],
      owner_ids: visibleGroupContent,
    };
    const response: PagedSearchResponseDto =
      await this.searchService.pagedSearch(q, filters, options);
    const result = {
      items: response.data.map(mapOpenSearchRuleToRule),
      total: response.meta.total,
    };

    // Enrich rules with user and group details and bookmark information
    const authorizationHeader = this.getAuthorizationHeader(req);
    const internalUser = await this.getInternalUserFromRequest(req);
    const enrichedRules = await this.enrichmentHelper.enrichRulesWithDetails(
      result.items,
      authorizationHeader,
      internalUser.id,
    );

    return createPaginatedApiResponse(enrichedRules, result.total, page, size);
  }

  private getAuthorizationHeader(request: any): string {
    const authorizationHeader = request?.headers['authorization'];
    if (!authorizationHeader) {
      throw new UnauthorizedException('No authorization header provided');
    }
    return authorizationHeader;
  }

  private async getInternalUserFromRequest(
    req: any,
  ): Promise<DetailedUserResponse> {
    const authorizationHeader = this.getAuthorizationHeader(req);
    return this.userService.getInternalUser(authorizationHeader);
  }
}
