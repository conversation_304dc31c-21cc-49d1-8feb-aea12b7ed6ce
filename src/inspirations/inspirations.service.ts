import {
  Injectable,
  Logger,
  NotFoundException,
  InternalServerErrorException,
  BadRequestException,
} from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import {
  InspirationType,
  InspirationStatus,
  Inspiration,
} from './models/inspiration.model';
import {
  CreateUrlInspirationData,
  UpdateUrlInspirationData,
} from './dto/url/url-inspiration.dto';
import {
  CreateTextInspirationData,
  UpdateTextInspirationData,
} from './dto/text/text-inspiration.dto';
import {
  CreateInstructionInspirationData,
  UpdateInstructionInspirationData,
} from './dto/instruction/instruction-inspiration.dto';
import { ListInspirationsQueryDto } from './dto/common/list-inspirations.dto';
import { InspirationRepository } from './repositories/inspiration.repository';
import { InspirationKeys } from './schemas/inspiration-keys.util';
import { StoredInspiration } from './schemas/inspiration.schema';
import {
  FileInspirationMetadataDto,
  UpdateFileInspirationData,
} from './dto/file/file-inspiration.dto';
import { ProcessedFile } from './file/file.service';
import { S3Service } from '../s3/s3.service';
import { ConfigService } from '@nestjs/config';
import { HashUtils } from './utils/hash.utils';
import { RuleInteractionsService } from '../rule-interactions/rule-interactions.service';
import { RuleInteractionType } from '../rule-interactions/models/rule-interaction.model';

@Injectable()
export class InspirationsService {
  private readonly logger = new Logger(InspirationsService.name);
  private readonly s3Bucket: string;
  private readonly GROUP_LOOKUP_BATCH_SIZE = 10; // Batch size for group lookups

  constructor(
    private readonly inspirationRepository: InspirationRepository,
    private readonly s3Service: S3Service,
    private readonly configService: ConfigService,
    private readonly ruleInteractionsService: RuleInteractionsService,
  ) {
    this.s3Bucket =
      this.configService.get<string>('s3.inspirationsBucket') || 'inspirations';
    this.logger.log(`Using S3 bucket: ${this.s3Bucket}`);
  }

  /**
   * Sort inspirations based on sort criteria
   * @private
   */
  private sortInspirations(
    items: StoredInspiration[],
    sortBy: 'created_at' | 'updated_at' | 'name' = 'updated_at',
    sortOrder: 'asc' | 'desc' = 'desc',
  ): StoredInspiration[] {
    this.logger.log('Sorting inspirations', { sortBy, sortOrder });

    const sortedItems = [...items]; // Create a copy to avoid modifying the original

    sortedItems.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'name':
          comparison = (a.name || '').localeCompare(b.name || '');
          break;
        case 'created_at':
          comparison =
            new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
          break;
        case 'updated_at':
        default:
          comparison =
            new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime();
          break;
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return sortedItems;
  }

  /**
   * List inspirations with filtering and pagination
   */
  async listInspirations(
    query: ListInspirationsQueryDto,
    externalUserId: string,
  ): Promise<{
    items: Inspiration[];
    total: number;
    currentPage: number;
    pageSize: number;
  }> {
    try {
      // Step 1: Get all inspirations for the user
      const storedInspirations =
        await this.inspirationRepository.getInspirationsByUser(externalUserId);

      this.logger.log('Retrieved inspirations from repository', {
        externalUserId,
        count: storedInspirations.length,
      });

      // Step 2: Apply filters
      const filteredItems = this.filterInspirations(storedInspirations, query);

      // Step 3: Apply sorting
      const sortedItems = this.sortInspirations(
        filteredItems,
        query.sort_by as 'created_at' | 'updated_at' | 'name',
        query.sort_order as 'asc' | 'desc',
      );

      // Filter to only include ENABLED inspirations
      const enabledItems = this.filterEnabledInspirations(sortedItems);

      // Step 4: Apply pagination
      const page = query.page || 1;
      const size = query.size || 10;
      const { paginatedItems, totalItems, currentPage, pageSize } =
        this.paginateInspirationsWithPageAndSize(enabledItems, page, size);

      // Convert stored inspirations to API model
      const apiItems = paginatedItems.map(this.mapToApiModel);

      return {
        items: apiItems,
        total: totalItems,
        currentPage,
        pageSize,
      };
    } catch (error) {
      this.logger.error(`Failed to list inspirations: ${error.message}`, error);
      throw new InternalServerErrorException('Failed to retrieve inspirations');
    }
  }

  async listInspirationsForGroups(
    query: ListInspirationsQueryDto,
    visibleGroupIds: string[],
  ): Promise<{
    items: Inspiration[];
    total: number;
    currentPage: number;
    pageSize: number;
  }> {
    try {
      // Step 1: Get all inspirations for the groups
      const storedInspirations =
        await this.inspirationRepository.getInspirationsByGroupIds(
          visibleGroupIds,
        );

      this.logger.log('Retrieved inspirations from groups', {
        groups: visibleGroupIds,
        count: storedInspirations.length,
      });

      // Step 2: Apply filters
      const filteredItems = this.filterInspirations(storedInspirations, query);

      // Step 3: Apply sorting
      const sortedItems = this.sortInspirations(
        filteredItems,
        query.sort_by as 'created_at' | 'updated_at' | 'name',
        query.sort_order as 'asc' | 'desc',
      );

      // Filter to only include ENABLED inspirations
      const enabledItems = this.filterEnabledInspirations(sortedItems);

      // Step 4: Apply pagination
      const page = query.page || 1;
      const size = query.size || 10;
      const { paginatedItems, totalItems, currentPage, pageSize } =
        this.paginateInspirationsWithPageAndSize(enabledItems, page, size);

      // Convert stored inspirations to API model
      const apiItems = paginatedItems.map(this.mapToApiModel);

      return {
        items: apiItems,
        total: totalItems,
        currentPage,
        pageSize,
      };
    } catch (error) {
      this.logger.error(`Failed to list inspirations: ${error.message}`, error);
      throw new InternalServerErrorException('Failed to retrieve inspirations');
    }
  }

  /**
   * Filter inspirations to only include enabled ones
   * @private
   */
  private filterEnabledInspirations(
    inspirations: StoredInspiration[],
  ): StoredInspiration[] {
    return inspirations.filter(
      (inspiration) => inspiration.status === InspirationStatus.ENABLED,
    );
  }

  /**
   * Filter inspirations based on search criteria
   * @private
   */
  private filterInspirations(
    items: StoredInspiration[],
    query: ListInspirationsQueryDto,
  ): StoredInspiration[] {
    let filteredItems = [...items];

    // Filter by type
    if (query.type) {
      filteredItems = filteredItems.filter((item) => item.type === query.type);
    }

    // Filter by name (substring match)
    if (query.name) {
      filteredItems = filteredItems.filter(
        (item) =>
          item.name &&
          item.name.toLowerCase().includes(query.name!.toLowerCase()),
      );
    }

    // Filter by tags
    if (query.tags) {
      const tags = query.tags.split(',').map((tag) => tag.trim());
      filteredItems = filteredItems.filter(
        (item) => item.tags && item.tags.some((tag) => tags.includes(tag)),
      );
    }

    // Filter by is_featured
    if (typeof query.is_featured === 'boolean') {
      filteredItems = filteredItems.filter(
        (item) => item.is_featured === query.is_featured,
      );
    }

    return filteredItems;
  }

  /**
   * Get inspiration by ID
   */
  async getInspiration(
    id: string,
    externalUserId: string,
    visibleGroupIds?: string[],
  ): Promise<Inspiration> {
    try {
      // 1. Try to get inspiration directly for the user
      let storedInspiration = await this.inspirationRepository.getInspiration(
        externalUserId,
        id,
      );

      if (storedInspiration) {
        return this.mapToApiModel(storedInspiration);
      }

      // 2. If not found for user, and visibleGroupIds are provided, search in groups in batches
      if (!storedInspiration && visibleGroupIds && visibleGroupIds.length > 0) {
        for (
          let i = 0;
          i < visibleGroupIds.length;
          i += this.GROUP_LOOKUP_BATCH_SIZE
        ) {
          const batchGroupIds = visibleGroupIds.slice(
            i,
            i + this.GROUP_LOOKUP_BATCH_SIZE,
          );

          const batchPromises = batchGroupIds.map((groupId) =>
            this.inspirationRepository.getInspirationByGroupIdAndInspirationId(
              groupId,
              id,
            ),
          );

          const batchResults = await Promise.all(batchPromises);

          for (const result of batchResults) {
            if (result) {
              storedInspiration = result;
              return this.mapToApiModel(storedInspiration); // Found in a group, return immediately
            }
          }
        }
      }

      throw new NotFoundException(`Inspiration with ID ${id} not found`);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to get inspiration: ${error.message}`, error);
      throw new InternalServerErrorException('Failed to retrieve inspiration');
    }
  }

  /**
   * Delete inspiration by ID
   */
  async deleteInspiration(
    id: string,
    externalUserId: string,
  ): Promise<Inspiration | null> {
    this.logger.log(`Soft deleting inspiration by disabling`, {
      id,
      externalUserId,
    });

    try {
      // Update status to DISABLED instead of deleting

      await this.updateInspirationStatus(
        await this.getInspiration(id, externalUserId),
        InspirationStatus.DISABLED,
      );
      const storedInspiration = await this.inspirationRepository.getInspiration(
        externalUserId,
        id,
      );

      if (!storedInspiration) {
        throw new NotFoundException(`Inspiration with ID ${id} not found`);
      }

      this.logger.log(`Successfully disabled inspiration`, {
        id,
        externalUserId,
      });

      return this.mapToApiModel(storedInspiration);
    } catch (error) {
      if (error instanceof NotFoundException) {
        return null;
      }
      this.logger.error(
        `Failed to disable inspiration: ${error.message}`,
        error,
      );
      throw new InternalServerErrorException('Failed to disable inspiration');
    }
  }

  /**
   * Update inspiration status
   */
  async updateInspirationStatus(
    inspiration: Inspiration,
    status: InspirationStatus,
    resetCreationDate: boolean = false,
  ): Promise<Inspiration> {
    this.logger.log(`Updating inspiration status`, {
      id: inspiration.id,
      status,
      externalUserId: inspiration.user_id,
      resetCreationDate,
    });

    try {
      // Get the existing inspiration to get its original created_at
      const existingInspiration =
        await this.inspirationRepository.getInspiration(
          inspiration.user_id,
          inspiration.id,
        );
      if (!existingInspiration) {
        this.logger.log(`Inspiration not found for status update`, {
          id: inspiration.id,
          externalUserId: inspiration.user_id,
        });
        throw new NotFoundException(
          `Inspiration with ID ${inspiration.id} not found`,
        );
      }

      // Create the key for the inspiration
      const key = {
        PK: InspirationKeys.formatUserKey(inspiration.user_id),
        SK: InspirationKeys.formatInspirationKey(inspiration.id),
      };

      const now = new Date().toISOString();

      const updateData: Partial<StoredInspiration> = {
        status,
        updated_at: now,
      };

      // If resetCreationDate is true, update the created_at date to now
      if (resetCreationDate) {
        updateData.created_at = now;
      }

      // Use the appropriate created_at date for the GSI key (either the new one or the existing one)
      const createdAtForGsi = resetCreationDate
        ? now
        : existingInspiration.created_at;
      updateData.gsi_1_sk = InspirationKeys.getStatusCreatedAtKey(
        status,
        createdAtForGsi,
      );

      // If status is being set to DISABLED, remove sharing information
      if (
        status === InspirationStatus.DISABLED &&
        existingInspiration.shared_group_id
      ) {
        this.logger.log(`Removing sharing information during disable`, {
          id: inspiration.id,
          previously_shared_with: existingInspiration.shared_group_id,
        });

        // Use updateWithRemove to remove fields
        const updatedInspiration =
          await this.inspirationRepository.updateWithRemove(
            key,
            updateData,
            ['shared_group_id', 'gsi_4_pk', 'gsi_4_sk'], // Fields to remove
          );

        if (!updatedInspiration) {
          throw new NotFoundException(
            `Inspiration with ID ${inspiration.id} not found`,
          );
        }

        return this.mapToApiModel(updatedInspiration);
      } else {
        // For ENABLED status or no sharing, proceed with normal update
        const updatedInspiration = await this.inspirationRepository.update(
          key,
          updateData,
        );

        if (!updatedInspiration) {
          this.logger.log(`Inspiration not found for status update`, {
            id: inspiration.id,
            externalUserId: inspiration.user_id,
          });
          throw new NotFoundException(
            `Inspiration with ID ${inspiration.id} not found`,
          );
        }

        this.logger.log(`Successfully updated inspiration status`, {
          id: inspiration.id,
          externalUserId: inspiration.user_id,
          status,
          resetCreationDate,
        });

        return this.mapToApiModel(updatedInspiration);
      }
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Failed to update inspiration status: ${error.message}`,
        error,
      );
      throw new InternalServerErrorException(
        'Failed to update inspiration status',
      );
    }
  }

  /**
   * Share inspiration with a group
   */
  async shareInspiration(
    id: string,
    groupId: string,
    externalUserId: string,
    description?: string,
    source?: string,
  ): Promise<void> {
    this.logger.log(`Sharing inspiration with group`, {
      id,
      groupId,
      externalUserId,
      description,
      source,
    });

    try {
      // Get the inspiration to make sure it exists and to get the user ID
      const inspiration = await this.inspirationRepository.getInspiration(
        externalUserId,
        id,
      );
      if (!inspiration) {
        throw new NotFoundException(
          `Inspiration with ID ${id} not found for user ${externalUserId}`,
        );
      }

      // Create the key for the inspiration using the user_id from the fetched inspiration object
      const key = {
        PK: InspirationKeys.formatUserKey(inspiration.user_id),
        SK: InspirationKeys.formatInspirationKey(id),
      };

      const updatePayload: Partial<StoredInspiration> = {
        shared_group_id: groupId,
        gsi_4_pk: `GROUP#${groupId}`,
        gsi_4_sk: `INSPIRATION#${id}`,
        updated_at: new Date().toISOString(),
      };

      if (description !== undefined) {
        updatePayload.description = description;
      }

      if (source !== undefined) {
        updatePayload.source = source;
      }

      await this.inspirationRepository.update(key, updatePayload);

      this.logger.log(`Successfully shared inspiration with group`, {
        id,
        groupId,
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Failed to share inspiration with group: ${error.message}`,
        error,
      );
      throw new InternalServerErrorException(
        'Failed to share inspiration with group',
      );
    }
  }

  /**
   * Remove sharing of inspiration with a group
   */
  async removeSharing(inspiration: Inspiration): Promise<boolean> {
    try {
      if (!inspiration.shared_group_id) {
        return false;
      }

      // Create the key for the inspiration
      const key = {
        PK: InspirationKeys.formatUserKey(inspiration.user_id),
        SK: InspirationKeys.formatInspirationKey(inspiration.id),
      };

      await this.inspirationRepository.updateWithRemove(
        key,
        { updated_at: new Date().toISOString() }, // Fields to update
        ['shared_group_id', 'gsi_4_pk', 'gsi_4_sk', 'is_featured', 'image_url'], // Fields to remove
      );
      return true;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Failed to remove sharing from group: ${error.message}`,
        error,
      );
      throw new InternalServerErrorException(
        'Failed to remove sharing from group',
      );
    }
  }

  async featureInspiration(
    inspiration: Inspiration,
    imageUrl: string,
    description?: string,
    source?: string,
  ): Promise<void> {
    try {
      const key = {
        PK: InspirationKeys.formatUserKey(inspiration.user_id),
        SK: InspirationKeys.formatInspirationKey(inspiration.id),
      };

      const updatePayload: Partial<StoredInspiration> = {
        is_featured: true,
        image_url: imageUrl,
        updated_at: new Date().toISOString(),
      };

      if (description !== undefined) {
        updatePayload.description = description;
      }

      if (source !== undefined) {
        updatePayload.source = source;
      }

      await this.inspirationRepository.update(key, updatePayload);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Failed to feature inspiration: ${error.message}`,
        error,
      );
      throw new InternalServerErrorException('Failed to feature inspiration');
    }
  }

  async unfeatureInspiration(inspiration: Inspiration): Promise<boolean> {
    try {
      if (!inspiration.is_featured) {
        return false;
      }

      // Create the key for the inspiration
      const key = {
        PK: InspirationKeys.formatUserKey(inspiration.user_id),
        SK: InspirationKeys.formatInspirationKey(inspiration.id),
      };

      await this.inspirationRepository.updateWithRemove(
        key,
        { updated_at: new Date().toISOString() }, // Fields to update
        ['is_featured', 'image_url'], // Fields to remove
      );
      return true;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Failed to unfeature inspiration: ${error.message}`,
        error,
      );
      throw new InternalServerErrorException('Failed to unfeature inspiration');
    }
  }

  /**
   * Create URL inspiration
   */
  async createUrlInspiration(
    data: CreateUrlInspirationData,
    externalUserId: string,
    sharedGroupId?: string,
  ): Promise<Inspiration> {
    this.logger.log('Creating URL inspiration', data);

    // Check for existing inspirations with the same hash
    const { isFound, existingInspiration } =
      await this.checkExistingInspirationByHash(
        InspirationType.URL,
        { url: data.url },
        externalUserId,
        data,
      );

    if (isFound && existingInspiration) {
      this.logger.log('Found existing URL inspiration', {
        id: existingInspiration.id,
        status: existingInspiration.status,
        user_id: externalUserId,
      });
      return existingInspiration;
    }

    const id = uuidv4();
    const now = new Date().toISOString();
    const name = data.name || 'Untitled URL';
    const nameLowercase = name.toLowerCase();
    const status = InspirationStatus.ENABLED;

    // Calculate SHA256 hash
    const hash = HashUtils.calculateHash(
      InspirationType.URL,
      { url: data.url },
      externalUserId,
    );

    // Prepare metadata with MITRE information if provided
    const metadata: any = {
      token_count: 0, // Will be calculated elsewhere
      is_indexed: false, // Will be indexed elsewhere
    };

    if (data.mitre_attack) {
      metadata.mitre_attack = data.mitre_attack;
    }

    const newInspiration: StoredInspiration = {
      // Primary keys
      PK: InspirationKeys.formatUserKey(externalUserId),
      SK: InspirationKeys.formatInspirationKey(id),

      // Entity data
      id,
      user_id: externalUserId,
      type: InspirationType.URL,
      name,
      name_lowercase: nameLowercase,
      tags: data.tags || [],
      status,
      created_at: now,
      updated_at: now,
      hash, // Add hash field
      shared_group_id: sharedGroupId,
      // URL specific metadata
      url_metadata: {
        url: data.url,
      },

      // General metadata
      metadata,

      // GSI keys
      gsi_1_pk: InspirationKeys.getUserTypeKey(
        externalUserId,
        InspirationType.URL,
      ),
      gsi_1_sk: InspirationKeys.getStatusCreatedAtKey(status, now),
      gsi_2_pk: InspirationKeys.formatUserKey(externalUserId),
      gsi_2_sk: InspirationKeys.formatNameKey(name),
      gsi_3_pk: InspirationKeys.formatUserKey(externalUserId),
      gsi_3_sk: InspirationKeys.formatHashKey(hash),
    };

    // Set GSI4 keys if shared with a group
    if (sharedGroupId) {
      newInspiration.gsi_4_pk = `GROUP#${sharedGroupId}`;
      newInspiration.gsi_4_sk = `INSPIRATION#${id}`;
    }

    const created = await this.inspirationRepository.create(newInspiration);
    return this.mapToApiModel(created);
  }

  /**
   * Update a URL inspiration
   */
  async updateUrlInspiration(
    inspiration: Inspiration,
    data: UpdateUrlInspirationData,
  ): Promise<Inspiration> {
    this.logger.log(`Updating URL inspiration ${inspiration.id}`, data);

    // Validate that it's a URL inspiration
    if (inspiration.type !== InspirationType.URL) {
      throw new Error(
        `Inspiration with ID ${inspiration.id} is not a URL inspiration`,
      );
    }

    // Create the key for the inspiration
    const key = {
      PK: InspirationKeys.formatUserKey(inspiration.user_id),
      SK: InspirationKeys.formatInspirationKey(inspiration.id),
    };

    // Create update data
    const updateData: Partial<StoredInspiration> = {
      updated_at: new Date().toISOString(),
    };

    // Only update fields that are provided
    if (data.url) {
      updateData.url_metadata = {
        ...inspiration.url_metadata,
        url: data.url,
      };

      // Recalculate hash if URL changed
      const hash = HashUtils.calculateHash(
        InspirationType.URL,
        { url: data.url },
        inspiration.user_id,
      );
      updateData.hash = hash;
      updateData.gsi_3_sk = InspirationKeys.formatHashKey(hash);
    }

    if (data.name) {
      updateData.name = data.name;
      updateData.name_lowercase = data.name.toLowerCase();
      updateData.gsi_2_sk = InspirationKeys.formatNameKey(data.name);
    }

    if (data.tags) {
      updateData.tags = data.tags;
    }

    if (data.description !== undefined) {
      updateData.description = data.description;
    }

    if (data.source !== undefined) {
      updateData.source = data.source;
    }

    // Update metadata with MITRE information if provided
    const metadataUpdate: any = {
      ...(inspiration.metadata || {}),
    };

    if (data.mitre_attack) {
      metadataUpdate.mitre_attack = data.mitre_attack;
      updateData.metadata = metadataUpdate;
    }

    // Update the inspiration
    const updatedInspiration = await this.inspirationRepository.update(
      key,
      updateData,
    );

    if (!updatedInspiration) {
      throw new NotFoundException(
        `Inspiration with ID ${inspiration.id} not found after update`,
      );
    }

    return this.mapToApiModel(updatedInspiration);
  }

  /**
   * Create text inspiration
   */
  async createTextInspiration(
    data: CreateTextInspirationData,
    externalUserId: string,
    userId: string,
    sharedGroupId?: string,
  ): Promise<Inspiration> {
    this.logger.log('Creating text inspiration', data);

    // Check for existing inspirations with the same hash
    const { isFound, existingInspiration } =
      await this.checkExistingInspirationByHash(
        InspirationType.TEXT,
        { text: data.text },
        externalUserId,
        data,
      );

    if (isFound && existingInspiration) {
      this.logger.log('Found existing text inspiration', {
        id: existingInspiration.id,
        status: existingInspiration.status,
        user_id: externalUserId,
      });
      return existingInspiration;
    }

    const id = uuidv4();
    const now = new Date().toISOString();
    const name = data.name || 'Untitled text';
    const nameLowercase = name.toLowerCase();
    const status = InspirationStatus.ENABLED;

    // Calculate SHA256 hash
    const hash = HashUtils.calculateHash(
      InspirationType.TEXT,
      { text: data.text },
      externalUserId,
    );

    // Prepare metadata with MITRE information if provided
    const metadata: any = {
      token_count: 0, // Will be calculated elsewhere
      is_indexed: false, // Will be indexed elsewhere
    };

    if (data.mitre_attack) {
      metadata.mitre_attack = data.mitre_attack;
    }

    const newInspiration: StoredInspiration = {
      // Primary keys
      PK: InspirationKeys.formatUserKey(externalUserId),
      SK: InspirationKeys.formatInspirationKey(id),

      // Entity data
      id,
      user_id: externalUserId,
      type: InspirationType.TEXT,
      name,
      name_lowercase: nameLowercase,
      tags: data.tags || [],
      status,
      created_at: now,
      updated_at: now,
      hash, // Add hash field
      shared_group_id: sharedGroupId,
      rule_type: data.rule_type,
      rule_id: data.rule_id,

      // Text specific metadata
      text_metadata: {
        text: data.text,
      },

      // General metadata
      metadata,

      // GSI keys
      gsi_1_pk: InspirationKeys.getUserTypeKey(
        externalUserId,
        InspirationType.TEXT,
      ),
      gsi_1_sk: InspirationKeys.getStatusCreatedAtKey(status, now),
      gsi_2_pk: InspirationKeys.formatUserKey(externalUserId),
      gsi_2_sk: InspirationKeys.formatNameKey(name),
      gsi_3_pk: InspirationKeys.formatUserKey(externalUserId),
      gsi_3_sk: InspirationKeys.formatHashKey(hash),
    };

    // Set GSI4 keys if shared with a group
    if (sharedGroupId) {
      newInspiration.gsi_4_pk = `GROUP#${sharedGroupId}`;
      newInspiration.gsi_4_sk = `INSPIRATION#${id}`;
    }

    const created = await this.inspirationRepository.create(newInspiration);

    if (data.rule_id) {
      await this.ruleInteractionsService.createInteraction(
        userId,
        data.rule_id,
        RuleInteractionType.ADD_AS_INSPIRATION,
      );
    }
    return this.mapToApiModel(created);
  }

  /**
   * Update text inspiration
   */
  async updateTextInspiration(
    inspiration: Inspiration,
    data: UpdateTextInspirationData,
    userId: string,
  ): Promise<Inspiration> {
    this.logger.log(`Updating text inspiration ${inspiration.id}`, data);

    // Validate that it's a text inspiration
    if (inspiration.type !== InspirationType.TEXT) {
      throw new Error(
        `Inspiration with ID ${inspiration.id} is not a text inspiration`,
      );
    }

    // Create the key for the inspiration
    const key = {
      PK: InspirationKeys.formatUserKey(inspiration.user_id),
      SK: InspirationKeys.formatInspirationKey(inspiration.id),
    };

    // Create update data
    const updateData: Partial<StoredInspiration> = {
      updated_at: new Date().toISOString(),
    };

    // Only update fields that are provided
    if (data.text) {
      updateData.text_metadata = {
        ...inspiration.text_metadata,
        text: data.text,
      };

      // Recalculate hash if text changed
      const hash = HashUtils.calculateHash(
        InspirationType.TEXT,
        { text: data.text },
        inspiration.user_id,
      );
      updateData.hash = hash;
      updateData.gsi_3_sk = InspirationKeys.formatHashKey(hash);
    }

    if (data.name) {
      updateData.name = data.name;
      updateData.name_lowercase = data.name.toLowerCase();
      updateData.gsi_2_sk = InspirationKeys.formatNameKey(data.name);
    }

    if (data.tags) {
      updateData.tags = data.tags;
    }

    if (data.rule_type) {
      updateData.rule_type = data.rule_type;
    }

    if (data.rule_id && inspiration.rule_id !== data.rule_id) {
      if (inspiration.rule_id) {
        // Delete the old interaction
        await this.ruleInteractionsService.deleteInteraction(
          userId,
          inspiration.rule_id,
          RuleInteractionType.ADD_AS_INSPIRATION,
        );
      }
      await this.ruleInteractionsService.createInteraction(
        userId,
        data.rule_id,
        RuleInteractionType.ADD_AS_INSPIRATION,
      );
      updateData.rule_id = data.rule_id;
    }

    if (data.description !== undefined) {
      updateData.description = data.description;
    }

    if (data.source !== undefined) {
      updateData.source = data.source;
    }

    // Update metadata with MITRE information if provided
    const metadataUpdate: any = {
      ...(inspiration.metadata || {}),
    };

    if (data.mitre_attack) {
      metadataUpdate.mitre_attack = data.mitre_attack;
      updateData.metadata = metadataUpdate;
    }

    // Update the inspiration
    const updatedInspiration = await this.inspirationRepository.update(
      key,
      updateData,
    );

    if (!updatedInspiration) {
      throw new NotFoundException(
        `Inspiration with ID ${inspiration.id} not found after update`,
      );
    }

    return this.mapToApiModel(updatedInspiration);
  }

  /**
   * Create instruction inspiration
   */
  async createInstructionInspiration(
    data: CreateInstructionInspirationData,
    externalUserId: string,
    sharedGroupId?: string,
  ): Promise<Inspiration> {
    this.logger.log('Creating instruction inspiration', data);

    // Check for existing inspirations with the same hash
    const { isFound, existingInspiration } =
      await this.checkExistingInspirationByHash(
        InspirationType.INSTRUCTION,
        { instruction: data.instruction },
        externalUserId,
        data,
      );

    if (isFound && existingInspiration) {
      this.logger.log('Found existing instruction inspiration', {
        id: existingInspiration.id,
        status: existingInspiration.status,
        user_id: externalUserId,
      });
      return existingInspiration;
    }

    const id = uuidv4();
    const now = new Date().toISOString();
    const name = data.name || 'Untitled instruction';
    const nameLowercase = name.toLowerCase();
    const status = InspirationStatus.ENABLED;

    // Calculate SHA256 hash
    const hash = HashUtils.calculateHash(
      InspirationType.INSTRUCTION,
      { instruction: data.instruction },
      externalUserId,
    );

    const newInspiration: StoredInspiration = {
      // Primary keys
      PK: InspirationKeys.formatUserKey(externalUserId),
      SK: InspirationKeys.formatInspirationKey(id),

      // Entity data
      id,
      user_id: externalUserId,
      type: InspirationType.INSTRUCTION,
      name,
      name_lowercase: nameLowercase,
      tags: data.tags || [],
      status,
      created_at: now,
      updated_at: now,
      hash, // Add hash field
      shared_group_id: sharedGroupId,
      // Instruction specific metadata
      instruction_metadata: {
        instruction: data.instruction,
      },

      // General metadata
      metadata: {
        token_count: 0, // Will be calculated elsewhere
        is_indexed: false, // Will be indexed elsewhere
      },

      // GSI keys
      gsi_1_pk: InspirationKeys.getUserTypeKey(
        externalUserId,
        InspirationType.INSTRUCTION,
      ),
      gsi_1_sk: InspirationKeys.getStatusCreatedAtKey(status, now),
      gsi_2_pk: InspirationKeys.formatUserKey(externalUserId),
      gsi_2_sk: InspirationKeys.formatNameKey(name),
      gsi_3_pk: InspirationKeys.formatUserKey(externalUserId),
      gsi_3_sk: InspirationKeys.formatHashKey(hash),
    };

    // Set GSI4 keys if shared with a group
    if (sharedGroupId) {
      newInspiration.gsi_4_pk = `GROUP#${sharedGroupId}`;
      newInspiration.gsi_4_sk = `INSPIRATION#${id}`;
    }

    const created = await this.inspirationRepository.create(newInspiration);
    return this.mapToApiModel(created);
  }

  /**
   * Update instruction inspiration
   */
  async updateInstructionInspiration(
    inspiration: Inspiration,
    data: UpdateInstructionInspirationData,
  ): Promise<Inspiration> {
    this.logger.log(`Updating instruction inspiration ${inspiration.id}`, data);

    // Validate that it's an instruction inspiration
    if (inspiration.type !== InspirationType.INSTRUCTION) {
      throw new Error(
        `Inspiration with ID ${inspiration.id} is not an instruction inspiration`,
      );
    }

    // Create the key for the inspiration
    const key = {
      PK: InspirationKeys.formatUserKey(inspiration.user_id),
      SK: InspirationKeys.formatInspirationKey(inspiration.id),
    };

    // Create update data
    const updateData: Partial<StoredInspiration> = {
      updated_at: new Date().toISOString(),
    };

    // Only update fields that are provided
    if (data.instruction) {
      updateData.instruction_metadata = {
        ...inspiration.instruction_metadata,
        instruction: data.instruction,
      };

      // Recalculate hash if instruction changed
      const hash = HashUtils.calculateHash(
        InspirationType.INSTRUCTION,
        { instruction: data.instruction },
        inspiration.user_id,
      );
      updateData.hash = hash;
      updateData.gsi_3_sk = InspirationKeys.formatHashKey(hash);
    }

    if (data.name) {
      updateData.name = data.name;
      updateData.name_lowercase = data.name.toLowerCase();
      updateData.gsi_2_sk = InspirationKeys.formatNameKey(data.name);
    }

    if (data.tags) {
      updateData.tags = data.tags;
    }

    if (data.description !== undefined) {
      updateData.description = data.description;
    }

    if (data.source !== undefined) {
      updateData.source = data.source;
    }

    // Update the inspiration
    const updatedInspiration = await this.inspirationRepository.update(
      key,
      updateData,
    );

    if (!updatedInspiration) {
      throw new NotFoundException(
        `Inspiration with ID ${inspiration.id} not found after update`,
      );
    }

    return this.mapToApiModel(updatedInspiration);
  }

  /**
   * Create a file inspiration
   */
  async createFileInspiration(
    file: ProcessedFile,
    fileKey: string,
    metadata: FileInspirationMetadataDto | undefined,
    externalUserId: string,
    sharedGroupId?: string,
  ): Promise<Inspiration> {
    this.logger.log('Creating file inspiration', {
      fileName: file.originalname,
      fileSize: file.size,
      externalUserId,
    });

    try {
      // Check for existing file inspirations with the same hash
      const fileContent = {
        fileContent: file.buffer.toString('base64'),
        s3_path: fileKey,
        original_file_name: file.originalname,
      };

      const { isFound, existingInspiration } =
        await this.checkExistingInspirationByHash(
          InspirationType.FILE,
          fileContent,
          externalUserId,
          metadata,
        );

      if (isFound && existingInspiration) {
        this.logger.log('Found existing file inspiration', {
          id: existingInspiration.id,
          status: existingInspiration.status,
          user_id: externalUserId,
        });
        return existingInspiration;
      }

      // Generate a unique ID for the inspiration
      const id = uuidv4();
      const now = new Date().toISOString();
      const name = metadata?.name || file.originalname;
      const nameLowercase = name.toLowerCase();
      const status = InspirationStatus.ENABLED;

      // Upload file to S3
      await this.s3Service.uploadFile(
        this.s3Bucket,
        fileKey,
        file.buffer,
        file.mimetype,
      );

      // Calculate SHA256 hash
      const hash = HashUtils.calculateHash(
        InspirationType.FILE,
        fileContent,
        externalUserId,
      );

      // Create new inspiration
      const newInspiration: StoredInspiration = {
        // Primary keys
        PK: InspirationKeys.formatUserKey(externalUserId),
        SK: InspirationKeys.formatInspirationKey(id),

        // Entity data
        id,
        user_id: externalUserId,
        type: InspirationType.FILE,
        name,
        name_lowercase: nameLowercase,
        tags: metadata?.tags || [],
        status,
        created_at: now,
        updated_at: now,
        hash, // Add hash field
        shared_group_id: sharedGroupId,

        // File specific metadata
        file_metadata: {
          s3_path: `s3://${this.s3Bucket}/${fileKey}`,
          original_file_name: file.originalname,
          mime_type: file.mimetype,
          file_size: file.size,
        },

        // General metadata
        metadata: {
          token_count: 0, // Will be calculated elsewhere
          is_indexed: false, // Will be indexed elsewhere
        },

        // GSI keys
        gsi_1_pk: InspirationKeys.getUserTypeKey(
          externalUserId,
          InspirationType.FILE,
        ),
        gsi_1_sk: InspirationKeys.getStatusCreatedAtKey(status, now),
        gsi_2_pk: InspirationKeys.formatUserKey(externalUserId),
        gsi_2_sk: InspirationKeys.formatNameKey(name),
        gsi_3_pk: InspirationKeys.formatUserKey(externalUserId),
        gsi_3_sk: InspirationKeys.formatHashKey(hash),
      };

      // Set GSI4 keys if shared with a group
      if (sharedGroupId) {
        newInspiration.gsi_4_pk = `GROUP#${sharedGroupId}`;
        newInspiration.gsi_4_sk = `INSPIRATION#${id}`;
      }

      const created = await this.inspirationRepository.create(newInspiration);
      return this.mapToApiModel(created);
    } catch (error) {
      this.logger.error(
        `Failed to create file inspiration: ${error.message}`,
        error,
      );
      throw new InternalServerErrorException(
        'Failed to create file inspiration',
      );
    }
  }

  /**
   * Update file inspiration metadata
   */
  async updateFileInspiration(
    inspiration: Inspiration,
    data: UpdateFileInspirationData,
  ): Promise<Inspiration> {
    this.logger.log('Updating file inspiration', {
      id: inspiration.id,
      externalUserId: inspiration.user_id,
    });

    try {
      // Validate that it's a file inspiration
      if (inspiration.type !== InspirationType.FILE) {
        this.logger.log(`Not a file inspiration`, {
          id: inspiration.id,
          type: inspiration.type,
        });
        throw new BadRequestException(`Inspiration is not a file inspiration`);
      }

      // Create the key for the inspiration
      const key = {
        PK: InspirationKeys.formatUserKey(inspiration.user_id),
        SK: InspirationKeys.formatInspirationKey(inspiration.id),
      };

      // Create update data
      const updateData: Partial<StoredInspiration> = {
        updated_at: new Date().toISOString(),
      };

      // Only update fields that are provided
      if (data.name) {
        updateData.name = data.name;
        updateData.name_lowercase = data.name.toLowerCase();
        updateData.gsi_2_sk = InspirationKeys.formatNameKey(data.name);
      }

      if (data.tags) {
        updateData.tags = data.tags;
      }

      if (data.description !== undefined) {
        updateData.description = data.description;
      }

      if (data.source !== undefined) {
        updateData.source = data.source;
      }

      // Update the inspiration
      const updatedInspiration = await this.inspirationRepository.update(
        key,
        updateData,
      );

      if (!updatedInspiration) {
        throw new NotFoundException(
          `Inspiration with ID ${inspiration.id} not found after update`,
        );
      }

      return this.mapToApiModel(updatedInspiration);
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      this.logger.error(
        `Failed to update file inspiration: ${error.message}`,
        error,
      );
      throw new InternalServerErrorException(
        'Failed to update file inspiration',
      );
    }
  }

  /**
   * Map DynamoDB stored inspiration to API model
   * @private
   */
  private mapToApiModel(storedInspiration: StoredInspiration): Inspiration {
    return {
      id: storedInspiration.id,
      type: storedInspiration.type,
      name: storedInspiration.name || '',
      tags: storedInspiration.tags || [],
      status: storedInspiration.status,
      user_id: storedInspiration.user_id,
      created_at: storedInspiration.created_at,
      updated_at: storedInspiration.updated_at,
      hash: storedInspiration.hash,
      shared_group_id: storedInspiration.shared_group_id || undefined,

      // Type-specific metadata
      url_metadata: storedInspiration.url_metadata,
      text_metadata: storedInspiration.text_metadata,
      instruction_metadata: storedInspiration.instruction_metadata,
      file_metadata: storedInspiration.file_metadata,
      mitre_metadata: storedInspiration.mitre_metadata,

      // General metadata
      metadata: storedInspiration.metadata,
      image_url: storedInspiration.image_url || null,
      is_featured: storedInspiration.is_featured || false,
      description: storedInspiration.description || undefined,
      source: storedInspiration.source || undefined,
      rule_type: storedInspiration.rule_type || undefined,
      rule_id: storedInspiration.rule_id || undefined,
    };
  }

  /**
   * List MITRE inspirations with filtering and pagination
   */
  async listMitreInspirations(
    query: ListInspirationsQueryDto,
    externalUserId: string,
  ): Promise<{
    items: Inspiration[];
    total: number;
    currentPage: number;
    pageSize: number;
  }> {
    // Use the fixed "mitre" user ID instead of the current user's ID
    // This is because all MITRE data is saved under the "mitre" user
    const mitreUserId = 'mitre';

    // Clone the query and enforce the MITRE_TECHNIQUE type
    const mitreQuery = { ...query, type: InspirationType.MITRE };

    // Use the special mitreUserId instead of the current user's ID
    try {
      // Step 1: Get all MITRE inspirations
      const storedInspirations =
        await this.inspirationRepository.getInspirationsByUser(mitreUserId);

      this.logger.log('Retrieved MITRE inspirations from repository', {
        count: storedInspirations.length,
      });

      // Step 2: Apply filters - make sure we only get MITRE_TECHNIQUE type
      const filteredItems = this.filterInspirations(
        storedInspirations,
        mitreQuery,
      );

      // Step 3: Apply sorting
      const sortedItems = this.sortInspirations(
        filteredItems,
        query.sort_by as 'created_at' | 'updated_at' | 'name',
        query.sort_order as 'asc' | 'desc',
      );

      // Filter to only include ENABLED inspirations
      const enabledItems = this.filterEnabledInspirations(sortedItems);
      this.logger.log('Filtered to only enabled MITRE inspirations', {
        count: enabledItems.length,
      });

      // Step 4: Apply pagination
      const page = query.page || 1;
      const size = query.size || 10;
      const { paginatedItems, totalItems, currentPage, pageSize } =
        this.paginateInspirationsWithPageAndSize(enabledItems, page, size);

      // Convert stored inspirations to API model
      const apiItems = paginatedItems.map(this.mapToApiModel);

      return {
        items: apiItems,
        total: totalItems,
        currentPage,
        pageSize,
      };
    } catch (error) {
      this.logger.error(
        `Failed to list MITRE inspirations: ${error.message}`,
        error,
      );
      throw new InternalServerErrorException(
        'Failed to retrieve MITRE inspirations',
      );
    }
  }

  /**
   * Paginate inspirations using page and size
   * @private
   */
  private paginateInspirationsWithPageAndSize<T>(
    items: T[],
    page: number = 1,
    size: number = 10,
  ): {
    paginatedItems: T[];
    totalItems: number;
    currentPage: number;
    pageSize: number;
  } {
    const totalItems = items.length;
    const totalPages = Math.ceil(totalItems / size);
    const safeCurrentPage = Math.min(
      Math.max(1, page),
      Math.max(1, totalPages),
    );

    const startIndex = (safeCurrentPage - 1) * size;
    const endIndex = Math.min(startIndex + size, totalItems);

    const paginatedItems = items.slice(startIndex, endIndex);

    return {
      paginatedItems,
      totalItems,
      currentPage: safeCurrentPage,
      pageSize: size,
    };
  }

  /**
   * Find duplicate inspirations by hash
   * @param hash SHA256 hash to search for
   * @param externalUserId User ID
   * @returns List of matching inspirations
   */
  async getInspirationsByHash(
    hash: string,
    externalUserId: string,
  ): Promise<Inspiration[]> {
    this.logger.log('Finding inspirations by hash', {
      hash: hash.substring(0, 10) + '...',
      externalUserId,
    });

    try {
      const inspirations =
        await this.inspirationRepository.findInspirationsByHash(
          externalUserId,
          hash,
        );

      this.logger.log('Found potential matches', {
        count: inspirations.length,
      });

      // Map to API model and sort by created_at (newest first)
      const mappedInspirations = inspirations.map(this.mapToApiModel);

      // Sort by creation date (newest first)
      return this.sortInspirationsByCreationDate(mappedInspirations);
    } catch (error) {
      this.logger.error(
        `Failed to find inspirations by hash: ${error.message}`,
        error,
      );
      throw new InternalServerErrorException(
        'Failed to find inspirations by hash',
      );
    }
  }

  /**
   * Sort inspirations by creation date, newest first
   * @param inspirations Array of inspirations to sort
   * @returns Sorted array of inspirations
   * @private
   */
  private sortInspirationsByCreationDate(
    inspirations: Inspiration[],
  ): Inspiration[] {
    // Create a copy to avoid modifying the original array
    const sortedInspirations = [...inspirations];

    // Sort by created_at in descending order (newest first)
    sortedInspirations.sort((a, b) => {
      // If created_at dates exist on both inspirations, compare them
      if (a.created_at && b.created_at) {
        return (
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      }
      // If only one has a date, prioritize the one with the date
      if (a.created_at) return -1;
      if (b.created_at) return 1;
      // If neither has a date, maintain original order
      return 0;
    });

    return sortedInspirations;
  }

  /**
   * Update an existing inspiration with new data during re-enabling
   * This allows updating title, tags, etc. when re-enabling a disabled inspiration
   * @private
   */
  private async updateExistingInspirationOnReEnable(
    existingInspiration: Inspiration,
    type: InspirationType,
    newData: any,
    externalUserId: string,
    metadata?: any,
  ): Promise<Inspiration> {
    this.logger.log('Updating existing inspiration on re-enable', {
      id: existingInspiration.id,
      type,
      externalUserId,
    });

    try {
      // Create the key for the inspiration
      const key = {
        PK: InspirationKeys.formatUserKey(externalUserId),
        SK: InspirationKeys.formatInspirationKey(existingInspiration.id),
      };

      const now = new Date().toISOString();

      // Start with status update and created_at reset
      const updateData: Partial<StoredInspiration> = {
        status: InspirationStatus.ENABLED,
        created_at: now,
        updated_at: now,
        gsi_1_sk: InspirationKeys.getStatusCreatedAtKey(
          InspirationStatus.ENABLED,
          now,
        ),
      };

      // ONLY update name and tags, not content fields

      // Update name if provided
      if (metadata.name) {
        updateData.name = metadata.name;
        updateData.name_lowercase = metadata.name.toLowerCase();
        updateData.gsi_2_sk = InspirationKeys.formatNameKey(metadata.name);
      }

      // Update tags if provided
      if (metadata.tags) {
        updateData.tags = metadata.tags;
      }

      // Update the inspiration
      const updatedInspiration = await this.inspirationRepository.update(
        key,
        updateData,
      );

      if (!updatedInspiration) {
        throw new NotFoundException(
          `Inspiration with ID ${existingInspiration.id} not found during re-enable update`,
        );
      }

      const result = this.mapToApiModel(updatedInspiration);

      // Add metadata to indicate this was a re-enabled inspiration
      result.metadata = {
        ...result.metadata,
        wasExisting: true,
        wasDisabled: true,
      };

      return result;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Failed to update inspiration during re-enable: ${error.message}`,
        error,
      );
      throw new InternalServerErrorException(
        'Failed to update inspiration during re-enable',
      );
    }
  }

  /**
   * Check for existing inspirations with the same hash and handle appropriately
   * @returns Object containing information about whether an existing inspiration was found
   * @private
   */
  private async checkExistingInspirationByHash(
    type: InspirationType,
    content: any,
    externalUserId: string,
    metadata?: any,
  ): Promise<{
    isFound: boolean;
    existingInspiration?: Inspiration;
    wasDisabled?: boolean;
  }> {
    // Calculate hash for the content
    const hash = HashUtils.calculateHash(type, content, externalUserId);

    // Look for inspirations with the same hash
    const existingInspirations = await this.getInspirationsByHash(
      hash,
      externalUserId,
    );

    if (existingInspirations.length === 0) {
      // No existing inspirations found
      return { isFound: false };
    }

    // Find the most recently created one - they're already sorted by creation date (newest first)
    const mostRecent = existingInspirations[0];

    if (mostRecent.status === InspirationStatus.DISABLED) {
      // Existing inspiration is disabled, re-enable it and update all fields from the DTO
      const enabledInspiration = await this.updateExistingInspirationOnReEnable(
        mostRecent,
        type,
        content,
        externalUserId,
        metadata,
      );

      return {
        isFound: true,
        existingInspiration: enabledInspiration,
        wasDisabled: true,
      };
    } else {
      // Add metadata to indicate this was an existing inspiration
      mostRecent.metadata = {
        ...mostRecent.metadata,
        wasExisting: true,
        wasDisabled: false,
      };

      // Existing inspiration is already enabled
      return {
        isFound: true,
        existingInspiration: mostRecent,
        wasDisabled: false,
      };
    }
  }
}
