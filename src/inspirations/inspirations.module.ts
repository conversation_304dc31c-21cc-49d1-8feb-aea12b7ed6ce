import { Module } from '@nestjs/common';
import { InspirationsController } from './inspirations.controller';
import { InspirationsService } from './inspirations.service';
import { ConfigModule } from '@nestjs/config';
import { DynamoDBModule } from '../dynamodb/dynamodb.module';
import { InspirationRepository } from './repositories/inspiration.repository';
import { FgaModule } from '../auth/fga/fga.module';
import { S3Module } from '../s3/s3.module';
import { AuthModule } from '../auth/auth.module';
import { FileService } from './file/file.service';
import { FileConfigService } from './file/config/file.config';
import { HttpModule } from '@nestjs/axios';
import { UserGroupsService } from '../auth/services/user-groups.service';
import { CommonServicesModule } from '../common/services/common-services.module';
import { RulesModule } from '../rules/rules.module';

/**
 * Mo<PERSON>le for inspirations management
 */
@Module({
  imports: [
    ConfigModule,
    DynamoDBModule,
    FgaModule,
    S3Module,
    AuthModule,
    HttpModule,
    CommonServicesModule,
    RulesModule,
  ],
  controllers: [InspirationsController],
  providers: [
    InspirationsService,
    InspirationRepository,
    FileService,
    FileConfigService,
    UserGroupsService,
  ],
  exports: [InspirationsService],
})
export class InspirationsModule {}
