import { DynamoDBTableSchema } from '../../dynamodb/schemas/schema.base';
import { TableSchemaDefinition } from '../../dynamodb/dynamodb.types';
import {
  InspirationType,
  InspirationStatus,
} from '../models/inspiration.model';
import { InspirationMetadata } from '../models/inspiration-metadata.model';
import { RuleType } from '../../rules/models/rule.model';

/**
 * Inspiration table schema for DynamoDB
 */
export class InspirationTableSchema extends DynamoDBTableSchema {
  /**
   * Get the table name
   */
  get tableName(): string {
    // Allow configuration via environment variable
    return process.env.INSPIRATION_TABLE_NAME || 'inspirations';
  }

  /**
   * Get the table schema definition
   */
  get schema(): TableSchemaDefinition {
    return {
      tableName: this.tableName,
      attributes: [
        // Primary key attributes - MUST be defined as they're used in key schema
        { name: '<PERSON><PERSON>', type: 'S' }, // USER#{user_id}
        { name: 'SK', type: 'S' }, // INSPIRATION#{inspiration_id}

        // GSI attributes - MUST be defined as they're used in GSI key schema
        { name: 'gsi_1_pk', type: 'S' },
        { name: 'gsi_1_sk', type: 'S' },
        { name: 'gsi_2_pk', type: 'S' },
        { name: 'gsi_2_sk', type: 'S' },
        { name: 'gsi_3_pk', type: 'S' },
        { name: 'gsi_3_sk', type: 'S' },
        { name: 'gsi_4_pk', type: 'S' },
        { name: 'gsi_4_sk', type: 'S' },
      ],
      keys: [
        { name: 'PK', type: 'HASH' }, // Partition key
        { name: 'SK', type: 'RANGE' }, // Sort key
      ],
      globalSecondaryIndexes: [
        {
          // GSI1: Filter/Sort by Type, Status, Creation Date
          name: 'GSI1',
          keys: [
            { name: 'gsi_1_pk', type: 'HASH' }, // USER#{user_id}#TYPE#{type}
            { name: 'gsi_1_sk', type: 'RANGE' }, // STATUS#{status}#CREATEDAT#{created_at}
          ],
          projection: {
            type: 'ALL',
          },
        },
        {
          // GSI2: Filter/Sort by Name
          name: 'GSI2',
          keys: [
            { name: 'gsi_2_pk', type: 'HASH' }, // USER#{user_id}
            { name: 'gsi_2_sk', type: 'RANGE' }, // NAME#{name_lowercase}
          ],
          projection: {
            type: 'ALL',
          },
        },
        {
          // GSI3: For duplicate detection using hash
          name: 'GSI3',
          keys: [
            { name: 'gsi_3_pk', type: 'HASH' }, // USER#{user_id}
            { name: 'gsi_3_sk', type: 'RANGE' }, // HASH#{hash}
          ],
          projection: {
            type: 'ALL',
          },
        },
        {
          // GSI4: For querying by group ID
          name: 'GSI4',
          keys: [
            { name: 'gsi_4_pk', type: 'HASH' }, // GROUP#{group_id}
            { name: 'gsi_4_sk', type: 'RANGE' }, // USER#{user_id}#INSPIRATION#{inspiration_id}
          ],
          projection: {
            type: 'ALL',
          },
        },
      ],
      billingMode: 'PAY_PER_REQUEST',
      streamEnabled: true,
      streamViewType: 'NEW_AND_OLD_IMAGES',
    };
  }
}

/**
 * Stored Inspiration representing the DynamoDB item
 */
export class StoredInspiration {
  // Primary key components
  PK: string; // USER#{user_id}
  SK: string; // INSPIRATION#{inspiration_id}

  // Regular attributes
  id: string; // UUID
  user_id: string; // User ID who owns this inspiration
  type: InspirationType; // FILE, URL, TEXT, INSTRUCTION, MITRE
  status: InspirationStatus; // ENABLED, DISABLED
  created_at: string; // ISO 8601 format
  updated_at: string; // ISO 8601 format
  name?: string; // Optional name
  name_lowercase?: string; // Lowercase version for case-insensitive searching
  tags?: string[]; // Set of tags
  hash?: string; // SHA256 hash for duplicate detection
  shared_group_id?: string; // ID of group this inspiration is shared with
  image_url?: string | null; // URL of the image for the URL inspiration
  is_featured?: boolean; // Whether the inspiration is featured
  description?: string; // Description of the inspiration
  source?: string; // Source of the featured inspiration
  rule_type?: RuleType; // Type of rule this text inspiration pertains to
  rule_id?: string; // ID of the rule this text inspiration was created from

  // Type-specific metadata
  file_metadata?: {
    s3_path: string;
    original_file_name: string;
    [key: string]: any;
  };

  url_metadata?: {
    url: string;
    [key: string]: any;
  };

  text_metadata?: {
    text: string;
    [key: string]: any;
  };

  instruction_metadata?: {
    instruction: string;
    [key: string]: any;
  };

  // General metadata
  metadata: InspirationMetadata;

  // GSI attributes
  gsi_1_pk: string; // USER#{user_id}#TYPE#{type}
  gsi_1_sk: string; // STATUS#{status}#CREATEDAT#{created_at}
  gsi_2_pk: string; // USER#{user_id}
  gsi_2_sk?: string; // NAME#{name_lowercase}
  gsi_3_pk: string; // USER#{user_id}
  gsi_3_sk?: string; // HASH#{hash}
  gsi_4_pk?: string; // GROUP#{group_id}
  gsi_4_sk?: string; // INSPIRATION#{inspiration_id} (if shared_group_id is present)

  mitre_metadata?: {
    id: string;
    name: string;
    description: string;
    type: string;
    mappings?: Array<{ [key: string]: any }>;
    [key: string]: any;
  };
}
