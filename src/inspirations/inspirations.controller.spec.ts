import { Test, TestingModule } from '@nestjs/testing';
import { InspirationsController } from './inspirations.controller';
import { InspirationsService } from './inspirations.service';
import { FgaService } from '../auth/fga/fga.service';
import { ConfigService } from '@nestjs/config';
import { FileService } from './file/file.service';
import { UserGroupsService } from '../auth/services/user-groups.service';
import {
  ForbiddenException,
  InternalServerErrorException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { ListInspirationsQueryDto } from './dto/common/list-inspirations.dto';
import {
  Inspiration,
  InspirationType,
  InspirationStatus,
} from './models/inspiration.model';
import { createPaginatedApiResponse } from '../common/dto/pagination.dto';
import { getVisibleResources } from '../auth/decorators/with-visible-resources.decorator';
import { UpdateStatusDto } from './dto/common/update-status.dto';
import {
  CreateUrlInspirationDto,
  UpdateUrlInspirationDto,
} from './dto/url/url-inspiration.dto';
import {
  CreateTextInspirationDto,
  UpdateTextInspirationDto,
} from './dto/text/text-inspiration.dto';
import {
  CreateInstructionInspirationDto,
  UpdateInstructionInspirationDto,
} from './dto/instruction/instruction-inspiration.dto';
import { ProcessedFile } from './file/file.service';
import { UpdateFileInspirationDto } from './dto/file/file-inspiration.dto';
import { FGAType, FGARelation } from '../auth/fga/fga.enums';
import { S3Service } from '../s3/s3.service';
import { ImageService } from '../common/services/image.service';
import { S3SignedUrlOperation } from '../s3/s3.types';
import { ShareInspirationBodyDto } from './dto/share/share-inspiration-body.dto';
import { FeatureInspirationBodyDto } from './dto/feature/feature-inspiration-body.dto';

jest.mock('../auth/decorators/with-visible-resources.decorator', () => ({
  ...jest.requireActual('../auth/decorators/with-visible-resources.decorator'),
  getVisibleResources: jest.fn(),
}));

// Mock config-helpers directly
jest.mock('../common/config-helpers', () => ({
  ...jest.requireActual('../common/config-helpers'), // Keep other helpers like setConfigService if needed
  getWorkspaceId: jest.fn(() => 'test-workspace-id'), // Mock getWorkspaceId
}));

jest.mock('uuid', () => ({
  v4: jest.fn(),
}));

describe('InspirationsController', () => {
  let controller: InspirationsController;
  let mockRequest: any;

  const mockInspirationsService = {
    listInspirations: jest.fn(),
    listInspirationsForGroups: jest.fn(),
    listMitreInspirations: jest.fn(),
    getInspirationsByHash: jest.fn(),
    getInspiration: jest.fn(),
    deleteInspiration: jest.fn(),
    updateInspirationStatus: jest.fn(),
    shareInspiration: jest.fn(),
    removeSharing: jest.fn(),
    createUrlInspiration: jest.fn(),
    updateUrlInspiration: jest.fn(),
    createTextInspiration: jest.fn(),
    updateTextInspiration: jest.fn(),
    createInstructionInspiration: jest.fn(),
    updateInstructionInspiration: jest.fn(),
    createFileInspiration: jest.fn(),
    updateFileInspiration: jest.fn(),
    featureInspiration: jest.fn(),
    unfeatureInspiration: jest.fn(),
  };

  const mockFgaService = {
    checkUserAuthorization: jest.fn(),
    checkUserAuthorizationOnWorkspace: jest.fn(),
    writeTuple: jest.fn(),
    writeTuples: jest.fn(),
    deleteTuples: jest.fn(),
    getAccessibleResourceIds: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn((key: string) => {
      if (key === 's3.inspirationsBucket') return 'test-inspirations-bucket';
      if (key === 'WORKSPACE_ID') return 'test-workspace-id'; // For getWorkspaceId
      return null;
    }),
  };

  const mockFileService = {
    processFile: jest.fn(),
    generateFileKey: jest.fn(),
  };

  const mockUserGroupsService = {
    getInternalUser: jest.fn(),
  };

  const mockS3Service = {
    createSignedUrl: jest.fn(),
    getSignedUrl: jest.fn(),
  };

  const mockImageService = {
    processImage: jest.fn(),
    uploadToS3: jest.fn(),
    deleteFromS3: jest.fn(),
  };

  beforeEach(async () => {
    // Mock common request object parts
    mockRequest = {
      user: { sub: 'test-user-external-id' }, // For getExternalUserIdFromRequest
      params: {}, // For FGA decorators using req.params.id
      query: {}, // For FGA decorators using req.query
      headers: { authorization: 'Bearer test-token' }, // For getInternalUserIdFromRequest
    };

    // Ensure getInternalUser returns a proper user object with id
    mockUserGroupsService.getInternalUser.mockResolvedValue({
      id: 'internal-user-123',
      // Add other properties if needed by the service
    });

    const module: TestingModule = await Test.createTestingModule({
      controllers: [InspirationsController],
      providers: [
        { provide: InspirationsService, useValue: mockInspirationsService },
        { provide: FgaService, useValue: mockFgaService },
        { provide: ConfigService, useValue: mockConfigService },
        { provide: FileService, useValue: mockFileService },
        { provide: UserGroupsService, useValue: mockUserGroupsService },
        { provide: S3Service, useValue: mockS3Service },
        { provide: ImageService, useValue: mockImageService },
      ],
    }).compile();

    controller = module.get<InspirationsController>(InspirationsController);
    // Call onModuleInit to ensure setConfigService is called for getWorkspaceId()
    // This assumes getWorkspaceId() might rely on a global config set by setConfigService
    // or that ConfigService itself is sufficient. The controller calls setConfigService.
    if (controller.onModuleInit) {
      controller.onModuleInit();
    }

    // Mock uuidv4 before each test in this suite
    (require('uuid').v4 as jest.Mock).mockReturnValue('mocked-uuid-for-image');
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks(); // Ensure spies are restored as well
  });

  // --- listInspirations ---
  describe('listInspirations', () => {
    it('should call service and return paginated response', async () => {
      const query: ListInspirationsQueryDto = { page: 1, size: 5 };
      const serviceResult = {
        items: [{ id: '1' } as Inspiration],
        total: 1,
        currentPage: 1,
        pageSize: 5,
      };
      mockInspirationsService.listInspirations.mockResolvedValue(serviceResult);

      const expectedResponse = createPaginatedApiResponse(
        serviceResult.items,
        serviceResult.total,
        serviceResult.currentPage,
        serviceResult.pageSize,
      );

      const result = await controller.listInspirations(mockRequest, query);

      expect(mockInspirationsService.listInspirations).toHaveBeenCalledWith(
        query,
        mockRequest.user.sub,
      );
      expect(result).toEqual(expectedResponse);
    });

    it('should throw if service throws', async () => {
      const query: ListInspirationsQueryDto = {};
      mockInspirationsService.listInspirations.mockRejectedValue(
        new InternalServerErrorException('Service Error'),
      );
      await expect(
        controller.listInspirations(mockRequest, query),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  // --- listInspirationsSharedToGroups ---
  describe('listInspirationsSharedToGroups', () => {
    it('should call service with visible group IDs and return paginated response', async () => {
      const query: ListInspirationsQueryDto = { page: 1, size: 3 };
      const mockVisibleGroupIds = ['groupA', 'groupB'];
      (getVisibleResources as jest.Mock).mockReturnValue(mockVisibleGroupIds);

      const serviceResult = {
        items: [{ id: 'g1' } as Inspiration],
        total: 1,
        currentPage: 1,
        pageSize: 3,
      };
      mockInspirationsService.listInspirationsForGroups.mockResolvedValue(
        serviceResult,
      );
      const expectedResponse = createPaginatedApiResponse(
        serviceResult.items,
        serviceResult.total,
        serviceResult.currentPage,
        serviceResult.pageSize,
      );

      const result = await controller.listInspirationsSharedToGroups(
        mockRequest,
        query,
      );

      expect(getVisibleResources).toHaveBeenCalledWith(
        mockRequest,
        'group',
        'group_inspiration_viewer',
      );
      expect(
        mockInspirationsService.listInspirationsForGroups,
      ).toHaveBeenCalledWith(query, mockVisibleGroupIds);
      expect(result).toEqual(expectedResponse);
    });

    it('should throw if listInspirationsForGroups service throws', async () => {
      (getVisibleResources as jest.Mock).mockReturnValue(['groupX']);
      mockInspirationsService.listInspirationsForGroups.mockRejectedValue(
        new InternalServerErrorException('Group List Service Error'),
      );
      await expect(
        controller.listInspirationsSharedToGroups(mockRequest, {}),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  // --- listMitreInspirations ---
  describe('listMitreInspirations', () => {
    it('should call mitre service method and return paginated response', async () => {
      const query: ListInspirationsQueryDto = { name: 'mitre search' };
      const serviceResult = {
        items: [{ id: 'm1', type: InspirationType.MITRE } as Inspiration],
        total: 1,
        currentPage: 1,
        pageSize: 10,
      };
      mockInspirationsService.listMitreInspirations.mockResolvedValue(
        serviceResult,
      );
      const expectedResponse = createPaginatedApiResponse(
        serviceResult.items,
        serviceResult.total,
        serviceResult.currentPage,
        serviceResult.pageSize,
      );

      const result = await controller.listMitreInspirations(mockRequest, query);

      expect(
        mockInspirationsService.listMitreInspirations,
      ).toHaveBeenCalledWith(query, mockRequest.user.sub);
      expect(result).toEqual(expectedResponse);
    });

    it('should throw if listMitreInspirations service throws', async () => {
      mockInspirationsService.listMitreInspirations.mockRejectedValue(
        new InternalServerErrorException('Mitre Service Error'),
      );
      await expect(
        controller.listMitreInspirations(mockRequest, {}),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  // --- getInspirationsByHash ---
  describe('getInspirationsByHash', () => {
    const hash = 'testHash123';
    const mockInspiration1 = { id: 'id1', name: 'Inspi 1' } as Inspiration;
    const mockInspiration2 = { id: 'id2', name: 'Inspi 2' } as Inspiration;

    it('should return authorized inspirations after FGA check', async () => {
      mockInspirationsService.getInspirationsByHash.mockResolvedValueOnce([
        mockInspiration1,
        mockInspiration2,
      ]);
      // Mock FGA: allow inspi1, deny inspi2
      mockFgaService.checkUserAuthorization.mockImplementation(
        async (tuple) => {
          return tuple.object_id === 'id1';
        },
      );

      const result = await controller.getInspirationsByHash(mockRequest, hash);

      expect(
        mockInspirationsService.getInspirationsByHash,
      ).toHaveBeenCalledWith(hash, mockRequest.user.sub);
      expect(mockFgaService.checkUserAuthorization).toHaveBeenCalledTimes(2); // Called for each inspiration
      expect(mockFgaService.checkUserAuthorization).toHaveBeenCalledWith({
        user_type: 'user',
        user_id: mockRequest.user.sub,
        relation: 'inspiration_viewer',
        object_type: 'inspiration',
        object_id: 'id1',
      });
      expect(mockFgaService.checkUserAuthorization).toHaveBeenCalledWith({
        user_type: 'user',
        user_id: mockRequest.user.sub,
        relation: 'inspiration_viewer',
        object_type: 'inspiration',
        object_id: 'id2',
      });
      expect(result.data).toHaveLength(1);
      expect(result.data?.[0].id).toBe('id1');
    });

    it('should return all inspirations if all are authorized', async () => {
      mockInspirationsService.getInspirationsByHash.mockResolvedValueOnce([
        mockInspiration1,
        mockInspiration2,
      ]);
      mockFgaService.checkUserAuthorization.mockResolvedValue(true); // All allowed
      const result = await controller.getInspirationsByHash(mockRequest, hash);
      expect(result.data).toHaveLength(2);
    });

    it('should return empty if service returns empty or FGA denies all', async () => {
      mockInspirationsService.getInspirationsByHash.mockResolvedValueOnce([]);
      let result = await controller.getInspirationsByHash(mockRequest, hash);
      expect(result.data).toHaveLength(0);

      mockInspirationsService.getInspirationsByHash.mockResolvedValueOnce([
        mockInspiration1,
      ]);
      mockFgaService.checkUserAuthorization.mockResolvedValue(false); // Deny all
      result = await controller.getInspirationsByHash(mockRequest, hash);
      expect(result.data).toHaveLength(0);
    });

    it('should re-throw error from inspirationsService.getInspirationsByHash', async () => {
      mockInspirationsService.getInspirationsByHash.mockRejectedValueOnce(
        new InternalServerErrorException('Service hash error'),
      );
      await expect(
        controller.getInspirationsByHash(mockRequest, hash),
      ).rejects.toThrow(InternalServerErrorException);
    });

    it('should continue if FGA check throws, and exclude that item', async () => {
      mockInspirationsService.getInspirationsByHash.mockResolvedValueOnce([
        mockInspiration1,
        mockInspiration2,
      ]);
      mockFgaService.checkUserAuthorization
        .mockImplementationOnce(async () => true) // Allow id1
        .mockImplementationOnce(async () => {
          throw new Error('FGA check failed for id2');
        }); // Fail for id2

      const result = await controller.getInspirationsByHash(mockRequest, hash);
      expect(result.data).toHaveLength(1);
      expect(result.data?.[0].id).toBe('id1');
      // Log assertion skipped as per user request
    });
  });

  // --- getInspiration (by ID) ---
  describe('getInspiration', () => {
    const inspirationId = 'testInspiId1';
    const mockExternalUserId = 'test-user-external-id'; // from mockRequest.user.sub
    const mockSingleInspiration = {
      id: inspirationId,
      name: 'Fetched Inspi',
      user_id: mockExternalUserId,
      // Add other necessary fields for Inspiration model
      type: InspirationType.TEXT,
      status: InspirationStatus.ENABLED,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    } as Inspiration;

    beforeEach(() => {
      // Ensure mockRequest has user.sub for these tests
      mockRequest.user = { sub: mockExternalUserId };
      mockRequest.params = { id: inspirationId }; // For FGA decorator if it uses req.params.id
      // Reset service mock for getInspiration before each test in this describe block
      mockInspirationsService.getInspiration.mockReset();
      // Reset getVisibleResources mock
      (getVisibleResources as jest.Mock).mockReset();
    });

    it('should call service with id, externalUserId, and visibleGroupIds, then return inspiration', async () => {
      const mockVisibleGroupIds = ['groupA', 'groupB'];
      (getVisibleResources as jest.Mock).mockReturnValue(mockVisibleGroupIds);
      mockInspirationsService.getInspiration.mockResolvedValueOnce(
        mockSingleInspiration,
      );

      const result = await controller.getInspiration(
        mockRequest,
        inspirationId,
      );

      expect(getVisibleResources).toHaveBeenCalledWith(
        mockRequest,
        FGAType.GROUP,
        FGARelation.GROUP_INSPIRATION_VIEWER,
      );
      expect(mockInspirationsService.getInspiration).toHaveBeenCalledWith(
        inspirationId,
        mockExternalUserId,
        mockVisibleGroupIds,
      );
      expect(result.data).toEqual(mockSingleInspiration);
    });

    it('should call service with empty visibleGroupIds if getVisibleResources returns empty', async () => {
      const mockVisibleGroupIdsEmpty: string[] = [];
      (getVisibleResources as jest.Mock).mockReturnValue(
        mockVisibleGroupIdsEmpty,
      );
      mockInspirationsService.getInspiration.mockResolvedValueOnce(
        mockSingleInspiration,
      );

      await controller.getInspiration(mockRequest, inspirationId);

      expect(mockInspirationsService.getInspiration).toHaveBeenCalledWith(
        inspirationId,
        mockExternalUserId,
        mockVisibleGroupIdsEmpty,
      );
    });

    it('should throw NotFoundException if service throws it', async () => {
      (getVisibleResources as jest.Mock).mockReturnValue([]); // Provide a default mock value
      mockInspirationsService.getInspiration.mockRejectedValueOnce(
        new NotFoundException('Not found from service'),
      );
      // mockRequest is already set up with user.sub in beforeEach
      await expect(
        controller.getInspiration(mockRequest, inspirationId),
      ).rejects.toThrow(NotFoundException);
      expect(mockInspirationsService.getInspiration).toHaveBeenCalledWith(
        inspirationId,
        mockExternalUserId,
        [], // Corresponds to the mocked getVisibleResources
      );
    });

    it('should throw InternalServerErrorException if service throws it (and not NotFound)', async () => {
      (getVisibleResources as jest.Mock).mockReturnValue(['groupX']);
      mockInspirationsService.getInspiration.mockRejectedValueOnce(
        new InternalServerErrorException('Some other service error'),
      );
      await expect(
        controller.getInspiration(mockRequest, inspirationId),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  // --- deleteInspiration ---
  describe('deleteInspiration', () => {
    const inspirationId = 'delId1';
    const mockExternalUserId = 'test-user-external-id'; // from mockRequest.user.sub

    beforeEach(() => {
      mockRequest.params.id = inspirationId;
      mockRequest.user = { sub: mockExternalUserId };
      // Reset mocks before each test
      mockInspirationsService.deleteInspiration.mockReset();
      mockFgaService.deleteTuples.mockReset();
    });

    it('should call service to delete inspiration and FGA to delete all tuples if shared_group_id is present', async () => {
      const mockDeletedInspirationWithGroup: Inspiration = {
        id: inspirationId,
        type: InspirationType.TEXT,
        name: 'Test',
        tags: [],
        status: InspirationStatus.ENABLED,
        user_id: mockExternalUserId,
        created_at: '',
        updated_at: '',
        metadata: { token_count: 0, is_indexed: false },
        shared_group_id: 'group-to-delete-from',
      };
      mockInspirationsService.deleteInspiration.mockResolvedValueOnce(
        mockDeletedInspirationWithGroup,
      );
      mockFgaService.deleteTuples.mockResolvedValueOnce(undefined);

      await expect(
        controller.deleteInspiration(mockRequest, inspirationId),
      ).resolves.toBeUndefined();

      expect(mockInspirationsService.deleteInspiration).toHaveBeenCalledWith(
        inspirationId,
        mockExternalUserId,
      );
      expect(mockFgaService.deleteTuples).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            user_type: FGAType.USER,
            user_id: mockExternalUserId,
            relation: FGARelation.INSPIRATION_CREATOR,
            object_type: FGAType.INSPIRATION,
            object_id: inspirationId,
          }),
          expect.objectContaining({
            user_type: FGAType.WORKSPACE,
            user_id: 'test-workspace-id',
            relation: FGARelation.WORKSPACE,
            object_type: FGAType.INSPIRATION,
            object_id: inspirationId,
          }),
          expect.objectContaining({
            user_type: FGAType.GROUP,
            user_id: mockDeletedInspirationWithGroup.shared_group_id,
            relation: FGARelation.SHARED_GROUP,
            object_type: FGAType.INSPIRATION,
            object_id: inspirationId,
          }),
        ]),
      );
      // Ensure all three tuples were included
      const deletedTuplesCall = mockFgaService.deleteTuples.mock.calls[0][0];
      expect(deletedTuplesCall).toHaveLength(3);
    });

    it('should call service to delete inspiration and FGA to delete only creator tuple if shared_group_id is not present', async () => {
      const mockDeletedInspirationNoGroup: Inspiration = {
        id: inspirationId,
        type: InspirationType.TEXT,
        name: 'Test',
        tags: [],
        status: InspirationStatus.ENABLED,
        user_id: mockExternalUserId,
        created_at: '',
        updated_at: '',
        metadata: { token_count: 0, is_indexed: false },
        shared_group_id: undefined,
      };
      mockInspirationsService.deleteInspiration.mockResolvedValueOnce(
        mockDeletedInspirationNoGroup,
      );
      mockFgaService.deleteTuples.mockResolvedValueOnce(undefined);

      await controller.deleteInspiration(mockRequest, inspirationId);

      expect(mockInspirationsService.deleteInspiration).toHaveBeenCalledWith(
        inspirationId,
        mockExternalUserId,
      );
      expect(mockFgaService.deleteTuples).toHaveBeenCalledWith([
        expect.objectContaining({
          user_type: FGAType.USER,
          user_id: mockExternalUserId,
          relation: FGARelation.INSPIRATION_CREATOR,
          object_type: FGAType.INSPIRATION,
          object_id: inspirationId,
        }),
        expect.objectContaining({
          user_type: FGAType.WORKSPACE,
          user_id: 'test-workspace-id',
          relation: FGARelation.WORKSPACE,
          object_type: FGAType.INSPIRATION,
          object_id: inspirationId,
        }),
      ]);
      // Ensure only two tuples were included
      const deletedTuplesCall = mockFgaService.deleteTuples.mock.calls[0][0];
      expect(deletedTuplesCall).toHaveLength(2);
    });

    it('should not call FGA deleteTuples if service.deleteInspiration returns null', async () => {
      mockInspirationsService.deleteInspiration.mockResolvedValueOnce(null);

      await controller.deleteInspiration(mockRequest, inspirationId);

      expect(mockInspirationsService.deleteInspiration).toHaveBeenCalledWith(
        inspirationId,
        mockExternalUserId,
      );
      expect(mockFgaService.deleteTuples).not.toHaveBeenCalled();
    });

    it('should re-throw NotFoundException from service and not call FGA delete', async () => {
      mockInspirationsService.deleteInspiration.mockRejectedValueOnce(
        new NotFoundException(),
      );
      await expect(
        controller.deleteInspiration(mockRequest, inspirationId),
      ).rejects.toThrow(NotFoundException);
      expect(mockFgaService.deleteTuples).not.toHaveBeenCalled();
    });

    it('should re-throw InternalServerErrorException from service and not call FGA delete', async () => {
      mockInspirationsService.deleteInspiration.mockRejectedValueOnce(
        new InternalServerErrorException(),
      );
      await expect(
        controller.deleteInspiration(mockRequest, inspirationId),
      ).rejects.toThrow(InternalServerErrorException);
      expect(mockFgaService.deleteTuples).not.toHaveBeenCalled();
    });
  });

  // --- updateStatus ---
  describe('updateStatus', () => {
    const inspirationId = 'statusUpdateId1';
    const dto: UpdateStatusDto = {
      data: { status: InspirationStatus.DISABLED },
    };
    const mockUpdatedInspiration = {
      id: inspirationId,
      status: InspirationStatus.DISABLED,
    } as Inspiration;
    beforeEach(() => {
      mockRequest.params.id = inspirationId; // For FGA decorator
    });

    it('should call service to update status and return updated inspiration', async () => {
      const mockInspiration = {
        id: inspirationId,
        status: InspirationStatus.ENABLED,
        user_id: mockRequest.user.sub,
        type: InspirationType.TEXT,
        name: 'Test Inspiration',
        tags: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        metadata: { token_count: 0, is_indexed: false },
      } as Inspiration;

      (getVisibleResources as jest.Mock).mockReturnValue([]);
      mockInspirationsService.getInspiration.mockResolvedValueOnce(
        mockInspiration,
      );
      mockInspirationsService.updateInspirationStatus.mockResolvedValueOnce(
        mockUpdatedInspiration,
      );

      const result = await controller.updateStatus(
        mockRequest,
        inspirationId,
        dto,
      );

      expect(getVisibleResources).toHaveBeenCalledWith(
        mockRequest,
        FGAType.GROUP,
        FGARelation.GROUP_INSPIRATION_VIEWER,
      );
      expect(mockInspirationsService.getInspiration).toHaveBeenCalledWith(
        inspirationId,
        mockRequest.user.sub,
        [],
      );
      expect(
        mockInspirationsService.updateInspirationStatus,
      ).toHaveBeenCalledWith(mockInspiration, dto.data.status);
      expect(result.data).toEqual(mockUpdatedInspiration);
    });

    it('should re-throw errors from service during status update', async () => {
      mockInspirationsService.updateInspirationStatus.mockRejectedValueOnce(
        new InternalServerErrorException(),
      );
      await expect(
        controller.updateStatus(mockRequest, inspirationId, dto),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  // --- shareInspiration ---
  describe('shareInspiration', () => {
    const inspirationId = 'shareId1';
    const groupId = 'groupShareTo1';
    const mockShareBody: ShareInspirationBodyDto = {
      description: 'Test Desc',
      source: 'Test Source',
    };
    beforeEach(() => {
      mockRequest.params.id = inspirationId;
      mockRequest.params.groupId = groupId;
    });

    it('should call service to share and FGA to write tuple', async () => {
      mockInspirationsService.shareInspiration.mockResolvedValueOnce(undefined);
      mockFgaService.writeTuple.mockResolvedValueOnce(undefined);
      await expect(
        controller.shareInspiration(
          mockRequest,
          inspirationId,
          groupId,
          mockShareBody,
        ),
      ).resolves.toBeUndefined();
      expect(mockInspirationsService.shareInspiration).toHaveBeenCalledWith(
        inspirationId,
        groupId,
        mockRequest.user.sub,
        mockShareBody.description,
        mockShareBody.source,
      );
      expect(mockFgaService.writeTuple).toHaveBeenCalledWith(
        expect.objectContaining({
          user_type: 'group',
          user_id: groupId,
          relation: 'shared_group',
          object_type: 'inspiration',
          object_id: inspirationId,
        }),
      );
    });

    it('should re-throw errors from service during share', async () => {
      mockInspirationsService.shareInspiration.mockRejectedValueOnce(
        new InternalServerErrorException(),
      );
      await expect(
        controller.shareInspiration(
          mockRequest,
          inspirationId,
          groupId,
          mockShareBody,
        ),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  // --- removeSharing ---
  describe('removeSharing', () => {
    const inspirationId = 'removeShareId1';
    const mockInspirationForRemoveSharing: Inspiration = {
      id: inspirationId,
      type: InspirationType.TEXT,
      name: 'Test Remove Share',
      tags: [],
      status: InspirationStatus.ENABLED,
      user_id: 'test-user-external-id',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      metadata: { token_count: 0, is_indexed: false },
      shared_group_id: 'group-to-remove-from',
    };

    beforeEach(() => {
      mockRequest.params.id = inspirationId;
      mockRequest.user = { sub: 'test-user-external-id' }; // Ensure user is set
      // Ensure getInspiration is mocked for all tests in this describe block
      // to prevent NotFoundException before removeSharing logic is reached.
      mockInspirationsService.getInspiration.mockReset();
      mockInspirationsService.getInspiration.mockResolvedValue(
        mockInspirationForRemoveSharing,
      );
      (getVisibleResources as jest.Mock).mockReset();
      (getVisibleResources as jest.Mock).mockReturnValue([]);
    });

    it('should call service to remove sharing and FGA to delete tuple if updated and shared_group_id exists', async () => {
      const mockRemoveSharingResult = true; // Assuming service.removeSharing indicates success with true
      mockInspirationsService.removeSharing.mockResolvedValueOnce(
        mockRemoveSharingResult,
      );
      mockFgaService.deleteTuples.mockResolvedValueOnce(undefined);

      await expect(
        controller.removeSharing(mockRequest, inspirationId),
      ).resolves.toBeUndefined();

      expect(getVisibleResources).toHaveBeenCalledWith(
        mockRequest,
        FGAType.GROUP,
        FGARelation.GROUP_INSPIRATION_PUBLISHER,
      );
      expect(mockInspirationsService.getInspiration).toHaveBeenCalledWith(
        inspirationId,
        mockRequest.user.sub,
        [], // Expecting the mocked value from getVisibleResources
      );
      expect(mockInspirationsService.removeSharing).toHaveBeenCalledWith(
        mockInspirationForRemoveSharing,
      );
      expect(mockFgaService.deleteTuples).toHaveBeenCalledWith([
        expect.objectContaining({
          user_type: 'group',
          user_id: mockInspirationForRemoveSharing.shared_group_id, // Use the ID from the mocked inspiration
          relation: 'shared_group',
          object_type: 'inspiration',
          object_id: inspirationId,
        }),
      ]);
    });

    it('should not call FGA deleteTuples if service indicates no update occurred', async () => {
      const mockRemoveSharingResult = false; // Service indicates no update
      mockInspirationsService.removeSharing.mockResolvedValueOnce(
        mockRemoveSharingResult,
      );

      await expect(
        controller.removeSharing(mockRequest, inspirationId),
      ).resolves.toBeUndefined();

      expect(getVisibleResources).toHaveBeenCalledWith(
        mockRequest,
        FGAType.GROUP,
        FGARelation.GROUP_INSPIRATION_PUBLISHER,
      );
      expect(mockInspirationsService.getInspiration).toHaveBeenCalledWith(
        inspirationId,
        mockRequest.user.sub,
        [], // Expecting the mocked value from getVisibleResources
      );
      expect(mockInspirationsService.removeSharing).toHaveBeenCalledWith(
        mockInspirationForRemoveSharing,
      );
      expect(mockFgaService.deleteTuples).not.toHaveBeenCalled();
    });

    it('should not call FGA deleteTuples if inspiration has no shared_group_id even if updated', async () => {
      const inspirationWithoutGroup = {
        ...mockInspirationForRemoveSharing,
        shared_group_id: null,
      };
      mockInspirationsService.getInspiration.mockResolvedValueOnce(
        inspirationWithoutGroup,
      ); // Override for this test
      const mockRemoveSharingResult = true; // Service indicates update occurred
      mockInspirationsService.removeSharing.mockResolvedValueOnce(
        mockRemoveSharingResult,
      );

      await expect(
        controller.removeSharing(mockRequest, inspirationId),
      ).resolves.toBeUndefined();

      expect(getVisibleResources).toHaveBeenCalledWith(
        mockRequest,
        FGAType.GROUP,
        FGARelation.GROUP_INSPIRATION_PUBLISHER,
      );
      expect(mockInspirationsService.getInspiration).toHaveBeenCalledWith(
        inspirationId,
        mockRequest.user.sub,
        [], // Expecting the mocked value from getVisibleResources
      );
      expect(mockInspirationsService.removeSharing).toHaveBeenCalledWith(
        inspirationWithoutGroup,
      );
      expect(mockFgaService.deleteTuples).not.toHaveBeenCalled();
    });

    it('should re-throw errors from inspirationsService.removeSharing', async () => {
      // getInspiration is already mocked to succeed in beforeEach
      mockInspirationsService.removeSharing.mockRejectedValueOnce(
        new InternalServerErrorException('Service removeSharing error'),
      );
      await expect(
        controller.removeSharing(mockRequest, inspirationId),
      ).rejects.toThrow(InternalServerErrorException);
      expect(getVisibleResources).toHaveBeenCalledWith(
        mockRequest,
        FGAType.GROUP,
        FGARelation.GROUP_INSPIRATION_PUBLISHER,
      );
      expect(mockInspirationsService.getInspiration).toHaveBeenCalledWith(
        inspirationId,
        mockRequest.user.sub,
        [], // Expecting the mocked value from getVisibleResources
      );
      expect(mockInspirationsService.removeSharing).toHaveBeenCalledWith(
        mockInspirationForRemoveSharing,
      );
    });

    it('should throw NotFoundException if getInspiration fails first', async () => {
      mockInspirationsService.getInspiration.mockResolvedValueOnce(null); // Simulate inspiration not found
      await expect(
        controller.removeSharing(mockRequest, inspirationId),
      ).rejects.toThrow(NotFoundException);
      expect(getVisibleResources).toHaveBeenCalledWith(
        mockRequest,
        FGAType.GROUP,
        FGARelation.GROUP_INSPIRATION_PUBLISHER,
      );
      expect(mockInspirationsService.getInspiration).toHaveBeenCalledWith(
        inspirationId,
        mockRequest.user.sub,
        [], // Expecting the mocked value from getVisibleResources
      );
      expect(mockInspirationsService.removeSharing).not.toHaveBeenCalled();
      expect(mockFgaService.deleteTuples).not.toHaveBeenCalled();
    });
  });

  // --- URL Inspirations ---
  describe('URL Inspirations', () => {
    const urlId = 'urlInspiId1';
    const createUrlDto: CreateUrlInspirationDto = {
      data: { url: 'https://example.com/new', name: 'New URL Inspiration' },
      // shared_group_id can be added for specific tests
    };
    const updateUrlDto: UpdateUrlInspirationDto = {
      data: {
        url: 'https://example.com/updated',
        name: 'Updated URL Inspiration',
      },
    };
    const mockUrlInspiration = {
      id: urlId,
      name: 'URL Inspi',
      type: InspirationType.URL,
    } as Inspiration;

    describe('createUrlInspiration', () => {
      it('should create URL inspiration if authorized (no shared_group_id)', async () => {
        mockFgaService.checkUserAuthorizationOnWorkspace.mockResolvedValueOnce(
          true,
        ); // For private_inspiration_creator
        mockInspirationsService.createUrlInspiration.mockResolvedValueOnce(
          mockUrlInspiration,
        );
        mockFgaService.writeTuples.mockResolvedValueOnce(undefined); // For createInspirationTuples

        // DTO without shared_group_id
        const dtoWithoutGroup: CreateUrlInspirationDto = {
          data: { ...createUrlDto.data }, // Use existing data, ensure no shared_group_id
        };

        const result = await controller.createUrlInspiration(
          mockRequest,
          dtoWithoutGroup,
        );

        expect(
          mockFgaService.checkUserAuthorizationOnWorkspace,
        ).toHaveBeenCalled();
        expect(
          mockInspirationsService.createUrlInspiration,
        ).toHaveBeenCalledWith(
          dtoWithoutGroup.data,
          mockRequest.user.sub,
          undefined, // Explicitly undefined for shared_group_id
        );
        expect(mockFgaService.writeTuples).toHaveBeenCalledWith([
          expect.objectContaining({
            user_type: FGAType.USER,
            user_id: mockRequest.user.sub,
            relation: FGARelation.INSPIRATION_CREATOR,
            object_type: FGAType.INSPIRATION,
            object_id: mockUrlInspiration.id,
          }),
          expect.objectContaining({
            user_type: FGAType.WORKSPACE,
            user_id: 'test-workspace-id',
            relation: FGARelation.WORKSPACE,
            object_type: FGAType.INSPIRATION,
            object_id: mockUrlInspiration.id,
          }),
        ]);
        expect(result.data).toEqual(mockUrlInspiration);
      });

      it('should create URL inspiration and share with group if shared_group_id is provided and authorized', async () => {
        const groupId = 'test-group-for-url';
        const dtoWithGroup: CreateUrlInspirationDto = {
          ...createUrlDto,
          shared_group_id: groupId,
        };

        mockFgaService.checkUserAuthorizationOnWorkspace.mockResolvedValueOnce(
          true,
        ); // For private_inspiration_creator
        mockFgaService.checkUserAuthorization.mockResolvedValueOnce(true); // For group_inspiration_publisher

        mockInspirationsService.createUrlInspiration.mockResolvedValueOnce(
          mockUrlInspiration,
        );
        mockFgaService.writeTuples.mockResolvedValueOnce(undefined);

        const result = await controller.createUrlInspiration(
          mockRequest,
          dtoWithGroup,
        );

        expect(
          mockInspirationsService.createUrlInspiration,
        ).toHaveBeenCalledWith(
          dtoWithGroup.data,
          mockRequest.user.sub,
          groupId,
        );
        expect(mockFgaService.checkUserAuthorization).toHaveBeenCalledWith(
          expect.objectContaining({
            user_type: FGAType.USER,
            user_id: mockRequest.user.sub,
            relation: FGARelation.GROUP_INSPIRATION_PUBLISHER,
            object_type: FGAType.GROUP,
            object_id: groupId,
          }),
        );
        expect(mockFgaService.writeTuples).toHaveBeenCalledWith(
          expect.arrayContaining([
            expect.objectContaining({
              user_type: FGAType.USER,
              user_id: mockRequest.user.sub,
              relation: FGARelation.INSPIRATION_CREATOR,
              object_type: FGAType.INSPIRATION,
              object_id: mockUrlInspiration.id,
            }),
            expect.objectContaining({
              user_type: FGAType.WORKSPACE,
              user_id: 'test-workspace-id',
              relation: FGARelation.WORKSPACE,
              object_type: FGAType.INSPIRATION,
              object_id: mockUrlInspiration.id,
            }),
            expect.objectContaining({
              user_type: FGAType.GROUP,
              user_id: groupId,
              relation: FGARelation.SHARED_GROUP,
              object_type: FGAType.INSPIRATION,
              object_id: mockUrlInspiration.id,
            }),
          ]),
        );
        const writtenTuplesCall = mockFgaService.writeTuples.mock.calls[0][0];
        expect(writtenTuplesCall).toHaveLength(3);
        expect(result.data).toEqual(mockUrlInspiration);
      });

      it('should throw ForbiddenException if not authorized to create (private)', async () => {
        mockFgaService.checkUserAuthorizationOnWorkspace.mockResolvedValueOnce(
          false,
        );
        await expect(
          controller.createUrlInspiration(mockRequest, createUrlDto),
        ).rejects.toThrow(ForbiddenException);
      });

      it('should throw ForbiddenException if not authorized for group sharing', async () => {
        const dtoWithGroup: CreateUrlInspirationDto = {
          ...createUrlDto,
          shared_group_id: 'group123',
        };
        mockFgaService.checkUserAuthorizationOnWorkspace.mockResolvedValueOnce(
          true,
        ); // Allowed private
        mockFgaService.checkUserAuthorization.mockResolvedValueOnce(false); // Denied group publisher

        await expect(
          controller.createUrlInspiration(mockRequest, dtoWithGroup),
        ).rejects.toThrow(ForbiddenException);
        expect(mockFgaService.checkUserAuthorization).toHaveBeenCalledWith(
          expect.objectContaining({
            user_id: mockRequest.user.sub,
            relation: 'group_inspiration_publisher',
            object_id: 'group123',
          }),
        );
      });
    });

    describe('updateUrlInspiration', () => {
      beforeEach(() => {
        mockRequest.params.id = urlId;
        // Reset getInspiration mock to prevent state bleeding
        mockInspirationsService.getInspiration.mockReset();
      });

      it('should update URL inspiration', async () => {
        const mockInspiration = {
          id: urlId,
          type: InspirationType.URL,
          user_id: mockRequest.user.sub,
          name: 'Test URL',
          tags: [],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: { token_count: 0, is_indexed: false },
          status: InspirationStatus.ENABLED,
        } as Inspiration;

        (getVisibleResources as jest.Mock).mockReturnValue([]);
        mockInspirationsService.getInspiration.mockResolvedValueOnce(
          mockInspiration,
        );
        mockInspirationsService.updateUrlInspiration.mockResolvedValueOnce(
          mockUrlInspiration,
        );

        const result = await controller.updateUrlInspiration(
          mockRequest,
          urlId,
          updateUrlDto,
        );

        expect(getVisibleResources).toHaveBeenCalledWith(
          mockRequest,
          FGAType.GROUP,
          FGARelation.GROUP_INSPIRATION_VIEWER,
        );
        expect(mockInspirationsService.getInspiration).toHaveBeenCalledWith(
          urlId,
          mockRequest.user.sub,
          [],
        );
        expect(
          mockInspirationsService.updateUrlInspiration,
        ).toHaveBeenCalledWith(mockInspiration, updateUrlDto.data);
        expect(result.data).toEqual(mockUrlInspiration);
      });

      it('should re-throw NotFound from service on update', async () => {
        mockInspirationsService.updateUrlInspiration.mockRejectedValueOnce(
          new NotFoundException(),
        );
        await expect(
          controller.updateUrlInspiration(mockRequest, urlId, updateUrlDto),
        ).rejects.toThrow(NotFoundException);
      });
    });
  });

  // --- TEXT Inspirations ---
  describe('TEXT Inspirations', () => {
    const textId = 'textInspiId1';
    const createTextDto: CreateTextInspirationDto = {
      data: { text: 'Sample text content', name: 'New Text Inspiration' },
    };
    const updateTextDto: UpdateTextInspirationDto = {
      data: { text: 'Updated text content', name: 'Updated Text Inspiration' },
    };
    const mockTextInspiration = {
      id: textId,
      name: 'Text Inspi',
      type: InspirationType.TEXT,
    } as Inspiration;

    describe('createTextInspiration', () => {
      it('should create TEXT inspiration if authorized (no shared_group_id)', async () => {
        mockFgaService.checkUserAuthorizationOnWorkspace.mockResolvedValueOnce(
          true,
        );
        mockInspirationsService.createTextInspiration.mockResolvedValueOnce(
          mockTextInspiration,
        );
        mockFgaService.writeTuples.mockResolvedValueOnce(undefined);

        // DTO without shared_group_id
        const dtoWithoutGroup: CreateTextInspirationDto = {
          data: { ...createTextDto.data },
        };

        const result = await controller.createTextInspiration(
          mockRequest,
          dtoWithoutGroup,
        );
        expect(
          mockInspirationsService.createTextInspiration,
        ).toHaveBeenCalledWith(
          dtoWithoutGroup.data,
          mockRequest.user.sub,
          'internal-user-123', // internalUserId parameter
          undefined, // Explicitly undefined for shared_group_id
        );
        expect(mockFgaService.writeTuples).toHaveBeenCalledWith([
          expect.objectContaining({
            user_type: FGAType.USER,
            user_id: mockRequest.user.sub,
            relation: FGARelation.INSPIRATION_CREATOR,
            object_type: FGAType.INSPIRATION,
            object_id: mockTextInspiration.id,
          }),
          expect.objectContaining({
            user_type: FGAType.WORKSPACE,
            user_id: 'test-workspace-id',
            relation: FGARelation.WORKSPACE,
            object_type: FGAType.INSPIRATION,
            object_id: mockTextInspiration.id,
          }),
        ]);
        expect(result.data).toEqual(mockTextInspiration);
      });

      it('should create TEXT inspiration and share with group if shared_group_id is provided and authorized', async () => {
        const groupId = 'test-group-for-text';
        const dtoWithGroup: CreateTextInspirationDto = {
          ...createTextDto,
          shared_group_id: groupId,
        };

        mockFgaService.checkUserAuthorizationOnWorkspace.mockResolvedValueOnce(
          true,
        );
        mockFgaService.checkUserAuthorization.mockResolvedValueOnce(true);
        mockInspirationsService.createTextInspiration.mockResolvedValueOnce(
          mockTextInspiration,
        );
        mockFgaService.writeTuples.mockResolvedValueOnce(undefined);

        const result = await controller.createTextInspiration(
          mockRequest,
          dtoWithGroup,
        );

        expect(
          mockInspirationsService.createTextInspiration,
        ).toHaveBeenCalledWith(
          dtoWithGroup.data,
          mockRequest.user.sub,
          'internal-user-123', // internalUserId parameter
          groupId,
        );
        expect(mockFgaService.checkUserAuthorization).toHaveBeenCalledWith(
          expect.objectContaining({
            user_type: FGAType.USER,
            user_id: mockRequest.user.sub,
            relation: FGARelation.GROUP_INSPIRATION_PUBLISHER,
            object_type: FGAType.GROUP,
            object_id: groupId,
          }),
        );
        expect(mockFgaService.writeTuples).toHaveBeenCalledWith(
          expect.arrayContaining([
            expect.objectContaining({
              user_type: FGAType.USER,
              user_id: mockRequest.user.sub,
              relation: FGARelation.INSPIRATION_CREATOR,
              object_type: FGAType.INSPIRATION,
              object_id: mockTextInspiration.id,
            }),
            expect.objectContaining({
              user_type: FGAType.WORKSPACE,
              user_id: 'test-workspace-id',
              relation: FGARelation.WORKSPACE,
              object_type: FGAType.INSPIRATION,
              object_id: mockTextInspiration.id,
            }),
            expect.objectContaining({
              user_type: FGAType.GROUP,
              user_id: groupId,
              relation: FGARelation.SHARED_GROUP,
              object_type: FGAType.INSPIRATION,
              object_id: mockTextInspiration.id,
            }),
          ]),
        );
        const writtenTuplesCallText =
          mockFgaService.writeTuples.mock.calls[0][0];
        expect(writtenTuplesCallText).toHaveLength(3);
        expect(result.data).toEqual(mockTextInspiration);
      });

      // Add authorization failure tests similar to URL create
      it('should throw ForbiddenException if not authorized to create (private)', async () => {
        mockFgaService.checkUserAuthorizationOnWorkspace.mockResolvedValueOnce(
          false,
        );
        await expect(
          controller.createTextInspiration(mockRequest, createTextDto),
        ).rejects.toThrow(ForbiddenException);
      });
    });

    describe('updateTextInspiration', () => {
      beforeEach(() => {
        mockRequest.params.id = textId;
        // Reset getInspiration mock to prevent state bleeding
        mockInspirationsService.getInspiration.mockReset();
      });
      it('should update TEXT inspiration', async () => {
        const mockInspiration = {
          id: textId,
          type: InspirationType.TEXT,
          user_id: mockRequest.user.sub,
          name: 'Test Text',
          tags: [],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: { token_count: 0, is_indexed: false },
          status: InspirationStatus.ENABLED,
        } as Inspiration;

        (getVisibleResources as jest.Mock).mockReturnValue([]);
        mockInspirationsService.getInspiration.mockResolvedValueOnce(
          mockInspiration,
        );
        mockInspirationsService.updateTextInspiration.mockResolvedValueOnce(
          mockTextInspiration,
        );

        const result = await controller.updateTextInspiration(
          mockRequest,
          textId,
          updateTextDto,
        );

        expect(getVisibleResources).toHaveBeenCalledWith(
          mockRequest,
          FGAType.GROUP,
          FGARelation.GROUP_INSPIRATION_VIEWER,
        );
        expect(mockInspirationsService.getInspiration).toHaveBeenCalledWith(
          textId,
          mockRequest.user.sub,
          [],
        );
        expect(
          mockInspirationsService.updateTextInspiration,
        ).toHaveBeenCalledWith(
          mockInspiration,
          updateTextDto.data,
          'internal-user-123',
        );
        expect(result.data).toEqual(mockTextInspiration);
      });
      // Add NotFound similar to URL update
      it('should re-throw NotFound from service on update', async () => {
        mockInspirationsService.updateTextInspiration.mockRejectedValueOnce(
          new NotFoundException(),
        );
        await expect(
          controller.updateTextInspiration(mockRequest, textId, updateTextDto),
        ).rejects.toThrow(NotFoundException);
      });
    });
  });

  // --- INSTRUCTION Inspirations ---
  describe('INSTRUCTION Inspirations', () => {
    const instructionId = 'instrInspiId1';
    const createInstructionDto: CreateInstructionInspirationDto = {
      data: {
        instruction: 'Sample instruction',
        name: 'New Instruction Inspiration',
      },
    };
    const updateInstructionDto: UpdateInstructionInspirationDto = {
      data: {
        instruction: 'Updated instruction',
        name: 'Updated Instruction Inspiration',
      },
    };
    const mockInstructionInspiration = {
      id: instructionId,
      name: 'Instruction Inspi',
      type: InspirationType.INSTRUCTION,
    } as Inspiration;

    describe('createInstructionInspiration', () => {
      it('should create INSTRUCTION inspiration if authorized (no shared_group_id)', async () => {
        mockFgaService.checkUserAuthorizationOnWorkspace.mockResolvedValueOnce(
          true,
        );
        mockInspirationsService.createInstructionInspiration.mockResolvedValueOnce(
          mockInstructionInspiration,
        );
        mockFgaService.writeTuples.mockResolvedValueOnce(undefined);

        // Create DTO without shared_group_id
        const dtoWithoutGroup: CreateInstructionInspirationDto = {
          data: { ...createInstructionDto.data }, // Use existing data but ensure no shared_group_id
        };

        const result = await controller.createInstructionInspiration(
          mockRequest,
          dtoWithoutGroup,
        );

        expect(
          mockInspirationsService.createInstructionInspiration,
        ).toHaveBeenCalledWith(
          dtoWithoutGroup.data,
          mockRequest.user.sub,
          undefined, // Explicitly undefined for shared_group_id
        );
        expect(mockFgaService.writeTuples).toHaveBeenCalledWith([
          expect.objectContaining({
            user_type: FGAType.USER,
            user_id: mockRequest.user.sub,
            relation: FGARelation.INSPIRATION_CREATOR,
            object_type: FGAType.INSPIRATION,
            object_id: mockInstructionInspiration.id,
          }),
          expect.objectContaining({
            user_type: FGAType.WORKSPACE,
            user_id: 'test-workspace-id',
            relation: FGARelation.WORKSPACE,
            object_type: FGAType.INSPIRATION,
            object_id: mockInstructionInspiration.id,
          }),
        ]);
        expect(result.data).toEqual(mockInstructionInspiration);
      });

      it('should create INSTRUCTION inspiration and share with group if shared_group_id is provided and authorized', async () => {
        const groupId = 'test-group-for-instruction';
        const dtoWithGroup: CreateInstructionInspirationDto = {
          ...createInstructionDto,
          shared_group_id: groupId,
        };

        mockFgaService.checkUserAuthorizationOnWorkspace.mockResolvedValueOnce(
          true,
        ); // For private_inspiration_creator
        mockFgaService.checkUserAuthorization.mockResolvedValueOnce(true); // For group_inspiration_publisher

        mockInspirationsService.createInstructionInspiration.mockResolvedValueOnce(
          mockInstructionInspiration, // Assuming this ID is used for tuples
        );
        mockFgaService.writeTuples.mockResolvedValueOnce(undefined);

        const result = await controller.createInstructionInspiration(
          mockRequest,
          dtoWithGroup,
        );

        expect(
          mockInspirationsService.createInstructionInspiration,
        ).toHaveBeenCalledWith(
          dtoWithGroup.data,
          mockRequest.user.sub,
          groupId,
        );
        expect(mockFgaService.checkUserAuthorization).toHaveBeenCalledWith(
          expect.objectContaining({
            user_type: FGAType.USER,
            user_id: mockRequest.user.sub,
            relation: FGARelation.GROUP_INSPIRATION_PUBLISHER,
            object_type: FGAType.GROUP,
            object_id: groupId,
          }),
        );
        expect(mockFgaService.writeTuples).toHaveBeenCalledWith(
          expect.arrayContaining([
            expect.objectContaining({
              user_type: FGAType.USER,
              user_id: mockRequest.user.sub,
              relation: FGARelation.INSPIRATION_CREATOR,
              object_type: FGAType.INSPIRATION,
              object_id: mockInstructionInspiration.id,
            }),
            expect.objectContaining({
              user_type: FGAType.WORKSPACE,
              user_id: 'test-workspace-id',
              relation: FGARelation.WORKSPACE,
              object_type: FGAType.INSPIRATION,
              object_id: mockInstructionInspiration.id,
            }),
            expect.objectContaining({
              user_type: FGAType.GROUP,
              user_id: groupId,
              relation: FGARelation.SHARED_GROUP,
              object_type: FGAType.INSPIRATION,
              object_id: mockInstructionInspiration.id,
            }),
          ]),
        );
        // Also check that the array has the correct number of tuples
        const writtenTuplesCallInstr =
          mockFgaService.writeTuples.mock.calls[0][0];
        expect(writtenTuplesCallInstr).toHaveLength(3);

        expect(result.data).toEqual(mockInstructionInspiration);
      });

      it('should throw ForbiddenException if not authorized to create (private)', async () => {
        mockFgaService.checkUserAuthorizationOnWorkspace.mockResolvedValueOnce(
          false,
        );
        await expect(
          controller.createInstructionInspiration(
            mockRequest,
            createInstructionDto,
          ),
        ).rejects.toThrow(ForbiddenException);
      });
    });

    describe('updateInstructionInspiration', () => {
      beforeEach(() => {
        mockRequest.params.id = instructionId;
        // Reset getInspiration mock to prevent state bleeding
        mockInspirationsService.getInspiration.mockReset();
      });
      it('should update INSTRUCTION inspiration', async () => {
        const mockInspiration = {
          id: instructionId,
          type: InspirationType.INSTRUCTION,
          user_id: mockRequest.user.sub,
          name: 'Test Instruction',
          tags: [],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: { token_count: 0, is_indexed: false },
          status: InspirationStatus.ENABLED,
        } as Inspiration;

        (getVisibleResources as jest.Mock).mockReturnValue([]);
        mockInspirationsService.getInspiration.mockResolvedValueOnce(
          mockInspiration,
        );
        mockInspirationsService.updateInstructionInspiration.mockResolvedValueOnce(
          mockInstructionInspiration,
        );

        const result = await controller.updateInstructionInspiration(
          mockRequest,
          instructionId,
          updateInstructionDto,
        );

        expect(getVisibleResources).toHaveBeenCalledWith(
          mockRequest,
          FGAType.GROUP,
          FGARelation.GROUP_INSPIRATION_VIEWER,
        );
        expect(mockInspirationsService.getInspiration).toHaveBeenCalledWith(
          instructionId,
          mockRequest.user.sub,
          [],
        );
        expect(
          mockInspirationsService.updateInstructionInspiration,
        ).toHaveBeenCalledWith(mockInspiration, updateInstructionDto.data);
        expect(result.data).toEqual(mockInstructionInspiration);
      });
      // Add NotFound similar to URL update
      it('should re-throw NotFound from service on update', async () => {
        mockInspirationsService.updateInstructionInspiration.mockRejectedValueOnce(
          new NotFoundException(),
        );
        await expect(
          controller.updateInstructionInspiration(
            mockRequest,
            instructionId,
            updateInstructionDto,
          ),
        ).rejects.toThrow(NotFoundException);
      });
    });
  });

  // --- FILE Inspirations ---
  describe('FILE Inspirations', () => {
    const fileId = 'fileInspiId1';
    const mockProcessedFile: ProcessedFile = {
      buffer: Buffer.from('file content'),
      originalname: 'test.pdf',
      mimetype: 'application/pdf',
      size: 1024,
    };
    const mockMulterFile = { ...mockProcessedFile } as Express.Multer.File; // Close enough for controller test
    const mockFileKey =
      'inspirations/files/internalUser1/generated_file_key.pdf';
    const mockFileInspiration = {
      id: fileId,
      name: 'File Inspi',
      type: InspirationType.FILE,
    } as Inspiration;

    describe('uploadFileInspiration', () => {
      const metadataString = JSON.stringify({
        name: 'My Uploaded File',
        tags: ['upload'],
      });
      const sharedGroupId = 'groupForFileUpload';

      beforeEach(() => {
        mockUserGroupsService.getInternalUser.mockResolvedValue({
          id: 'internalUser1',
        });
        mockFgaService.checkUserAuthorizationOnWorkspace.mockResolvedValue(
          true,
        ); // Assume authorized for creator
        mockFgaService.checkUserAuthorization.mockResolvedValue(true); // Assume authorized for group publisher if group provided
        mockFileService.processFile.mockResolvedValue(mockProcessedFile);
        mockFileService.generateFileKey.mockReturnValue(mockFileKey);
        mockInspirationsService.createFileInspiration.mockResolvedValue(
          mockFileInspiration,
        );
        mockFgaService.writeTuples.mockResolvedValue(undefined);
      });

      it('should upload file and create FILE inspiration, sharing with group if sharedGroupId is provided and authorized', async () => {
        const result = await controller.uploadFileInspiration(
          mockRequest,
          mockMulterFile,
          metadataString,
          sharedGroupId, // sharedGroupId is provided
        );

        expect(mockUserGroupsService.getInternalUser).toHaveBeenCalledWith(
          mockRequest.headers.authorization,
        );
        expect(
          mockFgaService.checkUserAuthorizationOnWorkspace,
        ).toHaveBeenCalled(); // For private creator
        expect(mockFgaService.checkUserAuthorization).toHaveBeenCalledWith(
          expect.objectContaining({
            user_type: FGAType.USER,
            user_id: mockRequest.user.sub,
            relation: FGARelation.GROUP_INSPIRATION_PUBLISHER,
            object_type: FGAType.GROUP,
            object_id: sharedGroupId,
          }),
        ); // For group publisher
        expect(mockFileService.processFile).toHaveBeenCalledWith(
          mockMulterFile,
        );
        expect(mockFileService.generateFileKey).toHaveBeenCalledWith(
          'internalUser1',
          expect.any(String),
          mockMulterFile.originalname,
        );
        expect(
          mockInspirationsService.createFileInspiration,
        ).toHaveBeenCalledWith(
          mockProcessedFile,
          mockFileKey,
          JSON.parse(metadataString),
          mockRequest.user.sub,
          sharedGroupId, // sharedGroupId is passed
        );
        expect(mockFgaService.writeTuples).toHaveBeenCalledWith(
          expect.arrayContaining([
            expect.objectContaining({
              user_type: FGAType.USER,
              user_id: mockRequest.user.sub,
              relation: FGARelation.INSPIRATION_CREATOR,
              object_type: FGAType.INSPIRATION,
              object_id: mockFileInspiration.id,
            }),
            expect.objectContaining({
              user_type: FGAType.WORKSPACE,
              user_id: 'test-workspace-id',
              relation: FGARelation.WORKSPACE,
              object_type: FGAType.INSPIRATION,
              object_id: mockFileInspiration.id,
            }),
            expect.objectContaining({
              user_type: FGAType.GROUP,
              user_id: sharedGroupId,
              relation: FGARelation.SHARED_GROUP,
              object_type: FGAType.INSPIRATION,
              object_id: mockFileInspiration.id,
            }),
          ]),
        );
        const writtenTuplesCallFile =
          mockFgaService.writeTuples.mock.calls[0][0];
        expect(writtenTuplesCallFile).toHaveLength(3);
        expect(result.data).toEqual(mockFileInspiration);
      });

      it('should upload file and create FILE inspiration if authorized (no sharedGroupId)', async () => {
        // Reset specific mocks if they were set to expect sharedGroupId in beforeEach or previous tests
        mockFgaService.checkUserAuthorization.mockReset();

        const result = await controller.uploadFileInspiration(
          mockRequest,
          mockMulterFile,
          metadataString,
          undefined, // No sharedGroupId provided
        );

        expect(mockUserGroupsService.getInternalUser).toHaveBeenCalled();
        expect(
          mockFgaService.checkUserAuthorizationOnWorkspace,
        ).toHaveBeenCalled();
        // Ensure group publisher check is NOT called when no sharedGroupId
        expect(mockFgaService.checkUserAuthorization).not.toHaveBeenCalledWith(
          expect.objectContaining({
            relation: FGARelation.GROUP_INSPIRATION_PUBLISHER,
          }),
        );

        expect(
          mockInspirationsService.createFileInspiration,
        ).toHaveBeenCalledWith(
          mockProcessedFile,
          mockFileKey,
          JSON.parse(metadataString),
          mockRequest.user.sub,
          undefined, // No sharedGroupId for this test case
        );
        // Expect creator and workspace tuples
        const writtenTuplesMissingMeta =
          mockFgaService.writeTuples.mock.calls[0][0];
        expect(writtenTuplesMissingMeta).toHaveLength(2); // Corrected expected length
        expect(writtenTuplesMissingMeta).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              user_type: FGAType.USER,
              user_id: mockRequest.user.sub,
              relation: FGARelation.INSPIRATION_CREATOR,
              object_type: FGAType.INSPIRATION,
              object_id: mockFileInspiration.id,
            }),
            expect.objectContaining({
              user_type: FGAType.WORKSPACE,
              user_id: 'test-workspace-id',
              relation: FGARelation.WORKSPACE,
              object_type: FGAType.INSPIRATION,
              object_id: mockFileInspiration.id,
            }),
          ]),
        );
      });

      it('should handle missing metadata string', async () => {
        await controller.uploadFileInspiration(
          mockRequest,
          mockMulterFile,
          undefined as any,
          undefined,
        );
        expect(
          mockInspirationsService.createFileInspiration,
        ).toHaveBeenCalledWith(
          mockProcessedFile,
          mockFileKey,
          undefined, // Metadata should be undefined
          mockRequest.user.sub,
          undefined,
        );
      });

      it('should throw BadRequestException for invalid metadata string', async () => {
        await expect(
          controller.uploadFileInspiration(
            mockRequest,
            mockMulterFile,
            'invalid-json',
            undefined,
          ),
        ).rejects.toThrow(BadRequestException);
      });

      it('should throw BadRequestException if no file provided', async () => {
        await expect(
          controller.uploadFileInspiration(
            mockRequest,
            undefined as any,
            metadataString,
            undefined,
          ),
        ).rejects.toThrow(BadRequestException);
      });

      it('should throw ForbiddenException if not authorized', async () => {
        mockFgaService.checkUserAuthorizationOnWorkspace.mockResolvedValueOnce(
          false,
        );
        await expect(
          controller.uploadFileInspiration(
            mockRequest,
            mockMulterFile,
            metadataString,
            undefined,
          ),
        ).rejects.toThrow(ForbiddenException);
      });
    });

    describe('updateFileInspiration', () => {
      const updateFileDto: UpdateFileInspirationDto = {
        data: { name: 'Updated File Doc', tags: ['docs'] },
      };
      beforeEach(() => {
        mockRequest.params.id = fileId;
        // Reset getInspiration mock to prevent state bleeding
        mockInspirationsService.getInspiration.mockReset();
      });

      it('should update FILE inspiration metadata', async () => {
        const mockInspiration = {
          id: fileId,
          type: InspirationType.FILE,
          user_id: mockRequest.user.sub,
          name: 'Test File',
          tags: [],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          metadata: { token_count: 0, is_indexed: false },
          status: InspirationStatus.ENABLED,
        } as Inspiration;

        (getVisibleResources as jest.Mock).mockReturnValue([]);
        mockInspirationsService.getInspiration.mockResolvedValueOnce(
          mockInspiration,
        );
        mockInspirationsService.updateFileInspiration.mockResolvedValueOnce(
          mockFileInspiration,
        );

        const result = await controller.updateFileInspiration(
          mockRequest,
          fileId,
          updateFileDto,
        );

        expect(getVisibleResources).toHaveBeenCalledWith(
          mockRequest,
          FGAType.GROUP,
          FGARelation.GROUP_INSPIRATION_VIEWER,
        );
        expect(mockInspirationsService.getInspiration).toHaveBeenCalledWith(
          fileId,
          mockRequest.user.sub,
          [],
        );
        expect(
          mockInspirationsService.updateFileInspiration,
        ).toHaveBeenCalledWith(mockInspiration, updateFileDto.data);
        expect(result.data).toEqual(mockFileInspiration);
      });

      it('should re-throw errors from service during file update', async () => {
        mockInspirationsService.updateFileInspiration.mockRejectedValueOnce(
          new NotFoundException(),
        );
        await expect(
          controller.updateFileInspiration(mockRequest, fileId, updateFileDto),
        ).rejects.toThrow(NotFoundException);
      });
    });
  });

  // --- Feature/Unfeature Inspiration Endpoints ---
  describe('featureInspiration', () => {
    const inspirationId = 'feature-inspi-id';
    const mockImageFile = {
      buffer: Buffer.from('image data'),
    } as Express.Multer.File;
    const mockProcessedImageBuffer = Buffer.from('processed image data');
    const mockImageUrl = 'https://s3.example.com/image.jpg';
    let mockExistingInspiration: Inspiration;
    const mockFeatureBody: FeatureInspirationBodyDto = {
      description: 'Featured Desc',
      source: 'Featured Source',
    };
    const mockGeneratedUuid = 'mocked-uuid-for-image'; // Define a mock UUID

    beforeEach(() => {
      mockRequest.params.id = inspirationId;
      mockRequest.user = { sub: 'test-user-external-id' };

      mockExistingInspiration = {
        id: inspirationId,
        type: InspirationType.TEXT,
        name: 'Test Inspiration',
        status: InspirationStatus.ENABLED,
        user_id: 'test-user-external-id',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        shared_group_id: 'group123', // Important for feature eligibility
        is_featured: false,
        image_url: null,
        tags: [],
        metadata: {},
      };

      mockInspirationsService.getInspiration.mockResolvedValue(
        mockExistingInspiration,
      );
      mockImageService.processImage.mockResolvedValue(mockProcessedImageBuffer);
      mockImageService.uploadToS3.mockResolvedValue(mockImageUrl);
      mockInspirationsService.featureInspiration.mockResolvedValue(undefined);
      (getVisibleResources as jest.Mock).mockReset();
      (getVisibleResources as jest.Mock).mockReturnValue([]);

      // Mock uuidv4 before each test in this suite
      (require('uuid').v4 as jest.Mock).mockReturnValue(mockGeneratedUuid);
    });

    it('should successfully feature an inspiration', async () => {
      await controller.featureInspiration(
        mockRequest,
        inspirationId,
        mockImageFile,
        mockFeatureBody,
      );

      expect(getVisibleResources).toHaveBeenCalledWith(
        mockRequest,
        FGAType.GROUP,
        FGARelation.GROUP_INSPIRATION_VIEWER,
      );
      expect(mockInspirationsService.getInspiration).toHaveBeenCalledWith(
        inspirationId,
        mockRequest.user.sub,
        [], // Expecting the mocked value from getVisibleResources
      );
      expect(mockImageService.processImage).toHaveBeenCalledWith(mockImageFile);
      expect(mockImageService.uploadToS3).toHaveBeenCalledWith(
        mockProcessedImageBuffer,
        expect.any(String), // S3_BUCKET.PUBLIC_BUCKET - actual value depends on ImageService config
        `community/inspirations/${inspirationId}/images`,
        mockGeneratedUuid, // Use the mocked UUID here
      );
      expect(mockInspirationsService.featureInspiration).toHaveBeenCalledWith(
        mockExistingInspiration,
        mockImageUrl,
        mockFeatureBody.description,
        mockFeatureBody.source,
      );
    });

    it('should successfully feature an inspiration when body is empty', async () => {
      const emptyBody = {};
      await controller.featureInspiration(
        mockRequest,
        inspirationId,
        mockImageFile,
        emptyBody as FeatureInspirationBodyDto,
      );

      expect(mockInspirationsService.featureInspiration).toHaveBeenCalledWith(
        mockExistingInspiration,
        mockImageUrl,
        undefined, // description should be undefined
        undefined, // source should be undefined
      );
    });

    it('should throw NotFoundException if inspiration is not found', async () => {
      mockInspirationsService.getInspiration.mockResolvedValue(null);
      await expect(
        controller.featureInspiration(
          mockRequest,
          inspirationId,
          mockImageFile,
          mockFeatureBody,
        ),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException if inspiration is not shared to a group', async () => {
      mockInspirationsService.getInspiration.mockResolvedValue({
        ...mockExistingInspiration,
        shared_group_id: null,
      });
      await expect(
        controller.featureInspiration(
          mockRequest,
          inspirationId,
          mockImageFile,
          mockFeatureBody,
        ),
      ).rejects.toThrow(
        new BadRequestException(
          'Featured inspirations must be already shared to a group',
        ),
      );
    });

    it('should throw BadRequestException if no image is provided', async () => {
      await expect(
        controller.featureInspiration(
          mockRequest,
          inspirationId,
          null as any,
          mockFeatureBody,
        ),
      ).rejects.toThrow(
        new BadRequestException('Image is required to feature an inspiration.'),
      );
    });

    it('should propagate error from imageService.processImage', async () => {
      const processError = new Error('Processing failed');
      mockImageService.processImage.mockRejectedValue(processError);
      await expect(
        controller.featureInspiration(
          mockRequest,
          inspirationId,
          mockImageFile,
          mockFeatureBody,
        ),
      ).rejects.toThrow(processError);
    });

    it('should propagate error from imageService.uploadToS3', async () => {
      const uploadError = new Error('S3 upload failed');
      mockImageService.uploadToS3.mockRejectedValue(uploadError);
      await expect(
        controller.featureInspiration(
          mockRequest,
          inspirationId,
          mockImageFile,
          mockFeatureBody,
        ),
      ).rejects.toThrow(uploadError);
    });

    it('should propagate error from inspirationsService.featureInspiration', async () => {
      const featureError = new Error('Feature service failed');
      mockInspirationsService.featureInspiration.mockRejectedValue(
        featureError,
      );
      await expect(
        controller.featureInspiration(
          mockRequest,
          inspirationId,
          mockImageFile,
          mockFeatureBody,
        ),
      ).rejects.toThrow(featureError);
    });
  });

  describe('unfeatureInspiration', () => {
    const inspirationId = 'unfeature-inspi-id';
    let mockFeaturedInspiration: Inspiration;

    beforeEach(() => {
      mockRequest.params.id = inspirationId;
      mockRequest.user = { sub: 'test-user-external-id' };

      mockFeaturedInspiration = {
        id: inspirationId,
        type: InspirationType.TEXT,
        name: 'Featured Inspiration',
        status: InspirationStatus.ENABLED,
        user_id: 'test-user-external-id',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        shared_group_id: 'group123',
        is_featured: true,
        image_url: 'https://s3.example.com/featured_image.jpg',
        tags: [],
        metadata: {},
      };

      mockInspirationsService.getInspiration.mockResolvedValue(
        mockFeaturedInspiration,
      );
      mockInspirationsService.unfeatureInspiration.mockResolvedValue(undefined);
      mockImageService.deleteFromS3.mockResolvedValue(undefined);
      (getVisibleResources as jest.Mock).mockReset();
      (getVisibleResources as jest.Mock).mockReturnValue([]);
    });

    it('should successfully unfeature an inspiration with an image_url', async () => {
      await controller.unfeatureInspiration(mockRequest, inspirationId);

      expect(getVisibleResources).toHaveBeenCalledWith(
        mockRequest,
        FGAType.GROUP,
        FGARelation.GROUP_INSPIRATION_VIEWER,
      );
      expect(mockInspirationsService.getInspiration).toHaveBeenCalledWith(
        inspirationId,
        mockRequest.user.sub,
        [], // Expecting the mocked value from getVisibleResources
      );
      expect(mockInspirationsService.unfeatureInspiration).toHaveBeenCalledWith(
        mockFeaturedInspiration,
      );
      expect(mockImageService.deleteFromS3).toHaveBeenCalledWith(
        mockFeaturedInspiration.image_url,
      );
    });

    it('should successfully unfeature an inspiration without an image_url and not call deleteFromS3', async () => {
      mockInspirationsService.getInspiration.mockResolvedValue({
        ...mockFeaturedInspiration,
        image_url: null,
      });
      await controller.unfeatureInspiration(mockRequest, inspirationId);

      expect(getVisibleResources).toHaveBeenCalledWith(
        mockRequest,
        FGAType.GROUP,
        FGARelation.GROUP_INSPIRATION_VIEWER,
      );
      expect(mockInspirationsService.getInspiration).toHaveBeenCalledWith(
        inspirationId,
        mockRequest.user.sub,
        [], // Expecting the mocked value from getVisibleResources
      );
      expect(mockInspirationsService.unfeatureInspiration).toHaveBeenCalledWith(
        { ...mockFeaturedInspiration, image_url: null },
      );
      expect(mockImageService.deleteFromS3).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException if inspiration is not found', async () => {
      mockInspirationsService.getInspiration.mockResolvedValue(null);
      await expect(
        controller.unfeatureInspiration(mockRequest, inspirationId),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException if inspiration is not currently featured', async () => {
      mockInspirationsService.getInspiration.mockResolvedValue({
        ...mockFeaturedInspiration,
        is_featured: false,
      });
      await expect(
        controller.unfeatureInspiration(mockRequest, inspirationId),
      ).rejects.toThrow(
        new BadRequestException('Inspiration must be featured to unfeature it'),
      );
    });

    it('should propagate error from inspirationsService.unfeatureInspiration and not call deleteFromS3', async () => {
      const unfeatureError = new Error('Unfeature service failed');
      mockInspirationsService.unfeatureInspiration.mockRejectedValue(
        unfeatureError,
      );

      await expect(
        controller.unfeatureInspiration(mockRequest, inspirationId),
      ).rejects.toThrow(unfeatureError);
      expect(mockImageService.deleteFromS3).not.toHaveBeenCalled();
    });

    it('should still complete unfeature successfully if imageService.deleteFromS3 ', async () => {
      const deleteError = new Error('S3 delete failed');
      mockImageService.deleteFromS3.mockRejectedValue(deleteError);
      // Spy on logger.error for the controller instance to check if it's called (optional, advanced)
      // const loggerErrorSpy = jest.spyOn((controller as any).logger, 'error');

      await controller.unfeatureInspiration(mockRequest, inspirationId);

      expect(mockInspirationsService.unfeatureInspiration).toHaveBeenCalledWith(
        mockFeaturedInspiration,
      );
      // deleteFromS3 is called before the error is thrown by it, so this assertion is valid.
      expect(mockImageService.deleteFromS3).toHaveBeenCalledWith(
        mockFeaturedInspiration.image_url,
      );
    });
  });

  // --- getFileDownloadUrl --- // This section was mistakenly removed by the previous edit, restoring it.
  describe('getFileDownloadUrl', () => {
    const fileId = 'file-inspi-123';
    const mockFileInspirationForDownload = {
      id: fileId,
      type: InspirationType.FILE,
      file_metadata: {
        s3_path: 's3://test-inspirations-bucket/path/to/file.pdf',
        original_file_name: 'original-file.pdf',
      },
      // Ensure all required Inspiration model fields are present
      name: 'Downloadable File',
      status: InspirationStatus.ENABLED,
      user_id: 'test-user-external-id',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      tags: [],
      metadata: { is_indexed: false, token_count: 0 },
    } as Inspiration;

    let originalConfigGet: any;

    beforeEach(() => {
      mockRequest.params.id = fileId;
      mockInspirationsService.getInspiration.mockResolvedValue(
        mockFileInspirationForDownload,
      );

      originalConfigGet = mockConfigService.get;
      mockConfigService.get = jest.fn((key: string) => {
        if (key === 'INSPIRATIONS_DOWNLOAD_EXPIRY_SECONDS') return '3600';
        if (key === 's3.inspirationsBucket') return 'test-inspirations-bucket'; // Ensure this is mocked if s3Service relies on it via controller
        return originalConfigGet(key); // Fallback to original mock for other keys
      });

      // Ensure s3Service.getSignedUrl is a mock function for this describe block
      // If it was replaced or altered in other tests, reset it here.
      mockS3Service.getSignedUrl.mockReset(); // Resetting to clear previous state/implementations
      mockS3Service.getSignedUrl.mockResolvedValue(
        'https://test-presigned-url.com/file.pdf',
      );
    });

    afterEach(() => {
      mockConfigService.get = originalConfigGet; // Restore original mockConfigService.get
    });

    it('should successfully generate a download URL with correct expiry time', async () => {
      const result = await controller.getFileDownloadUrl(mockRequest, fileId);

      expect(mockInspirationsService.getInspiration).toHaveBeenCalledWith(
        fileId,
        mockRequest.user.sub,
      );
      expect(mockConfigService.get).toHaveBeenCalledWith(
        'INSPIRATIONS_DOWNLOAD_EXPIRY_SECONDS',
      );
      expect(mockS3Service.getSignedUrl).toHaveBeenCalledWith(
        'test-inspirations-bucket', // Bucket from s3_path
        'path/to/file.pdf', // Key from s3_path
        S3SignedUrlOperation.GET,
        3600, // Expiry from config
      );
      expect(result.data?.url).toBe('https://test-presigned-url.com/file.pdf');
      expect(result.data?.filename).toBe(
        mockFileInspirationForDownload.file_metadata?.original_file_name,
      );
    });

    it('should throw BadRequestException when inspiration is not a file', async () => {
      const nonFileInspiration = {
        ...mockFileInspirationForDownload,
        type: InspirationType.TEXT,
      };
      mockInspirationsService.getInspiration.mockResolvedValueOnce(
        nonFileInspiration as Inspiration,
      );

      await expect(
        controller.getFileDownloadUrl(mockRequest, fileId),
      ).rejects.toThrow(BadRequestException);
      expect(mockS3Service.getSignedUrl).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException when file_metadata.s3_path is not found', async () => {
      const inspirationWithoutPath = {
        ...mockFileInspirationForDownload,
        file_metadata: { original_file_name: 'original-file.pdf' }, // s3_path is missing
      };
      mockInspirationsService.getInspiration.mockResolvedValueOnce(
        inspirationWithoutPath as Inspiration,
      );

      await expect(
        controller.getFileDownloadUrl(mockRequest, fileId),
      ).rejects.toThrow(new BadRequestException('File path not found'));
    });

    it('should throw BadRequestException when S3 path format is invalid', async () => {
      const inspirationWithInvalidPath = {
        ...mockFileInspirationForDownload,
        file_metadata: {
          ...mockFileInspirationForDownload.file_metadata,
          s3_path: 'invalid-s3-path',
        },
      };
      mockInspirationsService.getInspiration.mockResolvedValueOnce(
        inspirationWithInvalidPath as Inspiration,
      );

      await expect(
        controller.getFileDownloadUrl(mockRequest, fileId),
      ).rejects.toThrow(new BadRequestException('Invalid S3 path format'));
    });

    it('should throw an error when INSPIRATIONS_DOWNLOAD_EXPIRY_SECONDS is not configured', async () => {
      mockConfigService.get = jest.fn((key: string) => {
        if (key === 'INSPIRATIONS_DOWNLOAD_EXPIRY_SECONDS') return undefined; // Simulate not configured
        return originalConfigGet(key);
      });

      await expect(
        controller.getFileDownloadUrl(mockRequest, fileId),
      ).rejects.toThrow(
        'INSPIRATIONS_DOWNLOAD_EXPIRY_SECONDS is not configured',
      );
      expect(mockInspirationsService.getInspiration).not.toHaveBeenCalled(); // Should fail before this call
    });

    it('should re-throw errors from inspirationsService.getInspiration', async () => {
      const serviceError = new InternalServerErrorException('Service down');
      mockInspirationsService.getInspiration.mockRejectedValueOnce(
        serviceError,
      );

      await expect(
        controller.getFileDownloadUrl(mockRequest, fileId),
      ).rejects.toThrow(serviceError);
    });

    it('should re-throw errors from s3Service.getSignedUrl', async () => {
      const s3Error = new Error('S3 is down');
      mockS3Service.getSignedUrl.mockRejectedValueOnce(s3Error);

      await expect(
        controller.getFileDownloadUrl(mockRequest, fileId),
      ).rejects.toThrow(s3Error);
    });
  });
});
