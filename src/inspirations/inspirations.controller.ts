import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  Query,
  HttpStatus,
  Logger,
  HttpCode,
  Req,
  ForbiddenException,
  NotFoundException,
  InternalServerErrorException,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import { InspirationsService } from './inspirations.service';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiConsumes,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ListInspirationsQueryDto } from './dto/common/list-inspirations.dto';
import { UpdateStatusDto } from './dto/common/update-status.dto';
import {
  CreateUrlInspirationDto,
  UpdateUrlInspirationDto,
} from './dto/url/url-inspiration.dto';
import {
  CreateTextInspirationDto,
  UpdateTextInspirationDto,
} from './dto/text/text-inspiration.dto';
import {
  CreateInstructionInspirationDto,
  UpdateInstructionInspirationDto,
} from './dto/instruction/instruction-inspiration.dto';
import { BaseResponseDto, createResponse } from './dto/common/response.dto';
import { Inspiration } from './models/inspiration.model';
import { FgaService, FGATuple } from '../auth/fga/fga.service';
import { FGAType, FGARelation } from '../auth/fga/fga.enums';
import { ConfigService } from '@nestjs/config';
import { RequireFgaPermission } from '../auth/decorators/require-fga-permission.decorator';
import { setConfigService, getWorkspaceId } from '../common/config-helpers';
import { FileInterceptor } from '@nestjs/platform-express';
import { FileService } from './file/file.service';
import { UpdateFileInspirationDto } from './dto/file/file-inspiration.dto';
import 'multer';
import {
  PaginatedApiResponseDto,
  createPaginatedApiResponse,
} from '../common/dto/pagination.dto';
import { ALLOWED_FILE_EXTENSIONS } from '../common/constants/file-types';
import { UserGroupsService } from '../auth/services/user-groups.service';
import { HashUtils } from './utils/hash.utils';
import { InspirationType } from './models/inspiration.model';
import { Auth0Guard } from '../auth/guards/auth0.guard';
import {
  WithVisibleResources,
  getVisibleResources,
} from '../auth/decorators/with-visible-resources.decorator';
import { S3Service } from '../s3/s3.service';
import { S3SignedUrlOperation } from '../s3/s3.types';
import { ImageService, S3_BUCKET } from '../common/services/image.service';
import * as Sentry from '@sentry/node';
import { FeatureInspirationBodyDto } from './dto/feature/feature-inspiration-body.dto';
import { ShareInspirationBodyDto } from './dto/share/share-inspiration-body.dto';
import { v4 as uuidv4 } from 'uuid';

/**
 * Controller for Inspirations Management
 */
@ApiTags('Inspirations')
@ApiBearerAuth()
@Controller('api/v1/inspirations')
export class InspirationsController {
  private readonly logger = new Logger(InspirationsController.name);

  constructor(
    private readonly inspirationsService: InspirationsService,
    private readonly fgaService: FgaService,
    private readonly configService: ConfigService,
    private readonly fileService: FileService,
    private readonly userService: UserGroupsService,
    private readonly s3Service: S3Service,
    private readonly imageService: ImageService,
  ) {
    setConfigService(configService);
  }

  onModuleInit() {
    setConfigService(this.configService);
  }

  /* Common Endpoints */

  /**
   * List inspirations accessible to the requesting user
   */
  @ApiOperation({ summary: 'List inspirations' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of inspirations returned successfully',
    type: () => PaginatedApiResponseDto,
  })
  @Get()
  @UseGuards(Auth0Guard)
  async listInspirations(
    @Req() req: any,
    @Query() query: ListInspirationsQueryDto,
  ): Promise<PaginatedApiResponseDto<Inspiration>> {
    const externalUserId = this.getExternalUserIdFromRequest(req);

    const { items, total, currentPage, pageSize } =
      await this.inspirationsService.listInspirations(query, externalUserId);

    return createPaginatedApiResponse(items, total, currentPage, pageSize);
  }

  /**
   * List inspirations accessible to the requesting user
   */
  @ApiOperation({ summary: 'List inspirations shared to groups I can view' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of inspirations returned successfully',
    type: () => PaginatedApiResponseDto,
  })
  @Get('group-shared')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,
    (req) => getWorkspaceId(),
  )
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_INSPIRATION_VIEWER)
  async listInspirationsSharedToGroups(
    @Req() req: any,
    @Query() query: ListInspirationsQueryDto,
  ): Promise<PaginatedApiResponseDto<Inspiration>> {
    const visibleGroupIds = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_INSPIRATION_VIEWER,
    );

    const { items, total, currentPage, pageSize } =
      await this.inspirationsService.listInspirationsForGroups(
        query,
        visibleGroupIds,
      );

    return createPaginatedApiResponse(items, total, currentPage, pageSize);
  }

  /* MITRE Inspirations */

  /**
   * List MITRE inspirations accessible to the requesting user
   */
  @ApiOperation({ summary: 'List MITRE inspirations' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of MITRE inspirations returned successfully',
    type: () => PaginatedApiResponseDto,
  })
  @Get('mitre')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,
    (req) => getWorkspaceId(),
  )
  async listMitreInspirations(
    @Req() req: any,
    @Query() query: ListInspirationsQueryDto,
  ): Promise<PaginatedApiResponseDto<Inspiration>> {
    const externalUserId = this.getExternalUserIdFromRequest(req);

    const { items, total, currentPage, pageSize } =
      await this.inspirationsService.listMitreInspirations(
        query,
        externalUserId,
      );

    return createPaginatedApiResponse(items, total, currentPage, pageSize);
  }

  /**
   * Find inspirations by hash
   */
  @ApiOperation({ summary: 'Find inspirations by hash' })
  @ApiParam({ name: 'hash', description: 'SHA256 hash to search for' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of matching inspirations returned successfully',
    type: () => BaseResponseDto,
  })
  @Get('hash/:hash')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,
    (req) => getWorkspaceId(),
  )
  async getInspirationsByHash(
    @Req() req: any,
    @Param('hash') hash: string,
  ): Promise<BaseResponseDto<Inspiration[]>> {
    try {
      const externalUserId = this.getExternalUserIdFromRequest(req);
      this.logger.log('Finding inspirations by hash', {
        hash: hash.substring(0, 10) + '...',
        externalUserId,
      });

      const inspirations = await this.inspirationsService.getInspirationsByHash(
        hash,
        externalUserId,
      );

      this.logger.log('Found inspirations by hash', {
        hash: hash.substring(0, 10) + '...',
        count: inspirations.length,
      });

      // Filter inspirations based on user permissions
      const authorizedInspirations = await this.filterAuthorizedInspirations(
        inspirations,
        externalUserId,
      );

      return createResponse(authorizedInspirations);
    } catch (error) {
      this.safelyLogError('Error finding inspirations by hash', error);
      throw error;
    }
  }

  /**
   * Filter inspirations based on user authorization
   * @private
   */
  private async filterAuthorizedInspirations(
    inspirations: Inspiration[],
    externalUserId: string,
  ): Promise<Inspiration[]> {
    const authorizedInspirations: Inspiration[] = [];

    for (const inspiration of inspirations) {
      try {
        // Check if user has permission to view this inspiration
        const canView = await this.fgaService.checkUserAuthorization({
          user_type: FGAType.USER,
          user_id: externalUserId,
          relation: FGARelation.INSPIRATION_VIEWER,
          object_type: FGAType.INSPIRATION,
          object_id: inspiration.id,
        });

        if (canView) {
          authorizedInspirations.push(inspiration);
        }
      } catch (error) {
        // If permission check fails, log and skip this inspiration
        this.safelyLogError(
          `Failed to check permission for inspiration ${inspiration.id}`,
          error,
        );
        // Skip this inspiration (don't add it)
      }
    }

    if (authorizedInspirations.length !== inspirations.length) {
      this.logger.log('Filtered inspirations by permission', {
        total: inspirations.length,
        authorized: authorizedInspirations.length,
      });
    }

    return authorizedInspirations;
  }

  /**
   * Retrieve details of a specific inspiration
   */
  @ApiOperation({ summary: 'Get inspiration by ID' })
  @ApiParam({ name: 'id', description: 'Inspiration ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Inspiration details returned successfully',
    type: () => BaseResponseDto,
  })
  @Get(':id')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.INSPIRATION_VIEWER,
    FGAType.INSPIRATION,
    (req) => req.params.id,
  )
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_INSPIRATION_VIEWER)
  async getInspiration(
    @Req() req: any,
    @Param('id') id: string,
  ): Promise<BaseResponseDto<Inspiration>> {
    const externalUserId = this.getExternalUserIdFromRequest(req);
    const visibleGroupIds = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_INSPIRATION_VIEWER,
    );

    const inspiration = await this.inspirationsService.getInspiration(
      id,
      externalUserId,
      visibleGroupIds,
    );

    return createResponse(inspiration);
  }

  /**
   * Delete an inspiration
   */
  @ApiOperation({ summary: 'Delete inspiration' })
  @ApiParam({ name: 'id', description: 'Inspiration ID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Inspiration deleted successfully',
  })
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.INSPIRATION_EDITOR,
    FGAType.INSPIRATION,
    (req) => req.params.id,
  )
  async deleteInspiration(
    @Req() req: any,
    @Param('id') id: string,
  ): Promise<void> {
    const externalUserId = this.getExternalUserIdFromRequest(req);

    // Delete the inspiration
    const result = await this.inspirationsService.deleteInspiration(
      id,
      externalUserId,
    );

    if (result) {
      // Delete FGA tuples
      await this.deleteInspirationTuples(
        id,
        externalUserId,
        result.shared_group_id,
      );
    }
  }

  /**
   * Update the status of an inspiration
   */
  @ApiOperation({ summary: 'Update inspiration status' })
  @ApiParam({ name: 'id', description: 'Inspiration ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Inspiration status updated successfully',
    type: () => BaseResponseDto,
  })
  @Put(':id/status')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.INSPIRATION_EDITOR,
    FGAType.INSPIRATION,
    (req) => req.params.id,
  )
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_INSPIRATION_VIEWER)
  async updateStatus(
    @Req() req: any,
    @Param('id') id: string,
    @Body() updateStatusDto: UpdateStatusDto,
  ): Promise<BaseResponseDto<Inspiration>> {
    try {
      const externalUserId = this.getExternalUserIdFromRequest(req);
      const visibleGroupIds = getVisibleResources(
        req,
        FGAType.GROUP,
        FGARelation.GROUP_INSPIRATION_VIEWER,
      );

      const inspiration = await this.inspirationsService.getInspiration(
        id,
        externalUserId,
        visibleGroupIds,
      );

      const updatedInspiration =
        await this.inspirationsService.updateInspirationStatus(
          inspiration,
          updateStatusDto.data.status,
        );

      return createResponse(updatedInspiration);
    } catch (error) {
      this.safelyLogError('Error updating inspiration status', error);
      throw error;
    }
  }

  /**
   * Share an inspiration with a group
   */
  @ApiOperation({ summary: 'Share inspiration with a group' })
  @ApiParam({ name: 'id', description: 'Inspiration ID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Inspiration shared successfully',
  })
  @ApiBody({ type: ShareInspirationBodyDto })
  @Post(':id/share/:groupId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.INSPIRATION_EDITOR,
    FGAType.INSPIRATION,
    (req) => req.params.id,
  )
  @RequireFgaPermission(
    FGAType.GROUP,
    FGARelation.GROUP_INSPIRATION_PUBLISHER,
    FGAType.GROUP,
    (req) => req.params.groupId,
  )
  async shareInspiration(
    @Req() req: any,
    @Param('id') id: string,
    @Param('groupId') groupId: string,
    @Body() body: ShareInspirationBodyDto,
  ): Promise<void> {
    try {
      const externalUserId = this.getExternalUserIdFromRequest(req);

      await this.inspirationsService.shareInspiration(
        id,
        groupId,
        externalUserId,
        body.description,
        body.source,
      );

      // Create FGA tuple for sharing with group
      const groupTuple: FGATuple = {
        user_type: FGAType.GROUP,
        user_id: groupId,
        relation: FGARelation.SHARED_GROUP,
        object_type: FGAType.INSPIRATION,
        object_id: id,
      };

      this.logger.log('Creating FGA tuple for group sharing', {
        id,
        groupId,
        relation: FGARelation.SHARED_GROUP,
      });

      await this.fgaService.writeTuple(groupTuple);

      this.logger.log('Successfully shared inspiration with group', {
        id,
        groupId,
        externalUserId,
      });
    } catch (error) {
      this.safelyLogError('Error sharing inspiration with group', error);
      throw error;
    }
  }

  /**
   * Remove sharing of an inspiration with a group
   */
  @ApiOperation({ summary: 'Remove sharing of inspiration with a group' })
  @ApiParam({ name: 'id', description: 'Inspiration ID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Inspiration sharing removed successfully',
  })
  @Delete(':id/share')
  @HttpCode(HttpStatus.NO_CONTENT)
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.INSPIRATION_EDITOR,
    FGAType.INSPIRATION,
    (req) => req.params.id,
  )
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_INSPIRATION_PUBLISHER)
  async removeSharing(@Req() req: any, @Param('id') id: string): Promise<void> {
    const externalUserId = this.getExternalUserIdFromRequest(req);
    const visibleGroupIds = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_INSPIRATION_PUBLISHER,
    );
    const inspiration = await this.inspirationsService.getInspiration(
      id,
      externalUserId,
      visibleGroupIds,
    );
    if (!inspiration) {
      throw new NotFoundException('Inspiration not found');
    }

    const updated = await this.inspirationsService.removeSharing(inspiration);

    if (updated && inspiration?.is_featured && inspiration.image_url) {
      // Delete the image from S3
      try {
        await this.imageService.deleteFromS3(inspiration.image_url);
      } catch (error) {
        this.safelyLogError('Error deleting image from S3', error);
        Sentry.captureMessage(
          `Failed to delete file ${inspiration.image_url} from S3. Will need to be manually deleted.`,
        );
      }
    }

    // Delete FGA tuple for sharing with group
    if (updated && inspiration?.shared_group_id) {
      const groupTuple: FGATuple = {
        user_type: FGAType.GROUP,
        user_id: inspiration.shared_group_id,
        relation: FGARelation.SHARED_GROUP,
        object_type: FGAType.INSPIRATION,
        object_id: id,
      };
      await this.fgaService.deleteTuples([groupTuple]);
    }
  }

  /* URL Inspirations */

  /**
   * Add a new URL inspiration
   */
  @ApiOperation({ summary: 'Add a new URL inspiration' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'URL inspiration created successfully',
    type: () => BaseResponseDto,
  })
  @Post('urls')
  @UseGuards(Auth0Guard)
  async createUrlInspiration(
    @Req() req: any,
    @Body() createDto: CreateUrlInspirationDto,
  ): Promise<BaseResponseDto<Inspiration>> {
    const externalUserId = this.getExternalUserIdFromRequest(req);
    if (
      !(await this.isAuthorizedToCreateInspiration(
        externalUserId,
        createDto.shared_group_id,
      ))
    ) {
      throw new ForbiddenException(
        'You are not authorized to create this inspiration',
      );
    }

    const urlInspiration = await this.inspirationsService.createUrlInspiration(
      createDto.data,
      externalUserId,
      createDto.shared_group_id,
    );

    // If it's an existing inspiration that was already enabled, don't create FGA tuples
    if (
      urlInspiration.metadata?.wasExisting &&
      !urlInspiration.metadata.wasDisabled
    ) {
      return createResponse(
        urlInspiration,
        `Inspiration already exists with ID: ${urlInspiration.id}`,
      );
    }

    // Create FGA tuples for both new inspirations and re-enabled inspirations
    await this.createInspirationTuples(
      urlInspiration.id,
      externalUserId,
      createDto.shared_group_id,
    );

    // Add appropriate message for re-enabled inspirations
    if (urlInspiration.metadata?.wasDisabled) {
      return createResponse(
        urlInspiration,
        `Existing inspiration has been re-enabled with ID: ${urlInspiration.id}`,
      );
    }

    // New inspiration
    return createResponse(urlInspiration);
  }

  /**
   * Get External User ID from the request
   */
  private getExternalUserIdFromRequest(req: any): string {
    if (!req.user || !req.user.sub) {
      this.logger.error('User authentication information missing in request');
      throw new ForbiddenException('User not authenticated');
    }
    return req.user.sub;
  }

  /**
   * Get authorization header from the request
   */
  private getAuthorizationHeader(request: any): string {
    const authorizationHeader = request.headers.authorization;
    if (!authorizationHeader) {
      throw new UnauthorizedException('No authorization header provided');
    }
    return authorizationHeader;
  }

  /**
   * Get Internal User ID from the request
   */
  private async getInternalUserIdFromRequest(req: any): Promise<string> {
    const authorizationHeader = this.getAuthorizationHeader(req);
    const internalUser =
      await this.userService.getInternalUser(authorizationHeader);
    return internalUser.id;
  }

  /**
   * Create FGA tuples for a new inspiration
   */
  private async createInspirationTuples(
    inspirationId: string,
    externalUserId: string,
    groupId?: string | null,
  ): Promise<void> {
    this.logger.log('Creating FGA tuples for inspiration', {
      inspirationId,
      externalUserId,
      hasGroupId: !!groupId,
    });

    try {
      const tuples = this.getInspirationTuples(
        inspirationId,
        externalUserId,
        groupId,
      );

      this.logger.log('Writing FGA tuples', {
        count: tuples.length,
        inspirationId,
      });

      await this.fgaService.writeTuples(tuples);

      this.logger.log('Successfully created FGA tuples', {
        inspirationId,
        externalUserId,
      });
    } catch (error) {
      this.safelyLogError('Failed to create FGA tuples for inspiration', error);
      throw new InternalServerErrorException(
        'Failed to setup permissions for the inspiration',
      );
    }
  }

  /**
   * Delete FGA tuples for an inspiration
   */
  private async deleteInspirationTuples(
    inspirationId: string,
    externalUserId: string,
    groupId?: string | null,
  ): Promise<void> {
    this.logger.log('Deleting FGA tuples for inspiration', {
      inspirationId,
      externalUserId,
      hasGroupId: !!groupId,
    });

    try {
      const tuples = this.getInspirationTuples(
        inspirationId,
        externalUserId,
        groupId,
      );

      this.logger.log('Deleting FGA tuples', {
        count: tuples.length,
        inspirationId,
      });

      await this.fgaService.deleteTuples(tuples);

      this.logger.log('Successfully deleted FGA tuples', {
        inspirationId,
        externalUserId,
      });
    } catch (error) {
      this.safelyLogError('Failed to delete FGA tuples for inspiration', error);
      // Don't throw to avoid blocking the main operation
    }
  }

  /**
   * Get FGA tuples for an inspiration
   */
  private getInspirationTuples(
    inspirationId: string,
    externalUserId: string,
    groupId?: string | null,
  ): FGATuple[] {
    const tuples: FGATuple[] = [
      // User is the creator of the inspiration
      {
        user_type: FGAType.USER,
        user_id: externalUserId,
        relation: FGARelation.INSPIRATION_CREATOR,
        object_type: FGAType.INSPIRATION,
        object_id: inspirationId,
      },
      {
        user_type: FGAType.WORKSPACE,
        user_id: getWorkspaceId(),
        relation: FGARelation.WORKSPACE,
        object_type: FGAType.INSPIRATION,
        object_id: inspirationId,
      },
    ];

    // If groupId is provided, add group sharing tuple
    if (groupId) {
      tuples.push({
        user_type: FGAType.GROUP,
        user_id: groupId,
        relation: FGARelation.SHARED_GROUP,
        object_type: FGAType.INSPIRATION,
        object_id: inspirationId,
      });
    }

    return tuples;
  }

  /**
   * Update URL inspiration details
   */
  @ApiOperation({ summary: 'Update URL inspiration details' })
  @ApiParam({ name: 'id', description: 'URL inspiration ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'URL inspiration updated successfully',
    type: () => BaseResponseDto,
  })
  @Put('urls/:id')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.INSPIRATION_EDITOR,
    FGAType.INSPIRATION,
    (req) => req.params.id,
  )
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_INSPIRATION_VIEWER)
  async updateUrlInspiration(
    @Req() req: any,
    @Param('id') id: string,
    @Body() updateDto: UpdateUrlInspirationDto,
  ): Promise<BaseResponseDto<Inspiration>> {
    const externalUserId = this.getExternalUserIdFromRequest(req);
    const visibleGroupIds = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_INSPIRATION_VIEWER,
    );
    const inspiration = await this.inspirationsService.getInspiration(
      id,
      externalUserId,
      visibleGroupIds,
    );

    const urlInspiration = await this.inspirationsService.updateUrlInspiration(
      inspiration,
      updateDto.data,
    );
    return createResponse(urlInspiration);
  }

  @ApiOperation({ summary: 'Feature an inspiration' })
  @ApiParam({ name: 'id', description: 'Inspiration ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Inspiration featured successfully',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description:
      'Multipart form data including an optional image, description, and source.',
    schema: {
      type: 'object',
      properties: {
        image: {
          type: 'string',
          format: 'binary',
          description: 'Image file for the feature',
        },
        description: {
          type: 'string',
          description:
            'Optional update the description for the featured inspiration',
        },
        source: {
          type: 'string',
          description: 'Optional update thesource of the featured inspiration',
        },
      },
      required: ['image'],
    },
  })
  @Post(':id/feature')
  @UseInterceptors(FileInterceptor('image'))
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.INSPIRATION_FEATURE_EDITOR,
    FGAType.INSPIRATION,
    (req) => req.params.id,
  )
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_INSPIRATION_VIEWER)
  async featureInspiration(
    @Req() req: any,
    @Param('id') id: string,
    @UploadedFile() image: Express.Multer.File,
    @Body() body: FeatureInspirationBodyDto,
  ): Promise<void> {
    const externalUserId = this.getExternalUserIdFromRequest(req);

    const visibleGroupContent = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_INSPIRATION_VIEWER,
    );
    const inspiration = await this.inspirationsService.getInspiration(
      id,
      externalUserId,
      visibleGroupContent,
    );
    if (!inspiration) {
      throw new NotFoundException('Inspiration not found');
    }

    let imageUrl: string | null = null;
    if (!inspiration.shared_group_id) {
      throw new BadRequestException(
        'Featured inspirations must be already shared to a group',
      );
    }

    if (!image) {
      throw new BadRequestException(
        'Image is required to feature an inspiration.',
      );
    }

    const imageFileName = uuidv4();
    const imageBuffer = await this.imageService.processImage(image);
    imageUrl = await this.imageService.uploadToS3(
      imageBuffer,
      S3_BUCKET.PUBLIC_BUCKET,
      `community/inspirations/${id}/images`,
      imageFileName,
    );

    await this.inspirationsService.featureInspiration(
      inspiration,
      imageUrl,
      body.description,
      body.source,
    );
  }

  @ApiOperation({ summary: 'Unfeature an inspiration' })
  @ApiParam({ name: 'id', description: 'Inspiration ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Inspiration unfeatured successfully',
  })
  @Delete(':id/feature')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.INSPIRATION_FEATURE_EDITOR,
    FGAType.INSPIRATION,
    (req) => req.params.id,
  )
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_INSPIRATION_VIEWER)
  async unfeatureInspiration(
    @Req() req: any,
    @Param('id') id: string,
  ): Promise<void> {
    const externalUserId = this.getExternalUserIdFromRequest(req);
    const visibleGroupContent = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_INSPIRATION_VIEWER,
    );
    const existingInspiration = await this.inspirationsService.getInspiration(
      id,
      externalUserId,
      visibleGroupContent,
    );
    if (!existingInspiration) {
      throw new NotFoundException('Inspiration not found');
    }

    if (!existingInspiration.is_featured) {
      throw new BadRequestException(
        'Inspiration must be featured to unfeature it',
      );
    }

    await this.inspirationsService.unfeatureInspiration(existingInspiration);

    if (existingInspiration.image_url) {
      try {
        await this.imageService.deleteFromS3(existingInspiration.image_url);
      } catch (error) {
        this.safelyLogError('Error deleting image from S3', error);
        Sentry.captureMessage(
          `Failed to delete file ${existingInspiration.image_url} from S3. Will need to be manually deleted.`,
        );
      }
    }
  }

  /* Text Inspirations */

  /**
   * Add a new text inspiration
   */
  @ApiOperation({ summary: 'Add a new text inspiration' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Text inspiration created successfully',
    type: () => BaseResponseDto,
  })
  @Post('texts')
  @UseGuards(Auth0Guard)
  async createTextInspiration(
    @Req() req: any,
    @Body() createDto: CreateTextInspirationDto,
  ): Promise<BaseResponseDto<Inspiration>> {
    const externalUserId = this.getExternalUserIdFromRequest(req);
    const internalUserId = await this.getInternalUserIdFromRequest(req);
    if (
      !(await this.isAuthorizedToCreateInspiration(
        externalUserId,
        createDto.shared_group_id,
      ))
    ) {
      throw new ForbiddenException(
        'You are not authorized to create this inspiration',
      );
    }

    const textInspiration =
      await this.inspirationsService.createTextInspiration(
        createDto.data,
        externalUserId,
        internalUserId,
        createDto.shared_group_id,
      );

    // If it's an existing inspiration that was already enabled, don't create FGA tuples
    if (
      textInspiration.metadata?.wasExisting &&
      !textInspiration.metadata.wasDisabled
    ) {
      return createResponse(
        textInspiration,
        `Inspiration already exists with ID: ${textInspiration.id}`,
      );
    }

    // Create FGA tuples for both new inspirations and re-enabled inspirations
    await this.createInspirationTuples(
      textInspiration.id,
      externalUserId,
      createDto.shared_group_id,
    );

    // Add appropriate message for re-enabled inspirations
    if (textInspiration.metadata?.wasDisabled) {
      return createResponse(
        textInspiration,
        `Existing inspiration has been re-enabled with ID: ${textInspiration.id}`,
      );
    }

    // New inspiration
    return createResponse(textInspiration);
  }

  /**
   * Update text inspiration details
   */
  @ApiOperation({ summary: 'Update text inspiration details' })
  @ApiParam({ name: 'id', description: 'Text inspiration ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Text inspiration updated successfully',
    type: () => BaseResponseDto,
  })
  @Put('texts/:id')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.INSPIRATION_EDITOR,
    FGAType.INSPIRATION,
    (req) => req.params.id,
  )
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_INSPIRATION_VIEWER)
  async updateTextInspiration(
    @Req() req: any,
    @Param('id') id: string,
    @Body() updateDto: UpdateTextInspirationDto,
  ): Promise<BaseResponseDto<Inspiration>> {
    const externalUserId = this.getExternalUserIdFromRequest(req);
    const internalUserId = await this.getInternalUserIdFromRequest(req);
    const visibleGroupIds = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_INSPIRATION_VIEWER,
    );

    const inspiration = await this.inspirationsService.getInspiration(
      id,
      externalUserId,
      visibleGroupIds,
    );

    const textInspiration =
      await this.inspirationsService.updateTextInspiration(
        inspiration,
        updateDto.data,
        internalUserId,
      );
    return createResponse(textInspiration);
  }

  /* Instruction Inspirations */

  /**
   * Add a new instruction inspiration
   */
  @ApiOperation({ summary: 'Add a new instruction inspiration' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Instruction inspiration created successfully',
    type: () => BaseResponseDto,
  })
  @Post('instructions')
  @UseGuards(Auth0Guard)
  async createInstructionInspiration(
    @Req() req: any,
    @Body() createDto: CreateInstructionInspirationDto,
  ): Promise<BaseResponseDto<Inspiration>> {
    const externalUserId = this.getExternalUserIdFromRequest(req);
    if (
      !(await this.isAuthorizedToCreateInspiration(
        externalUserId,
        createDto.shared_group_id,
      ))
    ) {
      throw new ForbiddenException(
        'You are not authorized to create this inspiration',
      );
    }

    const instructionInspiration =
      await this.inspirationsService.createInstructionInspiration(
        createDto.data,
        externalUserId,
        createDto.shared_group_id,
      );

    // If it's an existing inspiration that was already enabled, don't create FGA tuples
    if (
      instructionInspiration.metadata?.wasExisting &&
      !instructionInspiration.metadata.wasDisabled
    ) {
      return createResponse(
        instructionInspiration,
        `Inspiration already exists with ID: ${instructionInspiration.id}`,
      );
    }

    // Create FGA tuples for both new inspirations and re-enabled inspirations
    await this.createInspirationTuples(
      instructionInspiration.id,
      externalUserId,
      createDto.shared_group_id,
    );

    // Add appropriate message for re-enabled inspirations
    if (instructionInspiration.metadata?.wasDisabled) {
      return createResponse(
        instructionInspiration,
        `Existing inspiration has been re-enabled with ID: ${instructionInspiration.id}`,
      );
    }

    // New inspiration
    return createResponse(instructionInspiration);
  }

  /**
   * Update instruction inspiration details
   */
  @ApiOperation({ summary: 'Update instruction inspiration details' })
  @ApiParam({ name: 'id', description: 'Instruction inspiration ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Instruction inspiration updated successfully',
    type: () => BaseResponseDto,
  })
  @Put('instructions/:id')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.INSPIRATION_EDITOR,
    FGAType.INSPIRATION,
    (req) => req.params.id,
  )
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_INSPIRATION_VIEWER)
  async updateInstructionInspiration(
    @Req() req: any,
    @Param('id') id: string,
    @Body() updateDto: UpdateInstructionInspirationDto,
  ): Promise<BaseResponseDto<Inspiration>> {
    const externalUserId = this.getExternalUserIdFromRequest(req);
    const visibleGroupIds = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_INSPIRATION_VIEWER,
    );

    // Get the inspiration first
    const inspiration = await this.inspirationsService.getInspiration(
      id,
      externalUserId,
      visibleGroupIds,
    );

    const instructionInspiration =
      await this.inspirationsService.updateInstructionInspiration(
        inspiration,
        updateDto.data,
      );
    return createResponse(instructionInspiration);
  }

  /**
   * Upload a new file inspiration
   */
  @ApiOperation({ summary: 'Upload file inspiration' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'File upload with optional metadata',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: `File to upload. Supported formats: ${ALLOWED_FILE_EXTENSIONS.join(', ')}`,
        },
        metadata: {
          type: 'string',
          description: 'Optional JSON string with name and tags',
          example: '{"name": "My File", "tags": ["tag1", "tag2"]}',
        },
      },
      required: ['file'],
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'File inspiration created successfully',
    type: () => BaseResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid request or unsupported file format',
  })
  @Post('files')
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (req, file, callback) => {
        // Extract file extension
        const originalName = file.originalname;
        const ext = originalName
          .substring(originalName.lastIndexOf('.'))
          .toLowerCase();

        // Check if extension is allowed
        if (ALLOWED_FILE_EXTENSIONS.includes(ext)) {
          callback(null, true);
        } else {
          callback(
            new BadRequestException(
              `Unsupported file type: ${ext}. Please use a supported file type.`,
            ),
            false,
          );
        }
      },
    }),
  )
  @UseGuards(Auth0Guard)
  async uploadFileInspiration(
    @Req() req: any,
    @UploadedFile() file: Express.Multer.File,
    @Body('metadata') metadataString: string,
    @Body('shared_group_id') sharedGroupId?: string,
  ): Promise<BaseResponseDto<Inspiration>> {
    try {
      const externalUserId = this.getExternalUserIdFromRequest(req);
      const internalUserId = await this.getInternalUserIdFromRequest(req);

      if (
        !(await this.isAuthorizedToCreateInspiration(
          externalUserId,
          sharedGroupId,
        ))
      ) {
        throw new ForbiddenException(
          'You are not authorized to create this inspiration',
        );
      }

      this.logger.log('Uploading file inspiration', {
        fileName: file?.originalname,
        size: file?.size,
        externalUserId,
        internalUserId,
      });

      // Validate file input
      if (!file) {
        throw new BadRequestException('No file provided');
      }

      // Parse metadata JSON if provided
      let metadata = undefined;
      if (metadataString) {
        try {
          metadata = JSON.parse(metadataString);
        } catch (error) {
          throw new BadRequestException(
            'Invalid metadata format. Expected JSON string.',
          );
        }
      }

      // Process file
      const processedFile = await this.fileService.processFile(file);

      // Calculate SHA256 hash for the file
      const hash = HashUtils.calculateHash(
        InspirationType.FILE,
        {
          fileContent: processedFile.buffer.toString('base64'),
          original_file_name: file.originalname,
        },
        externalUserId,
      );

      // Generate S3 key with internal user ID and hash
      const fileKey = this.fileService.generateFileKey(
        internalUserId,
        hash.substring(0, 8), // Use first 8 chars of hash for brevity
        file.originalname,
      );

      // Create file inspiration
      const fileInspiration =
        await this.inspirationsService.createFileInspiration(
          processedFile,
          fileKey,
          metadata,
          externalUserId, // Still use externalUserId for authorization and DB keys
          sharedGroupId,
        );

      // If it's an existing inspiration that was already enabled, don't create FGA tuples
      if (
        fileInspiration.metadata?.wasExisting &&
        !fileInspiration.metadata.wasDisabled
      ) {
        return createResponse(
          fileInspiration,
          `Inspiration already exists with ID: ${fileInspiration.id}`,
        );
      }

      // Create FGA tuples for both new inspirations and re-enabled inspirations
      await this.createInspirationTuples(
        fileInspiration.id,
        externalUserId,
        sharedGroupId,
      );

      // Add appropriate message for re-enabled inspirations
      if (fileInspiration.metadata?.wasDisabled) {
        return createResponse(
          fileInspiration,
          `Existing inspiration has been re-enabled with ID: ${fileInspiration.id}`,
        );
      }

      this.logger.log('File inspiration created', {
        id: fileInspiration.id,
        externalUserId,
        s3Path: fileKey,
      });

      // New inspiration
      return createResponse(fileInspiration);
    } catch (error) {
      this.safelyLogError('Error uploading file inspiration', error);
      throw error;
    }
  }

  /**
   * Update file inspiration metadata
   */
  @ApiOperation({ summary: 'Update file inspiration metadata' })
  @ApiParam({ name: 'id', description: 'File inspiration ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'File inspiration updated successfully',
    type: () => BaseResponseDto,
  })
  @Put('files/:id')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.INSPIRATION_EDITOR,
    FGAType.INSPIRATION,
    (req) => req.params.id,
  )
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_INSPIRATION_VIEWER)
  async updateFileInspiration(
    @Req() req: any,
    @Param('id') id: string,
    @Body() updateDto: UpdateFileInspirationDto,
  ): Promise<BaseResponseDto<Inspiration>> {
    try {
      const externalUserId = this.getExternalUserIdFromRequest(req);
      const visibleGroupIds = getVisibleResources(
        req,
        FGAType.GROUP,
        FGARelation.GROUP_INSPIRATION_VIEWER,
      );

      const inspiration = await this.inspirationsService.getInspiration(
        id,
        externalUserId,
        visibleGroupIds,
      );

      const updated = await this.inspirationsService.updateFileInspiration(
        inspiration,
        updateDto.data,
      );

      return createResponse(updated);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.safelyLogError('Error updating file inspiration', error);
      throw new InternalServerErrorException(
        'Failed to update file inspiration',
      );
    }
  }

  /**
   * Get a presigned URL to download a file inspiration
   */
  @ApiOperation({
    summary: 'Get a presigned URL to download a file inspiration',
  })
  @ApiParam({ name: 'id', description: 'File inspiration ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Presigned URL generated successfully',
    type: () => BaseResponseDto,
  })
  @Get('files/:id/download-url')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.INSPIRATION_VIEWER,
    FGAType.INSPIRATION,
    (req) => req.params.id,
  )
  async getFileDownloadUrl(
    @Req() req: any,
    @Param('id') id: string,
  ): Promise<BaseResponseDto<{ url: string; filename: string }>> {
    try {
      const externalUserId = this.getExternalUserIdFromRequest(req);
      this.logger.log('Generating file download URL', { id, externalUserId });

      // Get expiry time from environment variable
      const expirySecondsStr = this.configService.get<string>(
        'INSPIRATIONS_DOWNLOAD_EXPIRY_SECONDS',
      );

      if (!expirySecondsStr) {
        throw new Error(
          'INSPIRATIONS_DOWNLOAD_EXPIRY_SECONDS is not configured',
        );
      }

      // Parse to number for the S3 service
      const expirySeconds = parseInt(expirySecondsStr, 10);

      // Get the inspiration to ensure it's a file and to get the S3 path
      const inspiration = await this.inspirationsService.getInspiration(
        id,
        externalUserId,
      );

      if (inspiration.type !== InspirationType.FILE) {
        throw new BadRequestException('Inspiration is not a file');
      }

      if (!inspiration.file_metadata?.s3_path) {
        throw new BadRequestException('File path not found');
      }

      // Extract bucket and key from s3_path
      // Format is s3://<bucket>/<key>
      const s3Path = inspiration.file_metadata.s3_path;
      const s3PathMatch = s3Path.match(/^s3:\/\/([^/]+)\/(.+)$/);

      if (!s3PathMatch) {
        throw new BadRequestException('Invalid S3 path format');
      }

      const [, bucket, key] = s3PathMatch;

      // Generate a presigned URL with the specified expiry
      const url = await this.s3Service.getSignedUrl(
        bucket,
        key,
        S3SignedUrlOperation.GET,
        expirySeconds,
      );

      this.logger.log('Successfully generated download URL', {
        id,
        externalUserId,
        expires: `${expirySeconds} seconds`,
      });

      return createResponse({
        url,
        filename: inspiration.file_metadata.original_file_name,
      });
    } catch (error) {
      this.safelyLogError('Error generating file download URL', error);
      throw error;
    }
  }

  private async isAuthorizedToCreateInspiration(
    externalUserId: string,
    sharedGroupId?: string,
  ): Promise<boolean> {
    const inspirationCreatorResult =
      await this.fgaService.checkUserAuthorizationOnWorkspace(
        FGAType.USER,
        externalUserId,
        FGARelation.PRIVATE_INSPIRATION_CREATOR,
      );
    if (!inspirationCreatorResult) {
      return false;
    }

    if (sharedGroupId) {
      return await this.fgaService.checkUserAuthorization({
        user_type: FGAType.USER,
        user_id: externalUserId,
        relation: FGARelation.GROUP_INSPIRATION_PUBLISHER,
        object_type: FGAType.GROUP,
        object_id: sharedGroupId,
      });
    }

    return true;
  }

  /**
   * Safely extract error message from any error object
   * @param error The error object
   * @param defaultMessage Default message if error is not an Error object
   * @returns Safe error message
   * @private
   */
  private getErrorMessage(
    error: unknown,
    defaultMessage: string = 'Unknown error',
  ): string {
    return error instanceof Error ? error.message : defaultMessage;
  }

  /**
   * Safely log an error
   * @param message Error message prefix
   * @param error The error object
   * @private
   */
  private safelyLogError(message: string, error: unknown): void {
    const errorMessage = this.getErrorMessage(error);
    this.logger.error(
      `${message}: ${errorMessage}`,
      error instanceof Error ? error : undefined,
    );
  }
}
