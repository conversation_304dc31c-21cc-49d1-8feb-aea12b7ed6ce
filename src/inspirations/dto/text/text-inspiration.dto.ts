import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  IsEnum,
  ValidateNested,
  IsArray,
} from 'class-validator';
import { Type } from 'class-transformer';
import { MitreAttackObject } from '../../../rules/models/rule-metadata.model';
import { RuleType } from '../../../rules/models/rule.model';

export class CreateTextInspirationData {
  @ApiProperty({ description: 'Text content for inspiration' })
  @IsString()
  @IsNotEmpty()
  text: string;

  @ApiProperty({
    description: 'Name for the text inspiration',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Tags for the text inspiration',
    type: [String],
    required: false,
  })
  @IsOptional()
  tags?: string[];

  @ApiProperty({
    description: 'MITRE ATT&CK objects associated with this inspiration',
    type: () => [MitreAttackObject],
    required: false,
  })
  @IsOptional()
  mitre_attack?: MitreAttackObject[];

  @ApiPropertyOptional({
    description: 'The type of rule this text inspiration pertains to.',
    example: 'SigmaQ',
  })
  @IsString()
  @IsOptional()
  @IsEnum(RuleType)
  rule_type?: RuleType;

  @ApiPropertyOptional({
    description: 'The ID of the rule this text inspiration was created from.',
    example: '123',
  })
  @IsString()
  @IsOptional()
  rule_id?: string;
}

export class CreateTextInspirationDto {
  @ApiProperty({
    description: 'Text inspiration data',
    type: CreateTextInspirationData,
  })
  @ValidateNested()
  @Type(() => CreateTextInspirationData)
  @IsNotEmpty()
  data: CreateTextInspirationData;

  @ApiProperty({
    description: 'Whether the text inspiration is shared to a group',
    required: false,
  })
  @IsString()
  @IsOptional()
  shared_group_id?: string;
}

export class UpdateTextInspirationData {
  @ApiPropertyOptional({ description: 'Text content to update' })
  @IsString()
  @IsOptional()
  text?: string;

  @ApiPropertyOptional({ description: 'Name for the text inspiration' })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({
    description: 'Tags for the text inspiration',
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];

  @ApiPropertyOptional({
    description: 'MITRE ATT&CK techniques related to the inspiration',
    type: () => [MitreAttackObject],
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => MitreAttackObject)
  mitre_attack?: MitreAttackObject[];

  @ApiPropertyOptional({
    description: 'The type of rule this text inspiration pertains to.',
    example: 'Sigma',
  })
  @IsString()
  @IsEnum(RuleType)
  @IsOptional()
  rule_type?: RuleType;

  @ApiPropertyOptional({
    description: 'The ID of the rule this text inspiration was created from.',
    example: '123',
  })
  @IsString()
  @IsOptional()
  rule_id?: string;

  @ApiPropertyOptional({
    description: 'Description of the inspiration',
    example: 'This is a detailed description for the text.',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Source of the inspiration',
    example: 'User Input',
  })
  @IsString()
  @IsOptional()
  source?: string;
}

export class UpdateTextInspirationDto {
  @ApiProperty({
    description: 'Text inspiration update data',
    type: UpdateTextInspirationData,
  })
  @ValidateNested()
  @Type(() => UpdateTextInspirationData)
  @IsNotEmpty()
  data: UpdateTextInspirationData;
}
