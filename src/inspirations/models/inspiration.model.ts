import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsOptional,
  IsUUID,
  IsString,
  IsBoolean,
} from 'class-validator';
import { InspirationMetadata } from './inspiration-metadata.model';
import { RuleType } from '../../rules/models/rule.model';

export { InspirationMetadata } from './inspiration-metadata.model';

/**
 * Enum for inspiration types
 */
export enum InspirationType {
  FILE = 'FILE',
  URL = 'URL',
  TEXT = 'TEXT',
  INSTRUCTION = 'INSTRUCTION',
  MITRE = 'MITRE_TECHNIQUE',
}

/**
 * Enum for inspiration status
 */
export enum InspirationStatus {
  ENABLED = 'ENABLED',
  DISABLED = 'DISABLED',
}

/**
 * Inspiration model class
 */
export class Inspiration {
  @ApiProperty({
    description: 'Unique identifier of the inspiration',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  @ApiProperty({
    description: 'User ID who owns this inspiration',
    example: 'auth0|12345',
  })
  @IsUUID()
  user_id: string;

  @ApiProperty({
    description: 'Type of the inspiration',
    enum: InspirationType,
    enumName: 'InspirationType',
    example: InspirationType.TEXT,
  })
  @IsEnum(InspirationType)
  type: InspirationType;

  @ApiProperty({
    description: 'Status of the inspiration. Defaults to ENABLED.',
    enum: InspirationStatus,
    enumName: 'InspirationStatus',
    example: InspirationStatus.ENABLED,
  })
  @IsEnum(InspirationStatus)
  status: InspirationStatus;

  @ApiPropertyOptional({
    description: 'Name of the inspiration',
    example: 'Research on threat hunting',
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({
    description: 'Tags associated with the inspiration',
    example: ['research', 'security', 'threat-hunting'],
  })
  tags?: string[];

  @ApiPropertyOptional({
    description: 'SHA256 hash for duplicate detection',
    example: 'a1b2c3d4e5f6...',
  })
  hash?: string;

  @ApiPropertyOptional({
    description: 'File metadata for FILE type inspirations',
    example: {
      s3_path: 'inspirations/user123/file.pdf',
      original_file_name: 'research.pdf',
    },
  })
  @IsOptional()
  file_metadata?: {
    s3_path: string;
    original_file_name: string;
    [key: string]: any;
  };

  @ApiPropertyOptional({
    description: 'URL metadata for URL type inspirations',
    example: { url: 'https://example.com/article' },
  })
  @IsOptional()
  url_metadata?: {
    url: string;
    [key: string]: any;
  };

  @ApiPropertyOptional({
    description: 'Text metadata for TEXT type inspirations',
    example: { text: 'This is a sample text for inspiration' },
  })
  @IsOptional()
  text_metadata?: {
    text: string;
    [key: string]: any;
  };

  @ApiPropertyOptional({
    description: 'Instruction metadata for INSTRUCTION type inspirations',
    example: {
      instruction: 'Create a detection rule for suspicious login attempts',
    },
  })
  @IsOptional()
  instruction_metadata?: {
    instruction: string;
    [key: string]: any;
  };

  @ApiProperty({
    description: 'Additional metadata for the inspiration',
    type: InspirationMetadata,
  })
  metadata: InspirationMetadata;

  @ApiPropertyOptional({
    description: 'MITRE metadata for MITRE type inspirations',
    example: {
      id: 'T1003',
      name: 'Credential Dumping',
      description: 'Adversaries may attempt to dump credentials...',
      type: 'technique',
      mappings: [
        {
          id: 'T1003.001',
          name: 'LSASS Memory',
          platform: 'Windows',
          foo: 'bar',
        },
      ],
    },
  })
  @IsOptional()
  mitre_metadata?: {
    id: string;
    name: string;
    description: string;
    type: string;
    mappings?: Array<{ [key: string]: any }>;
    [key: string]: any;
  };

  @ApiProperty({
    description: 'Creation timestamp in ISO 8601 format',
    example: '2023-10-27T10:00:00.000Z',
  })
  created_at: string;

  @ApiProperty({
    description: 'Last update timestamp in ISO 8601 format',
    example: '2023-10-28T10:00:00.000Z',
  })
  updated_at: string;

  @ApiProperty({
    description: 'ID of group this inspiration is shared with',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  @IsOptional()
  shared_group_id?: string;

  @ApiPropertyOptional({
    description: 'Image URL for the inspiration',
    example: 'https://example.com/image.jpg',
  })
  @IsString()
  @IsOptional()
  image_url?: string | null;

  @ApiPropertyOptional({
    description: 'Whether the inspiration is featured',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  is_featured?: boolean;

  @ApiPropertyOptional({
    description: 'Description for a featured inspiration',
    example: 'This is a great example of a featured item.',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Source of the featured inspiration',
    example: 'Community Showcase',
  })
  @IsString()
  @IsOptional()
  source?: string;

  @ApiPropertyOptional({
    description:
      'The type of rule this text inspiration pertains to (e.g., SigmaQ). Only applicable for TEXT inspirations.',
    example: 'Sigma',
  })
  @IsString()
  @IsEnum(RuleType)
  @IsOptional()
  rule_type?: RuleType;

  @ApiPropertyOptional({
    description: 'The ID of the rule this text inspiration was created from.',
    example: '123',
  })
  @IsString()
  @IsOptional()
  rule_id?: string;
}

/**
 * DTO for creating an inspiration
 */
export class CreateInspirationDto {
  @ApiProperty({
    description: 'Type of the inspiration',
    enum: InspirationType,
    enumName: 'InspirationType',
    example: InspirationType.TEXT,
  })
  @IsEnum(InspirationType)
  type: InspirationType;

  @ApiPropertyOptional({
    description: 'Name of the inspiration',
    example: 'Research on threat hunting',
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({
    description: 'Tags associated with the inspiration',
    example: ['research', 'security', 'threat-hunting'],
  })
  @IsOptional()
  tags?: string[];

  @ApiPropertyOptional({
    description: 'File metadata for FILE type inspirations',
    example: {
      s3_path: 'inspirations/user123/file.pdf',
      original_file_name: 'research.pdf',
    },
  })
  @IsOptional()
  file_metadata?: {
    s3_path: string;
    original_file_name: string;
    [key: string]: any;
  };

  @ApiPropertyOptional({
    description: 'URL metadata for URL type inspirations',
    example: { url: 'https://example.com/article' },
  })
  @IsOptional()
  url_metadata?: {
    url: string;
    [key: string]: any;
  };

  @ApiPropertyOptional({
    description: 'Text metadata for TEXT type inspirations',
    example: { text: 'This is a sample text for inspiration' },
  })
  @IsOptional()
  text_metadata?: {
    text: string;
    [key: string]: any;
  };

  @ApiPropertyOptional({
    description: 'Instruction metadata for INSTRUCTION type inspirations',
    example: {
      instruction: 'Create a detection rule for suspicious login attempts',
    },
  })
  @IsOptional()
  instruction_metadata?: {
    instruction: string;
    [key: string]: any;
  };

  @ApiPropertyOptional({
    description: 'Additional metadata for the inspiration',
    type: InspirationMetadata,
  })
  @IsOptional()
  metadata?: InspirationMetadata;

  @ApiPropertyOptional({
    description: 'Status of the inspiration. Defaults to ENABLED.',
    enum: InspirationStatus,
    enumName: 'InspirationStatus',
    example: InspirationStatus.ENABLED,
    default: InspirationStatus.ENABLED,
  })
  @IsEnum(InspirationStatus)
  @IsOptional()
  status?: InspirationStatus;

  @ApiPropertyOptional({
    description: 'MITRE metadata for MITRE type inspirations',
    example: {
      id: 'T1003',
      name: 'Credential Dumping',
      description: 'Adversaries may attempt to dump credentials...',
      type: 'technique',
      mappings: [
        {
          id: 'T1003.001',
          name: 'LSASS Memory',
          platform: 'Windows',
          foo: 'bar',
        },
      ],
    },
  })
  @IsOptional()
  mitre_metadata?: {
    id: string;
    name: string;
    description: string;
    type: string;
    mappings?: Array<{ [key: string]: any }>;
    [key: string]: any;
  };
}

/**
 * DTO for updating an inspiration
 */
export class UpdateInspirationDto {
  @ApiPropertyOptional({
    description: 'Name of the inspiration',
    example: 'Research on threat hunting',
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({
    description: 'Tags associated with the inspiration',
    example: ['research', 'security', 'threat-hunting'],
  })
  @IsOptional()
  tags?: string[];

  @ApiPropertyOptional({
    description: 'File metadata for FILE type inspirations',
    example: {
      s3_path: 'inspirations/user123/file.pdf',
      original_file_name: 'research.pdf',
    },
  })
  @IsOptional()
  file_metadata?: {
    s3_path: string;
    original_file_name: string;
    [key: string]: any;
  };

  @ApiPropertyOptional({
    description: 'URL metadata for URL type inspirations',
    example: { url: 'https://example.com/article' },
  })
  @IsOptional()
  url_metadata?: {
    url: string;
    [key: string]: any;
  };

  @ApiPropertyOptional({
    description: 'Text metadata for TEXT type inspirations',
    example: { text: 'This is a sample text for inspiration' },
  })
  @IsOptional()
  text_metadata?: {
    text: string;
    [key: string]: any;
  };

  @ApiPropertyOptional({
    description: 'Instruction metadata for INSTRUCTION type inspirations',
    example: {
      instruction: 'Create a detection rule for suspicious login attempts',
    },
  })
  @IsOptional()
  instruction_metadata?: {
    instruction: string;
    [key: string]: any;
  };

  @ApiPropertyOptional({
    description: 'Additional metadata for the inspiration',
    type: InspirationMetadata,
  })
  @IsOptional()
  metadata?: InspirationMetadata;

  @ApiPropertyOptional({
    description: 'Status of the inspiration',
    enum: InspirationStatus,
    enumName: 'InspirationStatus',
    example: InspirationStatus.ENABLED,
  })
  @IsEnum(InspirationStatus)
  @IsOptional()
  status?: InspirationStatus;

  @ApiPropertyOptional({
    description: 'MITRE metadata for MITRE type inspirations',
    example: {
      id: 'T1003',
      name: 'Credential Dumping',
      description: 'Adversaries may attempt to dump credentials...',
      type: 'technique',
      mappings: [
        {
          id: 'T1003.001',
          name: 'LSASS Memory',
          platform: 'Windows',
          foo: 'bar',
        },
      ],
    },
  })
  @IsOptional()
  mitre_metadata?: {
    id: string;
    name: string;
    description: string;
    type: string;
    mappings?: Array<{ [key: string]: any }>;
    [key: string]: any;
  };
}
