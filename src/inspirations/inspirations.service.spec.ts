import { Test, TestingModule } from '@nestjs/testing';
import { InspirationsService } from './inspirations.service';
import { InspirationRepository } from './repositories/inspiration.repository';
import { ConfigService } from '@nestjs/config';
import { S3Service } from '../s3/s3.service';
import { StoredInspiration } from './schemas/inspiration.schema';
import {
  Inspiration,
  InspirationType,
  InspirationStatus,
} from './models/inspiration.model';
import { ListInspirationsQueryDto } from './dto/common/list-inspirations.dto';
import {
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InspirationKeys } from './schemas/inspiration-keys.util';
import {
  FileInspirationMetadataDto,
  UpdateFileInspirationData,
} from './dto/file/file-inspiration.dto';
import { ProcessedFile } from './file/file.service';
import { UpdateUrlInspirationData } from './dto/url/url-inspiration.dto';
import { UpdateTextInspirationData } from './dto/text/text-inspiration.dto';
import { UpdateInstructionInspirationData } from './dto/instruction/instruction-inspiration.dto';
import { RuleType } from '../rules/models/rule.model';
import { RuleInteractionsService } from '../rule-interactions/rule-interactions.service';
import { RuleInteractionType } from '../rule-interactions/models/rule-interaction.model';

describe('InspirationsService', () => {
  let service: InspirationsService;
  const OriginalDate = global.Date; // Store original Date constructor globally for this test suite
  let dateNowSpy: jest.SpyInstance;

  // Create a mock repository
  const mockInspirationRepository = {
    getInspiration: jest.fn(),
    getInspirationsByUser: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    getInspirationsByGroupIds: jest.fn(),
    updateWithRemove: jest.fn(),
    findInspirationsByHash: jest.fn(),
    getInspirationByGroupIdAndInspirationId: jest.fn(),
  };

  // Create a mock S3Service
  const mockS3Service = {
    uploadFile: jest.fn(),
    downloadFile: jest.fn(),
    getSignedUrl: jest.fn(),
    deleteObject: jest.fn(),
  };

  // Create a mock ConfigService
  const mockConfigService = {
    get: jest.fn().mockImplementation((key) => {
      if (key === 'INSPIRATION_BUCKET_NAME') {
        return 'test-inspiration-bucket';
      }
      return null;
    }),
  };

  // Create a mock RuleInteractionsService
  const mockRuleInteractionsService = {
    createInteraction: jest.fn(),
    deleteInteraction: jest.fn(),
    getUserInteractions: jest.fn(),
    getRuleInteractions: jest.fn(),
    getUserInteractionsWithRules: jest.fn(),
    deleteInteractionsForRule: jest.fn(),
  };

  beforeAll(() => {
    // Add default mocks that should be there for all tests
    mockInspirationRepository.findInspirationsByHash.mockResolvedValue([]);
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InspirationsService,
        {
          provide: InspirationRepository,
          useValue: mockInspirationRepository,
        },
        {
          provide: S3Service,
          useValue: mockS3Service,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: RuleInteractionsService,
          useValue: mockRuleInteractionsService,
        },
      ],
    }).compile();

    service = module.get<InspirationsService>(InspirationsService);
    // Mock Date.now() globally for all tests in this suite to prevent logger errors
    dateNowSpy = jest
      .spyOn(Date, 'now')
      .mockImplementation(() => new OriginalDate().getTime());
  });

  afterEach(() => {
    jest.clearAllMocks(); // Clears call counts, etc., for jest.fn()
    // dateNowSpy.mockRestore(); // This will be handled by restoreAllMocks if a global setup
    jest.restoreAllMocks(); // This should restore spies created with jest.spyOn
  });

  // --- Test data setup ---
  const mockUserId = 'test-user-id';
  const mockDate = new Date('2024-01-01T10:00:00.000Z');
  const mockDateString = mockDate.toISOString();

  const mockStoredInspirationText: StoredInspiration = {
    PK: `USER#${mockUserId}`,
    SK: `INSPIRATION#text1`,
    id: 'text1',
    user_id: mockUserId,
    type: InspirationType.TEXT,
    status: InspirationStatus.ENABLED,
    name: 'My Text Note',
    name_lowercase: 'my text note',
    created_at: mockDateString,
    updated_at: mockDateString,
    tags: ['sample', 'text'],
    text_metadata: { text: 'This is a sample text.' },
    metadata: { is_indexed: false, token_count: 4 },
    gsi_1_pk: `USER#${mockUserId}#TYPE#${InspirationType.TEXT}`,
    gsi_1_sk: `STATUS#${InspirationStatus.ENABLED}#CREATEDAT#${mockDateString}`,
    gsi_2_pk: `USER#${mockUserId}`,
    gsi_2_sk: `NAME#my text note`,
    gsi_3_pk: `USER#${mockUserId}`,
    // hash and gsi_3_sk would be set based on content
  };

  const mockStoredInspirationUrl: StoredInspiration = {
    PK: `USER#${mockUserId}`,
    SK: `INSPIRATION#url1`,
    id: 'url1',
    user_id: mockUserId,
    type: InspirationType.URL,
    status: InspirationStatus.ENABLED,
    name: 'My URL Link',
    name_lowercase: 'my url link',
    created_at: new Date(mockDate.getTime() + 1000).toISOString(), // slightly different time
    updated_at: new Date(mockDate.getTime() + 1000).toISOString(),
    tags: ['sample', 'url'],
    url_metadata: { url: 'https://example.com' },
    metadata: { is_indexed: false, token_count: 0 },
    gsi_1_pk: `USER#${mockUserId}#TYPE#${InspirationType.URL}`,
    gsi_1_sk: `STATUS#${InspirationStatus.ENABLED}#CREATEDAT#${new Date(mockDate.getTime() + 1000).toISOString()}`,
    gsi_2_pk: `USER#${mockUserId}`,
    gsi_2_sk: `NAME#my url link`,
    gsi_3_pk: `USER#${mockUserId}`,
  };

  const allMockStoredInspirations = [
    mockStoredInspirationText,
    mockStoredInspirationUrl,
  ];

  // --- listInspirations ---
  describe('listInspirations', () => {
    it('should retrieve, filter, paginate, and map inspirations for a user', async () => {
      mockInspirationRepository.getInspirationsByUser.mockResolvedValueOnce([
        ...allMockStoredInspirations,
      ]);

      // Override default sorting with created_at to match original test expectations
      const query: ListInspirationsQueryDto = {
        page: 1,
        size: 10,
        sort_by: 'created_at',
        sort_order: 'asc',
      };
      const result = await service.listInspirations(query, mockUserId);

      expect(
        mockInspirationRepository.getInspirationsByUser,
      ).toHaveBeenCalledWith(mockUserId);
      expect(result.items).toHaveLength(2);
      expect(result.total).toBe(2);
      expect(result.currentPage).toBe(1);
      expect(result.pageSize).toBe(10);

      // Check mapping (spot check a few fields)
      expect(result.items[0].id).toBe(mockStoredInspirationText.id);
      expect(result.items[0].name).toBe(mockStoredInspirationText.name);
      expect(result.items[0].type).toBe(InspirationType.TEXT);
      expect(result.items[0].text_metadata?.text).toBe(
        mockStoredInspirationText.text_metadata?.text,
      );

      expect(result.items[1].id).toBe(mockStoredInspirationUrl.id);
      expect(result.items[1].name).toBe(mockStoredInspirationUrl.name);
      expect(result.items[1].type).toBe(InspirationType.URL);
      expect(result.items[1].url_metadata?.url).toBe(
        mockStoredInspirationUrl.url_metadata?.url,
      );
    });

    it('should return empty items if repository returns no inspirations', async () => {
      mockInspirationRepository.getInspirationsByUser.mockResolvedValueOnce([]);
      const query: ListInspirationsQueryDto = {};
      const result = await service.listInspirations(query, mockUserId);

      expect(
        mockInspirationRepository.getInspirationsByUser,
      ).toHaveBeenCalledWith(mockUserId);
      expect(result.items).toEqual([]);
      expect(result.total).toBe(0);
      expect(result.currentPage).toBe(1); // Defaults to 1 even if no items/pages
    });

    it('should handle repository errors gracefully', async () => {
      const errorMessage = 'Repository failed';
      mockInspirationRepository.getInspirationsByUser.mockRejectedValueOnce(
        new Error(errorMessage),
      );
      const query: ListInspirationsQueryDto = {};

      await expect(service.listInspirations(query, mockUserId)).rejects.toThrow(
        InternalServerErrorException,
      );
      await expect(service.listInspirations(query, mockUserId)).rejects.toThrow(
        'Failed to retrieve inspirations',
      );

      expect(
        mockInspirationRepository.getInspirationsByUser,
      ).toHaveBeenCalledWith(mockUserId);
    });

    describe('filtering', () => {
      it('should filter by type', async () => {
        mockInspirationRepository.getInspirationsByUser.mockResolvedValueOnce([
          ...allMockStoredInspirations,
        ]);
        // allMockStoredInspirations contains one TEXT and one URL type
        const query: ListInspirationsQueryDto = { type: InspirationType.TEXT };
        const result = await service.listInspirations(query, mockUserId);

        expect(result.items).toHaveLength(1);
        expect(result.total).toBe(1);
        expect(result.items[0].id).toBe(mockStoredInspirationText.id);
        expect(result.items[0].type).toBe(InspirationType.TEXT);
      });

      it('should filter by name (substring, case-insensitive)', async () => {
        mockInspirationRepository.getInspirationsByUser.mockResolvedValue([
          ...allMockStoredInspirations,
        ]);
        // mockStoredInspirationText name: 'My Text Note'
        // mockStoredInspirationUrl name: 'My URL Link'
        const query: ListInspirationsQueryDto = { name: 'text' }; // Should match 'My Text Note'
        const result = await service.listInspirations(query, mockUserId);
        expect(result.items).toHaveLength(1);
        expect(result.total).toBe(1);
        expect(result.items[0].id).toBe(mockStoredInspirationText.id);

        const queryCaps: ListInspirationsQueryDto = { name: 'LINK' }; // Should match 'My URL Link'
        const resultCaps = await service.listInspirations(
          queryCaps,
          mockUserId,
        );
        expect(resultCaps.items).toHaveLength(1);
        expect(resultCaps.total).toBe(1);
        expect(resultCaps.items[0].id).toBe(mockStoredInspirationUrl.id);

        const queryNoMatch: ListInspirationsQueryDto = { name: 'nonexistent' };
        const resultNoMatch = await service.listInspirations(
          queryNoMatch,
          mockUserId,
        );
        expect(resultNoMatch.items).toHaveLength(0);
        expect(resultNoMatch.total).toBe(0);
      });

      it('should filter by tags (comma-separated, any match)', async () => {
        mockInspirationRepository.getInspirationsByUser.mockResolvedValue([
          ...allMockStoredInspirations,
        ]);
        // mockStoredInspirationText tags: ['sample', 'text']
        // mockStoredInspirationUrl tags: ['sample', 'url']

        const query: ListInspirationsQueryDto = { tags: 'text' }; // Matches mockStoredInspirationText
        const result = await service.listInspirations(query, mockUserId);
        expect(result.items).toHaveLength(1);
        expect(result.total).toBe(1);
        expect(result.items[0].id).toBe(mockStoredInspirationText.id);

        const queryMultipleTags: ListInspirationsQueryDto = {
          tags: 'url,other',
        }; // Matches mockStoredInspirationUrl
        const resultMultipleTags = await service.listInspirations(
          queryMultipleTags,
          mockUserId,
        );
        expect(resultMultipleTags.items).toHaveLength(1);
        expect(resultMultipleTags.total).toBe(1);
        expect(resultMultipleTags.items[0].id).toBe(
          mockStoredInspirationUrl.id,
        );

        const querySharedTag: ListInspirationsQueryDto = { tags: 'sample' }; // Matches both
        const resultSharedTag = await service.listInspirations(
          querySharedTag,
          mockUserId,
        );
        expect(resultSharedTag.items).toHaveLength(2);
        expect(resultSharedTag.total).toBe(2);

        const queryNoMatch: ListInspirationsQueryDto = { tags: 'nonexistent' };
        const resultNoMatch = await service.listInspirations(
          queryNoMatch,
          mockUserId,
        );
        expect(resultNoMatch.items).toHaveLength(0);
        expect(resultNoMatch.total).toBe(0);
      });

      it('should apply combined filters', async () => {
        const specificUrlInspiration: StoredInspiration = {
          ...mockStoredInspirationUrl,
          id: 'specificUrl',
          SK: 'INSPIRATION#specificUrl',
          name: 'Specific URL for Combined Test',
          tags: ['specific', 'url', 'test'],
          status: InspirationStatus.ENABLED,
        };
        mockInspirationRepository.getInspirationsByUser.mockResolvedValueOnce([
          mockStoredInspirationText,
          mockStoredInspirationUrl,
          specificUrlInspiration,
        ]);

        const query: ListInspirationsQueryDto = {
          type: InspirationType.URL,
          status: InspirationStatus.ENABLED,
          name: 'specific',
          tags: 'test,url',
        };
        const result = await service.listInspirations(query, mockUserId);
        expect(result.items).toHaveLength(1);
        expect(result.total).toBe(1);
        expect(result.items[0].id).toBe(specificUrlInspiration.id);
      });

      it('should filter by is_featured', async () => {
        const featuredInspiration: StoredInspiration = {
          ...mockStoredInspirationText,
          id: 'featuredItem',
          SK: 'INSPIRATION#featuredItem',
          is_featured: true,
          status: InspirationStatus.ENABLED, // ensure it passes the enabled filter too
        };
        const notFeaturedInspiration: StoredInspiration = {
          ...mockStoredInspirationUrl,
          id: 'notFeaturedItem',
          SK: 'INSPIRATION#notFeaturedItem',
          is_featured: false,
          status: InspirationStatus.ENABLED,
        };
        const undefinedFeaturedInspiration: StoredInspiration = {
          ...mockStoredInspirationText, // Another text inspiration
          id: 'undefinedFeaturedItem',
          SK: 'INSPIRATION#undefinedFeaturedItem',
          // is_featured is undefined
          status: InspirationStatus.ENABLED,
        };

        mockInspirationRepository.getInspirationsByUser.mockResolvedValueOnce([
          featuredInspiration,
          notFeaturedInspiration,
          undefinedFeaturedInspiration,
          mockStoredInspirationText, // A regular item, is_featured will be undefined
        ]);

        // Test for is_featured: true
        let query: ListInspirationsQueryDto = { is_featured: true };
        let result = await service.listInspirations(query, mockUserId);
        expect(result.items).toHaveLength(1);
        expect(result.total).toBe(1);
        expect(result.items[0].id).toBe(featuredInspiration.id);
        expect(result.items[0].is_featured).toBe(true);

        // Test for is_featured: false
        // Need to reset the mock for the next call if it was mockResolvedValueOnce
        mockInspirationRepository.getInspirationsByUser.mockResolvedValueOnce([
          featuredInspiration,
          notFeaturedInspiration,
          undefinedFeaturedInspiration,
          mockStoredInspirationText,
        ]);
        query = { is_featured: false };
        result = await service.listInspirations(query, mockUserId);
        expect(result.items).toHaveLength(1);
        expect(result.total).toBe(1);
        expect(result.items[0].id).toBe(notFeaturedInspiration.id);
        expect(result.items[0].is_featured).toBe(false);
      });
    });

    describe('pagination', () => {
      const manyInspirations: StoredInspiration[] = Array.from(
        { length: 25 },
        (_, i) => ({
          ...mockStoredInspirationText,
          id: `textItem${i + 1}`,
          SK: `INSPIRATION#textItem${i + 1}`,
          name: `Text Item ${i + 1}`,
          // ensure other unique aspects if necessary for deep equality or specific tests
        }),
      );

      beforeEach(() => {
        // Default mock for pagination tests, can be overridden in specific tests if needed
        mockInspirationRepository.getInspirationsByUser.mockResolvedValue([
          ...manyInspirations,
        ]);
      });

      it('should return the first page with default size if no page/size specified', async () => {
        const query: ListInspirationsQueryDto = {};
        const result = await service.listInspirations(query, mockUserId);
        expect(result.items).toHaveLength(10); // Default size
        expect(result.total).toBe(25);
        expect(result.currentPage).toBe(1);
        expect(result.pageSize).toBe(10);
        expect(result.items[0].id).toBe('textItem1');
      });

      it('should return the specified page and size', async () => {
        const query: ListInspirationsQueryDto = { page: 2, size: 5 };
        const result = await service.listInspirations(query, mockUserId);
        expect(result.items).toHaveLength(5);
        expect(result.total).toBe(25);
        expect(result.currentPage).toBe(2);
        expect(result.pageSize).toBe(5);
        expect(result.items[0].id).toBe('textItem6'); // 6th item overall (page 2, size 5)
      });

      it('should handle requests for pages beyond total pages', async () => {
        const query: ListInspirationsQueryDto = { page: 10, size: 5 }; // 25 items, size 5 -> 5 pages. Page 10 is too high.
        const result = await service.listInspirations(query, mockUserId);
        // The current page will be adjusted to the last valid page (5).
        // Items from that last page will be returned.
        expect(result.items).toHaveLength(5); // Items from page 5 (items 21-25)
        expect(result.total).toBe(25);
        expect(result.currentPage).toBe(5); // Should default to the last valid page
        expect(result.pageSize).toBe(5);
        expect(result.items[0].id).toBe('textItem21'); // First item of the last page
      });

      it('should handle page 0 or negative by defaulting to page 1', async () => {
        const queryPage0: ListInspirationsQueryDto = { page: 0, size: 5 };
        const resultPage0 = await service.listInspirations(
          queryPage0,
          mockUserId,
        );
        expect(resultPage0.currentPage).toBe(1);
        expect(resultPage0.items[0].id).toBe('textItem1');

        const queryPageNegative: ListInspirationsQueryDto = {
          page: -5,
          size: 5,
        };
        const resultPageNegative = await service.listInspirations(
          queryPageNegative,
          mockUserId,
        );
        expect(resultPageNegative.currentPage).toBe(1);
        expect(resultPageNegative.items[0].id).toBe('textItem1');
      });

      it('should handle pagination when total items are zero', async () => {
        mockInspirationRepository.getInspirationsByUser.mockResolvedValue([]); // No items
        const query: ListInspirationsQueryDto = { page: 1, size: 5 };
        const result = await service.listInspirations(query, mockUserId);
        expect(result.items).toHaveLength(0);
        expect(result.total).toBe(0);
        expect(result.currentPage).toBe(1); // Still page 1, as it's max(1, totalPages)
        expect(result.pageSize).toBe(5);
      });

      it('should return all items if size is larger than total items', async () => {
        const query: ListInspirationsQueryDto = { page: 1, size: 50 }; // size 50, 25 items
        const result = await service.listInspirations(query, mockUserId);
        expect(result.items).toHaveLength(25);
        expect(result.total).toBe(25);
        expect(result.currentPage).toBe(1);
        expect(result.pageSize).toBe(50);
      });
    });

    describe('sortInspirations', () => {
      it('should sort by updated_at in descending order by default', async () => {
        // Create test data with different updated_at timestamps
        const olderItem: StoredInspiration = {
          ...mockStoredInspirationText,
          id: 'older',
          updated_at: '2023-01-01T10:00:00.000Z',
        };
        const newerItem: StoredInspiration = {
          ...mockStoredInspirationUrl,
          id: 'newer',
          updated_at: '2023-02-01T10:00:00.000Z',
        };

        mockInspirationRepository.getInspirationsByUser.mockResolvedValueOnce([
          olderItem, // Add older item first in array
          newerItem,
        ]);

        // Don't specify sort parameters to test defaults
        const query: ListInspirationsQueryDto = { page: 1, size: 10 };
        const result = await service.listInspirations(query, mockUserId);

        // Default sort is by updated_at desc, so newer should be first
        expect(result.items[0].id).toBe('newer');
        expect(result.items[1].id).toBe('older');
      });

      it('should sort by created_at when specified', async () => {
        // Items with different created_at but same updated_at
        const olderCreated: StoredInspiration = {
          ...mockStoredInspirationText,
          id: 'olderCreated',
          created_at: '2023-01-01T10:00:00.000Z',
          updated_at: '2023-03-01T10:00:00.000Z', // Same updated_at
        };
        const newerCreated: StoredInspiration = {
          ...mockStoredInspirationUrl,
          id: 'newerCreated',
          created_at: '2023-02-01T10:00:00.000Z',
          updated_at: '2023-03-01T10:00:00.000Z', // Same updated_at
        };

        mockInspirationRepository.getInspirationsByUser.mockResolvedValueOnce([
          olderCreated,
          newerCreated,
        ]);

        // Test ascending order
        const queryAsc: ListInspirationsQueryDto = {
          page: 1,
          size: 10,
          sort_by: 'created_at',
          sort_order: 'asc',
        };
        const resultAsc = await service.listInspirations(queryAsc, mockUserId);
        expect(resultAsc.items[0].id).toBe('olderCreated');
        expect(resultAsc.items[1].id).toBe('newerCreated');

        // Test descending order
        mockInspirationRepository.getInspirationsByUser.mockResolvedValueOnce([
          olderCreated,
          newerCreated,
        ]);
        const queryDesc: ListInspirationsQueryDto = {
          page: 1,
          size: 10,
          sort_by: 'created_at',
          sort_order: 'desc',
        };
        const resultDesc = await service.listInspirations(
          queryDesc,
          mockUserId,
        );
        expect(resultDesc.items[0].id).toBe('newerCreated');
        expect(resultDesc.items[1].id).toBe('olderCreated');
      });

      it('should sort by name when specified', async () => {
        // Items with different names
        const alphaFirst: StoredInspiration = {
          ...mockStoredInspirationText,
          id: 'alphaFirst',
          name: 'AAA Test',
          name_lowercase: 'aaa test',
        };
        const alphaLast: StoredInspiration = {
          ...mockStoredInspirationUrl,
          id: 'alphaLast',
          name: 'ZZZ Test',
          name_lowercase: 'zzz test',
        };

        mockInspirationRepository.getInspirationsByUser.mockResolvedValueOnce([
          alphaLast, // Add in reverse order
          alphaFirst,
        ]);

        const query: ListInspirationsQueryDto = {
          page: 1,
          size: 10,
          sort_by: 'name',
          sort_order: 'asc',
        };
        const result = await service.listInspirations(query, mockUserId);

        expect(result.items[0].id).toBe('alphaFirst');
        expect(result.items[1].id).toBe('alphaLast');

        // Test descending order
        mockInspirationRepository.getInspirationsByUser.mockResolvedValueOnce([
          alphaFirst, // Add in alphabetical order
          alphaLast,
        ]);
        const queryDesc: ListInspirationsQueryDto = {
          page: 1,
          size: 10,
          sort_by: 'name',
          sort_order: 'desc',
        };
        const resultDesc = await service.listInspirations(
          queryDesc,
          mockUserId,
        );

        expect(resultDesc.items[0].id).toBe('alphaLast');
        expect(resultDesc.items[1].id).toBe('alphaFirst');
      });
    });
  });

  // --- getInspiration ---
  describe('getInspiration', () => {
    const inspirationIdToGet = 'inspi-abc-123';
    const groupInspirationToFind: StoredInspiration = {
      ...mockStoredInspirationText, // Base on existing mock for completeness
      id: inspirationIdToGet,
      user_id: 'another-user-id', // Potentially owned by another user but shared to group
      PK: `USER#another-user-id`,
      SK: `INSPIRATION#${inspirationIdToGet}`,
      shared_group_id: 'group-xyz',
      gsi_4_pk: `GROUP#group-xyz`,
      gsi_4_sk: `USER#another-user-id#INSPIRATION#${inspirationIdToGet}`,
      name: 'Group Shared Inspiration',
    };

    beforeEach(() => {
      // Reset mocks before each test in this suite
      mockInspirationRepository.getInspiration.mockReset();
      mockInspirationRepository.getInspirationByGroupIdAndInspirationId.mockReset();
    });

    it('should return inspiration directly if found for the user', async () => {
      mockInspirationRepository.getInspiration.mockResolvedValueOnce(
        mockStoredInspirationText,
      );
      const result = await service.getInspiration(
        mockStoredInspirationText.id,
        mockUserId,
      );

      expect(mockInspirationRepository.getInspiration).toHaveBeenCalledWith(
        mockUserId,
        mockStoredInspirationText.id,
      );
      expect(
        mockInspirationRepository.getInspirationByGroupIdAndInspirationId,
      ).not.toHaveBeenCalled();
      expect(result.id).toBe(mockStoredInspirationText.id);
    });

    it('should throw NotFoundException if not found for user and no visibleGroupIds provided', async () => {
      mockInspirationRepository.getInspiration.mockResolvedValueOnce(null);
      await expect(
        service.getInspiration(inspirationIdToGet, mockUserId, []),
      ).rejects.toThrow(NotFoundException);
      expect(mockInspirationRepository.getInspiration).toHaveBeenCalledWith(
        mockUserId,
        inspirationIdToGet,
      );
      expect(
        mockInspirationRepository.getInspirationByGroupIdAndInspirationId,
      ).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException if not found for user and not in any visible groups', async () => {
      mockInspirationRepository.getInspiration.mockResolvedValueOnce(null);
      mockInspirationRepository.getInspirationByGroupIdAndInspirationId.mockResolvedValue(
        null,
      ); // All group lookups return null
      const visibleGroupIds = ['group1', 'group2'];

      await expect(
        service.getInspiration(inspirationIdToGet, mockUserId, visibleGroupIds),
      ).rejects.toThrow(NotFoundException);
      expect(mockInspirationRepository.getInspiration).toHaveBeenCalledWith(
        mockUserId,
        inspirationIdToGet,
      );
      expect(
        mockInspirationRepository.getInspirationByGroupIdAndInspirationId,
      ).toHaveBeenCalledTimes(visibleGroupIds.length);
      expect(
        mockInspirationRepository.getInspirationByGroupIdAndInspirationId,
      ).toHaveBeenCalledWith(visibleGroupIds[0], inspirationIdToGet);
      expect(
        mockInspirationRepository.getInspirationByGroupIdAndInspirationId,
      ).toHaveBeenCalledWith(visibleGroupIds[1], inspirationIdToGet);
    });

    describe('batching for group lookups', () => {
      const GROUP_LOOKUP_BATCH_SIZE = 10; // Align with service constant
      const totalGroupIds = 22;
      const mockVisibleGroupIds = Array.from(
        { length: totalGroupIds },
        (_, i) => `group-id-${i + 1}`,
      );
      const inspirationToFindInBatch = {
        ...groupInspirationToFind, // Use a specific mock for this
        shared_group_id: 'group-id-12', // Will be found in the 12th group
      };

      beforeEach(() => {
        // Default: user direct lookup fails
        mockInspirationRepository.getInspiration.mockResolvedValue(null);
        // Default: group lookups mostly fail, one will succeed in the specific test
        mockInspirationRepository.getInspirationByGroupIdAndInspirationId.mockResolvedValue(
          null,
        );
      });

      it('should find inspiration in the second batch and not process subsequent batches', async () => {
        // Mock the specific group ID call to return the inspiration
        mockInspirationRepository.getInspirationByGroupIdAndInspirationId.mockImplementation(
          async (groupId, inspId) => {
            if (groupId === 'group-id-12' && inspId === inspirationIdToGet) {
              return inspirationToFindInBatch;
            }
            return null;
          },
        );

        const result = await service.getInspiration(
          inspirationIdToGet,
          mockUserId,
          mockVisibleGroupIds,
        );

        expect(mockInspirationRepository.getInspiration).toHaveBeenCalledWith(
          mockUserId,
          inspirationIdToGet,
        );

        // Batch 1: group-id-1 to group-id-10 (10 calls, all null)
        // Batch 2: group-id-11, group-id-12 (2 calls, group-id-12 returns item)
        // Promise.all executes all promises in a batch, so both full batches will be called.
        const expectedCalls = 20; // 10 for batch 1 + 10 for batch 2
        expect(
          mockInspirationRepository.getInspirationByGroupIdAndInspirationId,
        ).toHaveBeenCalledTimes(expectedCalls);

        // Check specific calls for the first two batches (up to group-id-20)
        for (let i = 0; i < expectedCalls; i++) {
          expect(
            mockInspirationRepository.getInspirationByGroupIdAndInspirationId,
          ).toHaveBeenCalledWith(`group-id-${i + 1}`, inspirationIdToGet);
        }
        // Ensure it wasn't called for group-id-21 (start of the third batch)
        expect(
          mockInspirationRepository.getInspirationByGroupIdAndInspirationId,
        ).not.toHaveBeenCalledWith('group-id-21', inspirationIdToGet);

        expect(result.id).toBe(inspirationIdToGet);
        expect(result.name).toBe(inspirationToFindInBatch.name);
        expect(result.shared_group_id).toBe('group-id-12');
      });

      it('should process all batches if inspiration not found in any group', async () => {
        // All group lookups will return null (default mock behavior from beforeEach)
        await expect(
          service.getInspiration(
            inspirationIdToGet,
            mockUserId,
            mockVisibleGroupIds,
          ),
        ).rejects.toThrow(NotFoundException);

        expect(
          mockInspirationRepository.getInspirationByGroupIdAndInspirationId,
        ).toHaveBeenCalledTimes(totalGroupIds);
        // Check last group ID call to confirm all were processed
        expect(
          mockInspirationRepository.getInspirationByGroupIdAndInspirationId,
        ).toHaveBeenCalledWith(`group-id-${totalGroupIds}`, inspirationIdToGet);
      });
    });

    it('should throw InternalServerErrorException if getInspiration (user) fails', async () => {
      mockInspirationRepository.getInspiration.mockRejectedValueOnce(
        new Error('DB error'),
      );
      await expect(
        service.getInspiration(inspirationIdToGet, mockUserId, []),
      ).rejects.toThrow(InternalServerErrorException);
    });

    it('should throw InternalServerErrorException if getInspirationByGroupIdAndInspirationId fails', async () => {
      mockInspirationRepository.getInspiration.mockResolvedValueOnce(null); // User lookup fails
      mockInspirationRepository.getInspirationByGroupIdAndInspirationId.mockRejectedValueOnce(
        new Error('Group DB error'),
      );
      const visibleGroupIds = ['group1'];

      await expect(
        service.getInspiration(inspirationIdToGet, mockUserId, visibleGroupIds),
      ).rejects.toThrow(InternalServerErrorException);
      // It should attempt the group lookup
      expect(
        mockInspirationRepository.getInspirationByGroupIdAndInspirationId,
      ).toHaveBeenCalledWith(visibleGroupIds[0], inspirationIdToGet);
    });
  });

  // --- listInspirationsForGroups ---
  describe('listInspirationsForGroups', () => {
    const mockGroupIds = ['group1', 'group2'];
    // Re-use allMockStoredInspirations for this, assuming they could be returned by getInspirationsByGroupIds

    it('should retrieve, filter, paginate, and map inspirations for groups', async () => {
      mockInspirationRepository.getInspirationsByGroupIds.mockResolvedValueOnce(
        [...allMockStoredInspirations],
      );
      const query: ListInspirationsQueryDto = {
        page: 1,
        size: 10,
        type: InspirationType.URL,
      };
      const result = await service.listInspirationsForGroups(
        query,
        mockGroupIds,
      );

      expect(
        mockInspirationRepository.getInspirationsByGroupIds,
      ).toHaveBeenCalledWith(mockGroupIds);
      // After filtering by URL type, only one item should remain from allMockStoredInspirations
      expect(result.items).toHaveLength(1);
      expect(result.total).toBe(1);
      expect(result.currentPage).toBe(1);
      expect(result.pageSize).toBe(10);
      expect(result.items[0].id).toBe(mockStoredInspirationUrl.id);
      expect(result.items[0].type).toBe(InspirationType.URL);
    });

    it('should return empty items if repository returns no group inspirations', async () => {
      mockInspirationRepository.getInspirationsByGroupIds.mockResolvedValueOnce(
        [],
      );
      const query: ListInspirationsQueryDto = {};
      const result = await service.listInspirationsForGroups(
        query,
        mockGroupIds,
      );

      expect(
        mockInspirationRepository.getInspirationsByGroupIds,
      ).toHaveBeenCalledWith(mockGroupIds);
      expect(result.items).toEqual([]);
      expect(result.total).toBe(0);
    });

    it('should handle repository errors for group inspirations gracefully', async () => {
      const errorMessage = 'Group repository failed';
      mockInspirationRepository.getInspirationsByGroupIds.mockRejectedValueOnce(
        new Error(errorMessage),
      );
      const query: ListInspirationsQueryDto = {};

      await expect(
        service.listInspirationsForGroups(query, mockGroupIds),
      ).rejects.toThrow(InternalServerErrorException);
      await expect(
        service.listInspirationsForGroups(query, mockGroupIds),
      ).rejects.toThrow('Failed to retrieve inspirations');
      expect(
        mockInspirationRepository.getInspirationsByGroupIds,
      ).toHaveBeenCalledWith(mockGroupIds);
    });
  });

  // --- deleteInspiration ---
  describe('deleteInspiration', () => {
    const inspirationIdToDelete = 'delId1';
    const userKey = `USER#${mockUserId}`;
    const inspirationKey = `INSPIRATION#${inspirationIdToDelete}`;
    const expectedRepoKey = { PK: userKey, SK: inspirationKey };
    const mockStoredDeletedInspiration: StoredInspiration = {
      ...mockStoredInspirationText, // Use a base mock
      id: inspirationIdToDelete,
      PK: userKey,
      SK: inspirationKey,
      user_id: mockUserId,
      shared_group_id: 'group123', // Example with a shared group
    };

    it('should delete an inspiration and return the mapped inspiration object', async () => {
      // Mock the first getInspiration call (deleteInspiration -> getInspiration)
      mockInspirationRepository.getInspiration.mockResolvedValueOnce(
        mockStoredDeletedInspiration,
      );

      // Mock the second getInspiration call (updateInspirationStatus)
      mockInspirationRepository.getInspiration.mockResolvedValueOnce(
        mockStoredDeletedInspiration,
      );

      // Mock the updateWithRemove call for status update to DISABLED (since it has shared_group_id)
      const updatedInspiration = {
        ...mockStoredDeletedInspiration,
        status: InspirationStatus.DISABLED,
        updated_at: new Date().toISOString(),
      };
      mockInspirationRepository.updateWithRemove.mockResolvedValueOnce(
        updatedInspiration,
      );

      // Mock the third getInspiration call to return the updated (now disabled) inspiration
      mockInspirationRepository.getInspiration.mockResolvedValueOnce(
        updatedInspiration,
      );

      const expectedMappedInspiration =
        service['mapToApiModel'](updatedInspiration);
      const result = await service.deleteInspiration(
        inspirationIdToDelete,
        mockUserId,
      );

      expect(result).toEqual(expectedMappedInspiration);
      // Check that getInspiration was called 3 times total
      expect(mockInspirationRepository.getInspiration).toHaveBeenCalledTimes(3);
      expect(mockInspirationRepository.getInspiration).toHaveBeenNthCalledWith(
        1,
        mockUserId,
        inspirationIdToDelete,
      );
      expect(mockInspirationRepository.getInspiration).toHaveBeenNthCalledWith(
        2,
        mockUserId,
        inspirationIdToDelete,
      );
      expect(mockInspirationRepository.getInspiration).toHaveBeenNthCalledWith(
        3,
        mockUserId,
        inspirationIdToDelete,
      );
    });

    it('should return null if inspiration to delete is not found', async () => {
      // Mock getInspiration to simulate not found
      mockInspirationRepository.getInspiration.mockResolvedValueOnce(null);

      const result = await service.deleteInspiration(
        inspirationIdToDelete,
        mockUserId,
      );

      expect(result).toBeNull();
      expect(mockInspirationRepository.getInspiration).toHaveBeenCalledWith(
        mockUserId,
        inspirationIdToDelete,
      );
      // Verify the repository's updateWithRemove is never called
      expect(mockInspirationRepository.updateWithRemove).not.toHaveBeenCalled();
    });

    it('should return null if getInspiration throws NotFoundException', async () => {
      // Mock getInspiration to throw NotFoundException
      mockInspirationRepository.getInspiration.mockRejectedValueOnce(
        new NotFoundException('Inspiration not found'),
      );

      const result = await service.deleteInspiration(
        inspirationIdToDelete,
        mockUserId,
      );

      expect(result).toBeNull();
      expect(mockInspirationRepository.getInspiration).toHaveBeenCalledWith(
        mockUserId,
        inspirationIdToDelete,
      );
      // Verify the repository's updateWithRemove is never called
      expect(mockInspirationRepository.updateWithRemove).not.toHaveBeenCalled();
    });

    it('should throw InternalServerErrorException if repository delete fails with a generic error', async () => {
      // Mock the first getInspiration call (deleteInspiration -> getInspiration) to succeed
      mockInspirationRepository.getInspiration.mockResolvedValueOnce(
        mockStoredDeletedInspiration,
      );

      // Mock the second getInspiration call (updateInspirationStatus) to succeed
      mockInspirationRepository.getInspiration.mockResolvedValueOnce(
        mockStoredDeletedInspiration,
      );

      // Mock updateWithRemove to fail with a generic error
      const errorMessage = 'Repo delete failed';
      mockInspirationRepository.updateWithRemove.mockRejectedValueOnce(
        new Error(errorMessage),
      );

      await expect(
        service.deleteInspiration(inspirationIdToDelete, mockUserId),
      ).rejects.toThrow(InternalServerErrorException);

      expect(mockInspirationRepository.getInspiration).toHaveBeenCalledTimes(2);
      expect(mockInspirationRepository.updateWithRemove).toHaveBeenCalledTimes(
        1,
      );
    });

    it('should throw InternalServerErrorException if updateInspirationStatus fails with a generic error', async () => {
      const errorMessage = 'Status update error';

      // Mock the first call to getInspiration (called by deleteInspiration -> getInspiration)
      mockInspirationRepository.getInspiration.mockResolvedValueOnce(
        mockStoredDeletedInspiration,
      );

      // Mock the second call to getInspiration (called by updateInspirationStatus)
      mockInspirationRepository.getInspiration.mockResolvedValueOnce(
        mockStoredDeletedInspiration,
      );

      // Since mockStoredDeletedInspiration has shared_group_id, updateInspirationStatus will call updateWithRemove
      mockInspirationRepository.updateWithRemove.mockRejectedValueOnce(
        new Error(errorMessage),
      );

      await expect(
        service.deleteInspiration(inspirationIdToDelete, mockUserId),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  // --- updateInspirationStatus ---
  describe('updateInspirationStatus', () => {
    const inspirationIdToUpdate = 'statusUpdateId1';
    const userKey = `USER#${mockUserId}`;
    const inspirationKey = `INSPIRATION#${inspirationIdToUpdate}`;
    const expectedRepoKey = { PK: userKey, SK: inspirationKey };

    const originalInspiration: StoredInspiration = {
      ...mockStoredInspirationText,
      id: inspirationIdToUpdate,
      SK: inspirationKey,
      status: InspirationStatus.ENABLED,
      created_at: '2023-01-01T00:00:00.000Z', // Fixed created_at for GSI key testing
      gsi_1_sk: InspirationKeys.getStatusCreatedAtKey(
        InspirationStatus.ENABLED,
        '2023-01-01T00:00:00.000Z',
      ),
    };

    it('should update inspiration status successfully', async () => {
      const newStatus = InspirationStatus.DISABLED;
      // originalInspiration has created_at: '2023-01-01T00:00:00.000Z'

      // Service now fetches existingInspiration first.
      mockInspirationRepository.getInspiration.mockResolvedValueOnce(
        originalInspiration,
      );

      const capturedDateForUpdate = new Date(); // For updated_at
      const capturedDateStringForUpdate = capturedDateForUpdate.toISOString();

      // Now, expectedUpdatePayload uses the originalInspiration.created_at for gsi_1_sk
      const expectedUpdatePayload = {
        status: newStatus,
        updated_at: capturedDateStringForUpdate,
        gsi_1_sk: InspirationKeys.getStatusCreatedAtKey(
          newStatus,
          originalInspiration.created_at,
        ),
      };

      const updatedRepoResponse: StoredInspiration = {
        ...originalInspiration,
        status: newStatus,
        updated_at: capturedDateStringForUpdate,
        gsi_1_sk: InspirationKeys.getStatusCreatedAtKey(
          newStatus,
          originalInspiration.created_at,
        ),
      };
      mockInspirationRepository.update.mockResolvedValueOnce(
        updatedRepoResponse,
      );

      const mockDateConstructorSpy = jest
        .spyOn(global, 'Date')
        .mockImplementation(((...args: any[]) => {
          if (args.length === 0) {
            return capturedDateForUpdate;
          }
          return new OriginalDate(...(args as [any]));
        }) as any);

      // Create an Inspiration object to pass to the method
      const inspirationToUpdate: Inspiration =
        service['mapToApiModel'](originalInspiration);

      const result = await service.updateInspirationStatus(
        inspirationToUpdate,
        newStatus,
      );

      expect(mockInspirationRepository.getInspiration).toHaveBeenCalledWith(
        mockUserId,
        inspirationIdToUpdate,
      );
      expect(mockInspirationRepository.update).toHaveBeenCalledWith(
        expectedRepoKey,
        expect.objectContaining(expectedUpdatePayload),
      );
      expect(result.id).toBe(inspirationIdToUpdate);
      expect(result.status).toBe(newStatus);
      expect(result.updated_at).toBe(capturedDateStringForUpdate);

      mockDateConstructorSpy.mockRestore(); // Restore only this specific spy immediately
    });

    it('should throw NotFoundException if inspiration for status update is not found by getInspiration', async () => {
      // Create an Inspiration object to pass to the method
      const inspirationToUpdate: Inspiration =
        service['mapToApiModel'](originalInspiration);

      mockInspirationRepository.getInspiration.mockResolvedValueOnce(null); // This call will fail first

      await expect(
        service.updateInspirationStatus(
          inspirationToUpdate,
          InspirationStatus.DISABLED,
        ),
      ).rejects.toThrow(NotFoundException);
      expect(mockInspirationRepository.getInspiration).toHaveBeenCalledWith(
        mockUserId,
        inspirationIdToUpdate,
      );
      expect(mockInspirationRepository.update).not.toHaveBeenCalled(); // Update should not be called
    });

    it('should throw NotFoundException if repository.update returns null (e.g. condition failed)', async () => {
      // Create an Inspiration object to pass to the method
      const inspirationToUpdate: Inspiration =
        service['mapToApiModel'](originalInspiration);

      // First, getInspiration must succeed
      mockInspirationRepository.getInspiration.mockResolvedValueOnce(
        originalInspiration,
      );
      // Then, update returns null
      mockInspirationRepository.update.mockResolvedValueOnce(null);

      await expect(
        service.updateInspirationStatus(
          inspirationToUpdate,
          InspirationStatus.DISABLED,
        ),
      ).rejects.toThrow(NotFoundException);
      expect(mockInspirationRepository.getInspiration).toHaveBeenCalledWith(
        mockUserId,
        inspirationIdToUpdate,
      );
      expect(mockInspirationRepository.update).toHaveBeenCalled();
    });

    it('should throw InternalServerErrorException if repository update for status fails after get succeeds', async () => {
      const errorMessage = 'Repo status update failed';
      // Create an Inspiration object to pass to the method
      const inspirationToUpdate: Inspiration =
        service['mapToApiModel'](originalInspiration);

      // First, getInspiration must succeed
      mockInspirationRepository.getInspiration.mockResolvedValueOnce(
        originalInspiration,
      );
      // Then, update fails
      mockInspirationRepository.update.mockRejectedValueOnce(
        new Error(errorMessage),
      );

      await expect(
        service.updateInspirationStatus(
          inspirationToUpdate,
          InspirationStatus.DISABLED,
        ),
      ).rejects.toThrow(InternalServerErrorException);
      expect(mockInspirationRepository.getInspiration).toHaveBeenCalledWith(
        mockUserId,
        inspirationIdToUpdate,
      );
      expect(mockInspirationRepository.update).toHaveBeenCalled();
    });
  });

  // --- shareInspiration ---
  describe('shareInspiration', () => {
    const inspirationId = 'shareTestId';
    const groupId = 'groupShareTestId';
    const sharerUserId = 'sharerUserId';
    const description = 'Shared description';
    const source = 'Shared source';
    let existingInspiration: StoredInspiration;

    beforeEach(() => {
      existingInspiration = {
        ...mockStoredInspirationText, // Use a base mock
        id: inspirationId,
        user_id: sharerUserId, // The user who owns and is sharing the inspiration
        PK: InspirationKeys.formatUserKey(sharerUserId),
        SK: InspirationKeys.formatInspirationKey(inspirationId),
      };
      mockInspirationRepository.getInspiration.mockResolvedValue(
        existingInspiration,
      );
      mockInspirationRepository.update.mockImplementation(
        async (key, payload) => ({
          ...existingInspiration,
          ...payload,
          updated_at: new Date().toISOString(), // Simulate update
        }),
      );
    });

    it('should successfully share an inspiration with description and source', async () => {
      await service.shareInspiration(
        inspirationId,
        groupId,
        sharerUserId,
        description,
        source,
      );

      expect(mockInspirationRepository.getInspiration).toHaveBeenCalledWith(
        sharerUserId,
        inspirationId,
      );
      expect(mockInspirationRepository.update).toHaveBeenCalledWith(
        { PK: existingInspiration.PK, SK: existingInspiration.SK },
        expect.objectContaining({
          shared_group_id: groupId,
          gsi_4_pk: `GROUP#${groupId}`,
          gsi_4_sk: `INSPIRATION#${inspirationId}`,
          description: description,
          source: source,
          updated_at: expect.any(String),
        }),
      );
    });

    it('should successfully share an inspiration with only description (source undefined)', async () => {
      await service.shareInspiration(
        inspirationId,
        groupId,
        sharerUserId,
        description,
        undefined,
      );

      expect(mockInspirationRepository.update).toHaveBeenCalledWith(
        { PK: existingInspiration.PK, SK: existingInspiration.SK },
        expect.objectContaining({
          shared_group_id: groupId,
          description: description,
        }),
      );
      const updatedPayload = mockInspirationRepository.update.mock.calls[0][1];
      expect(updatedPayload.source).toBeUndefined();
    });

    it('should successfully share an inspiration with only source (description undefined)', async () => {
      await service.shareInspiration(
        inspirationId,
        groupId,
        sharerUserId,
        undefined,
        source,
      );

      expect(mockInspirationRepository.update).toHaveBeenCalledWith(
        { PK: existingInspiration.PK, SK: existingInspiration.SK },
        expect.objectContaining({
          shared_group_id: groupId,
          source: source,
        }),
      );
      const updatedPayload = mockInspirationRepository.update.mock.calls[0][1];
      expect(updatedPayload.description).toBeUndefined();
    });

    it('should successfully share an inspiration with neither description nor source', async () => {
      await service.shareInspiration(
        inspirationId,
        groupId,
        sharerUserId,
        undefined,
        undefined,
      );

      expect(mockInspirationRepository.update).toHaveBeenCalledWith(
        { PK: existingInspiration.PK, SK: existingInspiration.SK },
        expect.objectContaining({
          shared_group_id: groupId,
        }),
      );
      const updatedPayload = mockInspirationRepository.update.mock.calls[0][1];
      expect(updatedPayload.description).toBeUndefined();
      expect(updatedPayload.source).toBeUndefined();
    });

    it('should throw NotFoundException if inspiration to share is not found', async () => {
      mockInspirationRepository.getInspiration.mockResolvedValue(null);
      await expect(
        service.shareInspiration(
          inspirationId,
          groupId,
          sharerUserId,
          description,
          source,
        ),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw InternalServerErrorException if getInspiration fails', async () => {
      mockInspirationRepository.getInspiration.mockRejectedValue(
        new Error('DB Get Failed'),
      );
      await expect(
        service.shareInspiration(
          inspirationId,
          groupId,
          sharerUserId,
          description,
          source,
        ),
      ).rejects.toThrow(InternalServerErrorException);
    });

    it('should throw InternalServerErrorException if update fails', async () => {
      mockInspirationRepository.update.mockRejectedValue(
        new Error('DB Update Failed'),
      );
      await expect(
        service.shareInspiration(
          inspirationId,
          groupId,
          sharerUserId,
          description,
          source,
        ),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  // --- featureInspiration ---
  describe('featureInspiration', () => {
    const featureInspirationId = 'featureTestId';
    const imageUrl = 'https://example.com/featured.jpg';
    const description = 'Featured description';
    const source = 'Featured source';
    let baseInspirationModel: Inspiration;

    beforeEach(() => {
      baseInspirationModel = {
        id: featureInspirationId,
        user_id: mockUserId, // Assuming mockUserId is the owner
        type: InspirationType.URL,
        status: InspirationStatus.ENABLED,
        created_at: mockDateString,
        updated_at: mockDateString,
        name: 'Feature Me',
        tags: [],
        metadata: { is_indexed: false, token_count: 0 },
        // other necessary fields for Inspiration model
      };

      mockInspirationRepository.update.mockImplementation(
        async (key, payload) =>
          ({
            // Simulate a StoredInspiration being returned by update
            PK: key.PK,
            SK: key.SK,
            id: featureInspirationId,
            user_id: baseInspirationModel.user_id,
            type: baseInspirationModel.type,
            status: baseInspirationModel.status,
            created_at: baseInspirationModel.created_at,
            updated_at: new Date().toISOString(), // Simulate update
            ...payload,
          }) as StoredInspiration,
      );
    });

    it('should successfully feature an inspiration with description and source', async () => {
      await service.featureInspiration(
        baseInspirationModel,
        imageUrl,
        description,
        source,
      );

      expect(mockInspirationRepository.update).toHaveBeenCalledWith(
        {
          PK: InspirationKeys.formatUserKey(baseInspirationModel.user_id),
          SK: InspirationKeys.formatInspirationKey(featureInspirationId),
        },
        {
          is_featured: true,
          image_url: imageUrl,
          description: description,
          source: source,
          updated_at: expect.any(String),
        },
      );
    });

    it('should successfully feature an inspiration with only description (source undefined)', async () => {
      await service.featureInspiration(
        baseInspirationModel,
        imageUrl,
        description,
        undefined,
      );

      expect(mockInspirationRepository.update).toHaveBeenCalledWith(
        {
          PK: InspirationKeys.formatUserKey(baseInspirationModel.user_id),
          SK: InspirationKeys.formatInspirationKey(featureInspirationId),
        },
        expect.objectContaining({
          is_featured: true,
          image_url: imageUrl,
          description: description,
        }),
      );
      const updatedPayload = mockInspirationRepository.update.mock.calls[0][1];
      expect(updatedPayload.source).toBeUndefined();
    });

    it('should successfully feature an inspiration with only source (description undefined)', async () => {
      await service.featureInspiration(
        baseInspirationModel,
        imageUrl,
        undefined,
        source,
      );

      expect(mockInspirationRepository.update).toHaveBeenCalledWith(
        {
          PK: InspirationKeys.formatUserKey(baseInspirationModel.user_id),
          SK: InspirationKeys.formatInspirationKey(featureInspirationId),
        },
        expect.objectContaining({
          is_featured: true,
          image_url: imageUrl,
          source: source,
        }),
      );
      const updatedPayload = mockInspirationRepository.update.mock.calls[0][1];
      expect(updatedPayload.description).toBeUndefined();
    });

    it('should successfully feature an inspiration with neither description nor source', async () => {
      await service.featureInspiration(
        baseInspirationModel,
        imageUrl,
        undefined,
        undefined,
      );

      expect(mockInspirationRepository.update).toHaveBeenCalledWith(
        {
          PK: InspirationKeys.formatUserKey(baseInspirationModel.user_id),
          SK: InspirationKeys.formatInspirationKey(featureInspirationId),
        },
        expect.objectContaining({
          is_featured: true,
          image_url: imageUrl,
        }),
      );
      const updatedPayload = mockInspirationRepository.update.mock.calls[0][1];
      expect(updatedPayload.description).toBeUndefined();
      expect(updatedPayload.source).toBeUndefined();
    });

    it('should throw InternalServerErrorException if update fails', async () => {
      mockInspirationRepository.update.mockRejectedValue(
        new Error('DB Update Failed'),
      );
      await expect(
        service.featureInspiration(
          baseInspirationModel,
          imageUrl,
          description,
          source,
        ),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  // --- unfeatureInspiration ---
  describe('unfeatureInspiration', () => {
    const inspirationToUnfeatureId = 'unfeatureId1';
    let featuredInspirationObject: Inspiration;
    let notFeaturedInspirationObject: Inspiration;

    beforeEach(() => {
      featuredInspirationObject = service['mapToApiModel']({
        ...mockStoredInspirationText,
        id: inspirationToUnfeatureId,
        user_id: mockUserId,
        PK: InspirationKeys.formatUserKey(mockUserId),
        SK: InspirationKeys.formatInspirationKey(inspirationToUnfeatureId),
        is_featured: true,
        image_url: 'https://example.com/image.png',
      });
      notFeaturedInspirationObject = service['mapToApiModel']({
        ...mockStoredInspirationText,
        id: inspirationToUnfeatureId,
        user_id: mockUserId,
        PK: InspirationKeys.formatUserKey(mockUserId),
        SK: InspirationKeys.formatInspirationKey(inspirationToUnfeatureId),
        is_featured: false,
      });
      mockInspirationRepository.updateWithRemove.mockReset();
    });

    it('should unfeature an inspiration successfully and return true', async () => {
      mockInspirationRepository.updateWithRemove.mockResolvedValueOnce(
        {} as StoredInspiration,
      );

      const result = await service.unfeatureInspiration(
        featuredInspirationObject,
      );
      expect(result).toBe(true);

      const expectedRepoKey = {
        PK: InspirationKeys.formatUserKey(mockUserId),
        SK: InspirationKeys.formatInspirationKey(inspirationToUnfeatureId),
      };
      const expectedAttributesToRemove = ['is_featured', 'image_url'];
      expect(mockInspirationRepository.updateWithRemove).toHaveBeenCalledWith(
        expectedRepoKey,
        expect.objectContaining({ updated_at: expect.any(String) }),
        expectedAttributesToRemove,
      );
    });

    it('should return false if inspiration was not featured', async () => {
      const result = await service.unfeatureInspiration(
        notFeaturedInspirationObject,
      );
      expect(result).toBe(false);
      expect(mockInspirationRepository.updateWithRemove).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException if repository updateWithRemove fails with NotFoundException', async () => {
      mockInspirationRepository.updateWithRemove.mockRejectedValueOnce(
        new NotFoundException('Item not found during update'),
      );
      await expect(
        service.unfeatureInspiration(featuredInspirationObject),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw InternalServerErrorException if repository updateWithRemove fails with a generic error', async () => {
      mockInspirationRepository.updateWithRemove.mockRejectedValueOnce(
        new Error('Unfeature update failed'),
      );
      await expect(
        service.unfeatureInspiration(featuredInspirationObject),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  // --- createUrlInspiration ---
  describe('createUrlInspiration', () => {
    const urlData = {
      url: 'https://new.example.com',
      name: 'New URL',
      tags: ['new', 'url'],
    };

    beforeEach(() => {
      // Mock findInspirationsByHash to return empty array (no existing inspirations)
      mockInspirationRepository.findInspirationsByHash.mockResolvedValue([]);
    });

    it('should create and return a URL inspiration', async () => {
      // Mock findInspirationsByHash to return empty array (no existing inspirations)
      mockInspirationRepository.findInspirationsByHash.mockResolvedValueOnce(
        [],
      );

      mockInspirationRepository.create.mockImplementationOnce(
        async (inspiration) => inspiration,
      ); // Echo back the input

      const result = await service.createUrlInspiration(urlData, mockUserId);

      expect(
        mockInspirationRepository.findInspirationsByHash,
      ).toHaveBeenCalledWith(
        mockUserId,
        expect.any(String), // hash
      );
      expect(mockInspirationRepository.create).toHaveBeenCalledTimes(1);
      const createdInspiration = mockInspirationRepository.create.mock
        .calls[0][0] as StoredInspiration;

      expect(createdInspiration.id).toEqual(expect.any(String)); // uuidv4
      expect(createdInspiration.PK).toBe(
        InspirationKeys.formatUserKey(mockUserId),
      );
      expect(createdInspiration.SK).toBe(
        InspirationKeys.formatInspirationKey(createdInspiration.id),
      );
      expect(createdInspiration.type).toBe(InspirationType.URL);
      expect(createdInspiration.user_id).toBe(mockUserId);
      expect(createdInspiration.name).toBe(urlData.name);
      expect(createdInspiration.tags).toEqual(urlData.tags);
      expect(createdInspiration.status).toBe(InspirationStatus.ENABLED);
      expect(createdInspiration.url_metadata?.url).toBe(urlData.url);
      expect(createdInspiration.hash).toEqual(expect.any(String)); // HashUtils is used
      expect(createdInspiration.gsi_3_sk).toBe(
        InspirationKeys.formatHashKey(createdInspiration.hash!),
      );
      expect(createdInspiration.metadata).toEqual({
        token_count: 0,
        is_indexed: false,
      });
      expect(createdInspiration.created_at).toEqual(expect.any(String));
      expect(createdInspiration.updated_at).toEqual(
        createdInspiration.created_at,
      );

      // Check mapped result
      expect(result.id).toBe(createdInspiration.id);
      expect(result.name).toBe(urlData.name);
      expect(result.url_metadata?.url).toBe(urlData.url);
    });

    it('should create with a default name if name is not provided', async () => {
      mockInspirationRepository.create.mockImplementationOnce(
        async (inspiration) => inspiration,
      );
      const urlDataNoName = { url: 'https://noname.example.com' };
      const result = await service.createUrlInspiration(
        urlDataNoName,
        mockUserId,
      );
      expect(mockInspirationRepository.create).toHaveBeenCalledTimes(1);
      const createdInspiration = mockInspirationRepository.create.mock
        .calls[0][0] as StoredInspiration;
      expect(createdInspiration.name).toBe('Untitled URL');
      expect(result.name).toBe('Untitled URL');
    });

    it('should correctly set GSI4 keys if sharedGroupId is provided', async () => {
      mockInspirationRepository.create.mockImplementationOnce(
        async (inspiration) => inspiration,
      );
      const sharedGroupId = 'groupSharedOnCreate';
      await service.createUrlInspiration(urlData, mockUserId, sharedGroupId);
      const createdInspiration = mockInspirationRepository.create.mock
        .calls[0][0] as StoredInspiration;
      expect(createdInspiration.shared_group_id).toBe(sharedGroupId);
      expect(createdInspiration.gsi_4_pk).toBe(`GROUP#${sharedGroupId}`);
      expect(createdInspiration.gsi_4_sk).toBe(
        `INSPIRATION#${createdInspiration.id}`,
      );
    });
  });

  // --- updateUrlInspiration ---
  describe('updateUrlInspiration', () => {
    const urlId = 'url123';
    const originalUrlData: UpdateUrlInspirationData = {
      url: 'https://original.com',
      name: 'Original URL',
      tags: ['original'],
    };
    let existingUrlInspiration: StoredInspiration;

    beforeEach(() => {
      existingUrlInspiration = {
        ...mockStoredInspirationUrl, // Use a base mock
        id: urlId,
        user_id: mockUserId,
        type: InspirationType.URL,
        PK: InspirationKeys.formatUserKey(mockUserId),
        SK: InspirationKeys.formatInspirationKey(urlId),
        url_metadata: { url: originalUrlData.url! },
        name: originalUrlData.name,
        tags: originalUrlData.tags,
        description: 'Original Description', // Add original description
        source: 'Original Source', // Add original source
      };
      mockInspirationRepository.getInspiration.mockResolvedValue(
        existingUrlInspiration,
      );
      mockInspirationRepository.update.mockImplementation(
        async (key, payload) => ({
          ...existingUrlInspiration,
          ...payload,
          updated_at: new Date().toISOString(),
        }),
      );
    });

    it('should update url, name, tags, description, and source', async () => {
      const updateDto: UpdateUrlInspirationData = {
        url: 'https://updated.com',
        name: 'Updated URL',
        tags: ['updated', 'tag'],
        description: 'Updated Description',
        source: 'Updated Source',
      };

      // Create an Inspiration object to pass to the method
      const inspirationToUpdate: Inspiration = service['mapToApiModel'](
        existingUrlInspiration,
      );

      await service.updateUrlInspiration(inspirationToUpdate, updateDto);
      expect(mockInspirationRepository.update).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          url_metadata: { url: updateDto.url },
          name: updateDto.name,
          name_lowercase: updateDto.name?.toLowerCase(),
          tags: updateDto.tags,
          description: updateDto.description,
          source: updateDto.source,
          hash: expect.any(String), // Hash should be recalculated
          gsi_3_sk: expect.any(String),
        }),
      );
    });

    it('should only update provided fields (e.g., only description)', async () => {
      const updateDto: UpdateUrlInspirationData = {
        description: 'New Description Only',
      };

      // Create an Inspiration object to pass to the method
      const inspirationToUpdate: Inspiration = service['mapToApiModel'](
        existingUrlInspiration,
      );

      await service.updateUrlInspiration(inspirationToUpdate, updateDto);
      const updatePayload = mockInspirationRepository.update.mock.calls[0][1];
      expect(updatePayload.description).toBe('New Description Only');
      expect(updatePayload.name).toBeUndefined(); // Name should not be in payload
      expect(updatePayload.url_metadata).toBeUndefined(); // URL should not be in payload
      expect(updatePayload.tags).toBeUndefined();
      expect(updatePayload.source).toBeUndefined();
      expect(updatePayload.hash).toBeUndefined(); // Hash should not change if URL not changed
    });

    it('should not update description or source if not provided', async () => {
      const updateDto: UpdateUrlInspirationData = { name: 'Name Change Only' };

      // Create an Inspiration object to pass to the method
      const inspirationToUpdate: Inspiration = service['mapToApiModel'](
        existingUrlInspiration,
      );

      await service.updateUrlInspiration(inspirationToUpdate, updateDto);
      const updatePayload = mockInspirationRepository.update.mock.calls[0][1];
      expect(updatePayload.name).toBe('Name Change Only');
      expect(updatePayload.description).toBeUndefined(); // Should not be sent to DB if not in DTO
      expect(updatePayload.source).toBeUndefined(); // Should not be sent to DB if not in DTO
    });
  });

  // --- createTextInspiration ---
  describe('createTextInspiration', () => {
    const textData = {
      text: 'This is new sample text.',
      name: 'New Text Note',
      tags: ['new', 'text'],
    };

    it('should create and return a Text inspiration', async () => {
      mockInspirationRepository.create.mockImplementationOnce(
        async (inspiration) => inspiration,
      );
      const result = await service.createTextInspiration(
        textData,
        mockUserId,
        mockUserId,
      );
      expect(mockInspirationRepository.create).toHaveBeenCalledTimes(1);
      const createdInspiration = mockInspirationRepository.create.mock
        .calls[0][0] as StoredInspiration;

      expect(createdInspiration.type).toBe(InspirationType.TEXT);
      expect(createdInspiration.name).toBe(textData.name);
      expect(createdInspiration.text_metadata?.text).toBe(textData.text);
      expect(createdInspiration.hash).toEqual(expect.any(String));
      expect(result.name).toBe(textData.name);
      expect(result.text_metadata?.text).toBe(textData.text);
    });

    it('should create with a default name if name is not provided for text', async () => {
      mockInspirationRepository.create.mockImplementationOnce(
        async (inspiration) => inspiration,
      );
      const textDataNoName = { text: 'Some anonymous text.' };
      const result = await service.createTextInspiration(
        textDataNoName,
        mockUserId,
        mockUserId,
      );
      expect(mockInspirationRepository.create.mock.calls[0][0].name).toBe(
        'Untitled text',
      );
      expect(result.name).toBe('Untitled text');
    });

    it('should create text inspiration with rule_id and call ruleInteractionsService', async () => {
      const textDataWithRule = {
        text: 'Text with rule reference',
        name: 'Rule-based Text',
        tags: ['rule', 'text'],
        rule_id: 'rule-123',
        rule_type: RuleType.SIGMA,
      };

      mockInspirationRepository.create.mockImplementationOnce(
        async (inspiration) => inspiration,
      );
      mockRuleInteractionsService.createInteraction.mockResolvedValueOnce(
        undefined,
      );

      const result = await service.createTextInspiration(
        textDataWithRule,
        mockUserId,
        mockUserId,
      );

      expect(mockInspirationRepository.create).toHaveBeenCalledTimes(1);
      const createdInspiration = mockInspirationRepository.create.mock
        .calls[0][0] as StoredInspiration;

      expect(createdInspiration.rule_id).toBe(textDataWithRule.rule_id);
      expect(createdInspiration.rule_type).toBe(textDataWithRule.rule_type);

      expect(
        mockRuleInteractionsService.createInteraction,
      ).toHaveBeenCalledWith(
        mockUserId,
        textDataWithRule.rule_id,
        RuleInteractionType.ADD_AS_INSPIRATION,
      );

      expect(result.rule_id).toBe(textDataWithRule.rule_id);
      expect(result.rule_type).toBe(textDataWithRule.rule_type);
    });

    it('should create text inspiration without rule_id and not call ruleInteractionsService', async () => {
      const textDataWithoutRule = {
        text: 'Text without rule reference',
        name: 'Regular Text',
        tags: ['text'],
      };

      mockInspirationRepository.create.mockImplementationOnce(
        async (inspiration) => inspiration,
      );

      const result = await service.createTextInspiration(
        textDataWithoutRule,
        mockUserId,
        mockUserId,
      );

      expect(mockInspirationRepository.create).toHaveBeenCalledTimes(1);
      expect(
        mockRuleInteractionsService.createInteraction,
      ).not.toHaveBeenCalled();
      expect(result.rule_id).toBeUndefined();
    });
  });

  // --- updateTextInspiration ---
  describe('updateTextInspiration', () => {
    const textId = 'text123';
    const originalTextData: UpdateTextInspirationData = {
      text: 'Original text content',
      name: 'Original Text',
      rule_type: RuleType.SIGMA,
      description: 'Original text description',
      source: 'Original text source',
    };
    let existingTextInspiration: StoredInspiration;

    beforeEach(() => {
      existingTextInspiration = {
        ...mockStoredInspirationText,
        id: textId,
        user_id: mockUserId,
        type: InspirationType.TEXT,
        PK: InspirationKeys.formatUserKey(mockUserId),
        SK: InspirationKeys.formatInspirationKey(textId),
        text_metadata: { text: originalTextData.text! },
        name: originalTextData.name,
        rule_type: originalTextData.rule_type,
        description: originalTextData.description,
        source: originalTextData.source,
      };
      mockInspirationRepository.getInspiration.mockResolvedValue(
        existingTextInspiration,
      );
      mockInspirationRepository.update.mockImplementation(
        async (key, payload) => ({
          ...existingTextInspiration,
          ...payload,
          updated_at: new Date().toISOString(),
        }),
      );
    });

    it('should update text, name, rule_type, description, and source', async () => {
      const updateDto: UpdateTextInspirationData = {
        text: 'Updated text',
        name: 'Updated Text Name',
        rule_type: RuleType.SIGMA,
        description: 'Updated text description',
        source: 'Updated text source',
      };

      // Create an Inspiration object to pass to the method
      const inspirationToUpdate: Inspiration = service['mapToApiModel'](
        existingTextInspiration,
      );

      await service.updateTextInspiration(
        inspirationToUpdate,
        updateDto,
        mockUserId,
      );
      expect(mockInspirationRepository.update).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          text_metadata: { text: updateDto.text },
          name: updateDto.name,
          rule_type: updateDto.rule_type,
          description: updateDto.description,
          source: updateDto.source,
          hash: expect.any(String), // Hash should be recalculated
        }),
      );
    });

    it('should only update source if only source is provided', async () => {
      const updateDto: UpdateTextInspirationData = {
        source: 'New Source Only',
      };

      // Create an Inspiration object to pass to the method
      const inspirationToUpdate: Inspiration = service['mapToApiModel'](
        existingTextInspiration,
      );

      await service.updateTextInspiration(
        inspirationToUpdate,
        updateDto,
        mockUserId,
      );
      const updatePayload = mockInspirationRepository.update.mock.calls[0][1];
      expect(updatePayload.source).toBe('New Source Only');
      expect(updatePayload.description).toBeUndefined();
      expect(updatePayload.name).toBeUndefined();
      expect(updatePayload.text_metadata).toBeUndefined();
      expect(updatePayload.hash).toBeUndefined();
    });

    it('should update rule_id and call ruleInteractionsService when rule_id changes', async () => {
      const updateDto: UpdateTextInspirationData = {
        rule_id: 'new-rule-456',
        rule_type: RuleType.SIGMA,
      };

      // Create an Inspiration object with existing rule_id
      const inspirationWithRule: Inspiration = service['mapToApiModel']({
        ...existingTextInspiration,
        rule_id: 'old-rule-123',
      });

      mockRuleInteractionsService.createInteraction.mockResolvedValueOnce(
        undefined,
      );
      mockRuleInteractionsService.deleteInteraction.mockResolvedValueOnce(
        undefined,
      );

      await service.updateTextInspiration(
        inspirationWithRule,
        updateDto,
        mockUserId,
      );

      expect(
        mockRuleInteractionsService.deleteInteraction,
      ).toHaveBeenCalledWith(
        mockUserId,
        'old-rule-123',
        RuleInteractionType.ADD_AS_INSPIRATION,
      );

      expect(
        mockRuleInteractionsService.createInteraction,
      ).toHaveBeenCalledWith(
        mockUserId,
        updateDto.rule_id,
        RuleInteractionType.ADD_AS_INSPIRATION,
      );

      const updatePayload = mockInspirationRepository.update.mock.calls[0][1];
      expect(updatePayload.rule_id).toBe(updateDto.rule_id);
      expect(updatePayload.rule_type).toBe(updateDto.rule_type);
    });

    it('should not call ruleInteractionsService when rule_id stays the same', async () => {
      const existingRuleId = 'existing-rule-123';
      const updateDto: UpdateTextInspirationData = {
        rule_id: existingRuleId, // Same as existing
        name: 'Updated name',
      };

      // Create an Inspiration object with existing rule_id
      const inspirationWithRule: Inspiration = service['mapToApiModel']({
        ...existingTextInspiration,
        rule_id: existingRuleId,
      });

      await service.updateTextInspiration(
        inspirationWithRule,
        updateDto,
        mockUserId,
      );

      expect(
        mockRuleInteractionsService.createInteraction,
      ).not.toHaveBeenCalled();

      const updatePayload = mockInspirationRepository.update.mock.calls[0][1];
      // rule_id should not be in the update payload since it didn't change
      expect(updatePayload.rule_id).toBeUndefined();
      expect(updatePayload.name).toBe(updateDto.name);
    });

    it('should add rule_id when updating from no rule to having a rule', async () => {
      const updateDto: UpdateTextInspirationData = {
        rule_id: 'new-rule-789',
        rule_type: RuleType.SIGMA,
      };

      // Create an Inspiration object without existing rule_id
      const inspirationWithoutRule: Inspiration = service['mapToApiModel'](
        existingTextInspiration,
      ); // No rule_id

      mockRuleInteractionsService.createInteraction.mockResolvedValueOnce(
        undefined,
      );

      await service.updateTextInspiration(
        inspirationWithoutRule,
        updateDto,
        mockUserId,
      );

      expect(
        mockRuleInteractionsService.createInteraction,
      ).toHaveBeenCalledWith(
        mockUserId,
        updateDto.rule_id,
        RuleInteractionType.ADD_AS_INSPIRATION,
      );

      const updatePayload = mockInspirationRepository.update.mock.calls[0][1];
      expect(updatePayload.rule_id).toBe(updateDto.rule_id);
      expect(updatePayload.rule_type).toBe(updateDto.rule_type);
    });
  });

  // --- createInstructionInspiration ---
  describe('createInstructionInspiration', () => {
    const instructionData = {
      instruction: 'Analyze the provided logs for anomalies.',
      name: 'New Instruction Task',
      tags: ['new', 'instruction'],
    };

    it('should create and return an Instruction inspiration', async () => {
      mockInspirationRepository.create.mockImplementationOnce(
        async (inspiration) => inspiration,
      );
      const result = await service.createInstructionInspiration(
        instructionData,
        mockUserId,
      );
      expect(mockInspirationRepository.create).toHaveBeenCalledTimes(1);
      const createdInspiration = mockInspirationRepository.create.mock
        .calls[0][0] as StoredInspiration;

      expect(createdInspiration.type).toBe(InspirationType.INSTRUCTION);
      expect(createdInspiration.name).toBe(instructionData.name);
      expect(createdInspiration.instruction_metadata?.instruction).toBe(
        instructionData.instruction,
      );
      expect(createdInspiration.hash).toEqual(expect.any(String));
      expect(result.name).toBe(instructionData.name);
      expect(result.instruction_metadata?.instruction).toBe(
        instructionData.instruction,
      );
    });

    it('should create with a default name if name is not provided for instruction', async () => {
      mockInspirationRepository.create.mockImplementationOnce(
        async (inspiration) => inspiration,
      );
      const instructionDataNoName = { instruction: 'Follow these steps.' };
      const result = await service.createInstructionInspiration(
        instructionDataNoName,
        mockUserId,
      );
      expect(mockInspirationRepository.create.mock.calls[0][0].name).toBe(
        'Untitled instruction',
      );
      expect(result.name).toBe('Untitled instruction');
    });
  });

  // --- updateInstructionInspiration ---
  describe('updateInstructionInspiration', () => {
    const instructionId = 'instr123';
    const originalInstructionData: UpdateInstructionInspirationData = {
      instruction: 'Do this first',
      name: 'Original Instruction',
      description: 'Original instruction description',
      source: 'Original instruction source',
    };
    let existingInstructionInspiration: StoredInspiration;

    beforeEach(() => {
      existingInstructionInspiration = {
        ...mockStoredInspirationText, // Base it on some mock
        id: instructionId,
        user_id: mockUserId,
        type: InspirationType.INSTRUCTION,
        PK: InspirationKeys.formatUserKey(mockUserId),
        SK: InspirationKeys.formatInspirationKey(instructionId),
        instruction_metadata: {
          instruction: originalInstructionData.instruction!,
        },
        name: originalInstructionData.name,
        description: originalInstructionData.description,
        source: originalInstructionData.source,
      };
      mockInspirationRepository.getInspiration.mockResolvedValue(
        existingInstructionInspiration,
      );
      mockInspirationRepository.update.mockImplementation(
        async (key, payload) => ({
          ...existingInstructionInspiration,
          ...payload,
          updated_at: new Date().toISOString(),
        }),
      );
    });

    it('should update instruction, name, description, and source', async () => {
      const updateDto: UpdateInstructionInspirationData = {
        instruction: 'Do this updated thing',
        name: 'Updated Instruction Name',
        description: 'Updated instruction description',
        source: 'Updated instruction source',
      };

      // Create an Inspiration object to pass to the method
      const inspirationToUpdate: Inspiration = service['mapToApiModel'](
        existingInstructionInspiration,
      );

      await service.updateInstructionInspiration(
        inspirationToUpdate,
        updateDto,
      );
      expect(mockInspirationRepository.update).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          instruction_metadata: { instruction: updateDto.instruction },
          name: updateDto.name,
          description: updateDto.description,
          source: updateDto.source,
          hash: expect.any(String), // Hash should be recalculated
        }),
      );
    });

    it('should only update name and tags if other fields are not provided', async () => {
      const updateDto: UpdateInstructionInspirationData = {
        name: 'Name Update Only',
        tags: ['newtag'],
      };

      // Create an Inspiration object to pass to the method
      const inspirationToUpdate: Inspiration = service['mapToApiModel'](
        existingInstructionInspiration,
      );

      await service.updateInstructionInspiration(
        inspirationToUpdate,
        updateDto,
      );
      const updatePayload = mockInspirationRepository.update.mock.calls[0][1];
      expect(updatePayload.name).toBe('Name Update Only');
      expect(updatePayload.tags).toEqual(['newtag']);
      expect(updatePayload.description).toBeUndefined();
      expect(updatePayload.source).toBeUndefined();
      expect(updatePayload.instruction_metadata).toBeUndefined();
      expect(updatePayload.hash).toBeUndefined();
    });
  });

  // --- createFileInspiration ---
  describe('createFileInspiration', () => {
    const mockFile: ProcessedFile = {
      originalname: 'test-file.pdf',
      mimetype: 'application/pdf',
      buffer: Buffer.from('this is a test pdf file content'),
      size: 1024,
    };
    const mockFileKey = `user/${mockUserId}/files/some-uuid/test-file.pdf`; // Example file key
    const fileMetadataDto: FileInspirationMetadataDto = {
      name: 'My PDF Document',
      tags: ['file', 'pdf'],
    };

    beforeEach(() => {
      // Mock S3Service.uploadFile to resolve successfully
      mockS3Service.uploadFile.mockResolvedValue({} as any); // Return type might be more specific, but {} works for now
      // Mock repository.create to echo back the created inspiration
      mockInspirationRepository.create.mockImplementation(
        async (inspiration) => inspiration,
      );
      // Mock configService to return the bucket name
      // Already mocked globally, but ensure it returns the expected value for this test if specific override needed.
      // For now, the global mock `mockConfigService.get.mockImplementation((key) => { if (key === 's3.inspirationsBucket')...`
      // is assumed to be active and returning 'inspirations' based on the constructor this.s3Bucket logic.
    });

    it('should upload file to S3 and create a File inspiration', async () => {
      const result = await service.createFileInspiration(
        mockFile,
        mockFileKey,
        fileMetadataDto,
        mockUserId,
      );

      expect(mockS3Service.uploadFile).toHaveBeenCalledWith(
        'inspirations', // from this.s3Bucket in service, derived from config
        mockFileKey,
        mockFile.buffer,
        mockFile.mimetype,
      );
      expect(mockInspirationRepository.create).toHaveBeenCalledTimes(1);
      const createdInspiration = mockInspirationRepository.create.mock
        .calls[0][0] as StoredInspiration;

      expect(createdInspiration.type).toBe(InspirationType.FILE);
      expect(createdInspiration.name).toBe(fileMetadataDto.name);
      expect(createdInspiration.tags).toEqual(fileMetadataDto.tags);
      expect(createdInspiration.file_metadata?.s3_path).toBe(
        `s3://inspirations/${mockFileKey}`,
      );
      expect(createdInspiration.file_metadata?.original_file_name).toBe(
        mockFile.originalname,
      );
      expect(createdInspiration.file_metadata?.mime_type).toBe(
        mockFile.mimetype,
      );
      expect(createdInspiration.file_metadata?.file_size).toBe(mockFile.size);
      expect(createdInspiration.hash).toEqual(expect.any(String));

      expect(result.name).toBe(fileMetadataDto.name);
      expect(result.file_metadata?.s3_path).toContain(mockFileKey);
    });

    it('should use originalname if DTO name is not provided for file', async () => {
      await service.createFileInspiration(
        mockFile,
        mockFileKey,
        { tags: ['notags'] },
        mockUserId,
      ); // No name in DTO
      const createdInspiration = mockInspirationRepository.create.mock
        .calls[0][0] as StoredInspiration;
      expect(createdInspiration.name).toBe(mockFile.originalname);
    });

    it('should throw InternalServerErrorException if S3 upload fails', async () => {
      mockS3Service.uploadFile.mockRejectedValueOnce(
        new Error('S3 Upload Failed'),
      );
      await expect(
        service.createFileInspiration(
          mockFile,
          mockFileKey,
          fileMetadataDto,
          mockUserId,
        ),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  // --- updateFileInspiration ---
  describe('updateFileInspiration', () => {
    const fileId = 'file123';
    const originalFileData: UpdateFileInspirationData = {
      name: 'Original File.txt',
      tags: ['original', 'file'],
      description: 'Original file description',
      source: 'Original file source',
    };
    let existingFileInspiration: StoredInspiration;

    beforeEach(() => {
      existingFileInspiration = {
        ...mockStoredInspirationText, // Base it on some mock, ensure type is FILE
        id: fileId,
        user_id: mockUserId,
        type: InspirationType.FILE,
        PK: InspirationKeys.formatUserKey(mockUserId),
        SK: InspirationKeys.formatInspirationKey(fileId),
        name: originalFileData.name,
        tags: originalFileData.tags,
        description: originalFileData.description,
        source: originalFileData.source,
        file_metadata: {
          s3_path: 's3://bucket/file.txt',
          original_file_name: 'file.txt',
        },
      };
      mockInspirationRepository.getInspiration.mockResolvedValue(
        existingFileInspiration,
      );
      mockInspirationRepository.update.mockImplementation(
        async (key, payload) => ({
          ...existingFileInspiration,
          ...payload,
          updated_at: new Date().toISOString(),
        }),
      );
    });

    it('should update name, tags, description, and source for a file', async () => {
      const updateDto: UpdateFileInspirationData = {
        name: 'Updated File.docx',
        tags: ['updated', 'document'],
        description: 'Updated file description',
        source: 'Updated file source',
      };

      // Create an Inspiration object to pass to the method
      const inspirationToUpdate: Inspiration = service['mapToApiModel'](
        existingFileInspiration,
      );

      await service.updateFileInspiration(inspirationToUpdate, updateDto);
      expect(mockInspirationRepository.update).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          name: updateDto.name,
          tags: updateDto.tags,
          description: updateDto.description,
          source: updateDto.source,
          // Hash is NOT updated for file metadata changes like name/tags/desc/source
        }),
      );
      const updatePayload = mockInspirationRepository.update.mock.calls[0][1];
      expect(updatePayload.hash).toBeUndefined(); // No content change, no hash update
    });

    it('should only update description and source if only they are provided', async () => {
      const updateDto: UpdateFileInspirationData = {
        description: 'Desc Update',
        source: 'Source Update',
      };

      // Create an Inspiration object to pass to the method
      const inspirationToUpdate: Inspiration = service['mapToApiModel'](
        existingFileInspiration,
      );

      await service.updateFileInspiration(inspirationToUpdate, updateDto);
      const updatePayload = mockInspirationRepository.update.mock.calls[0][1];
      expect(updatePayload.description).toBe('Desc Update');
      expect(updatePayload.source).toBe('Source Update');
      expect(updatePayload.name).toBeUndefined();
      expect(updatePayload.tags).toBeUndefined();
      expect(updatePayload.hash).toBeUndefined();
    });
  });

  // --- listMitreInspirations ---
  describe('listMitreInspirations', () => {
    const mitreUserId = 'mitre'; // As defined in the service
    const mockMitreInspiration: StoredInspiration = {
      ...mockStoredInspirationText, // Base it on some mock
      id: 'mitre1',
      PK: InspirationKeys.formatUserKey(mitreUserId),
      SK: InspirationKeys.formatInspirationKey('mitre1'),
      user_id: mitreUserId,
      type: InspirationType.MITRE,
      name: 'MITRE Technique T1234',
      mitre_metadata: {
        id: 'T1234',
        name: 'Technique Name',
        description: '...',
        type: 'technique',
      },
      // Ensure other GSI keys are appropriate if they depend on type/status for filtering
      gsi_1_pk: InspirationKeys.getUserTypeKey(
        mitreUserId,
        InspirationType.MITRE,
      ),
      gsi_1_sk: InspirationKeys.getStatusCreatedAtKey(
        InspirationStatus.ENABLED,
        mockDateString,
      ),
    };
    const anotherMitreInspiration: StoredInspiration = {
      ...mockStoredInspirationText,
      id: 'mitre2',
      PK: InspirationKeys.formatUserKey(mitreUserId),
      SK: InspirationKeys.formatInspirationKey('mitre2'),
      user_id: mitreUserId,
      type: InspirationType.MITRE,
      name: 'MITRE Technique T5678',
      mitre_metadata: {
        id: 'T5678',
        name: 'Another Technique',
        description: '...',
        type: 'technique',
      },
      gsi_1_pk: InspirationKeys.getUserTypeKey(
        mitreUserId,
        InspirationType.MITRE,
      ),
      gsi_1_sk: InspirationKeys.getStatusCreatedAtKey(
        InspirationStatus.ENABLED,
        new Date(mockDate.getTime() + 1000).toISOString(),
      ),
    };

    it('should list MITRE inspirations using fixed user ID and MITRE type', async () => {
      mockInspirationRepository.getInspirationsByUser.mockResolvedValueOnce([
        mockMitreInspiration,
        anotherMitreInspiration,
        { ...mockStoredInspirationUrl, user_id: mitreUserId }, // A non-MITRE type under mitre user, should be filtered out
      ]);

      const query: ListInspirationsQueryDto = { page: 1, size: 10 };
      const result = await service.listMitreInspirations(
        query,
        'any-external-user-id',
      ); // External user ID is ignored

      expect(
        mockInspirationRepository.getInspirationsByUser,
      ).toHaveBeenCalledWith(mitreUserId);
      expect(result.items).toHaveLength(2); // Only MITRE types should remain
      expect(result.total).toBe(2);
      expect(result.items[0].type).toBe(InspirationType.MITRE);
      expect(result.items[1].type).toBe(InspirationType.MITRE);
      expect(
        result.items.every((item) => item.type === InspirationType.MITRE),
      ).toBe(true);
    });

    it('should apply further filters like name to MITRE results', async () => {
      mockInspirationRepository.getInspirationsByUser.mockResolvedValueOnce([
        mockMitreInspiration,
        anotherMitreInspiration,
      ]);
      const query: ListInspirationsQueryDto = { name: 'T1234' }; // Filter by name
      const result = await service.listMitreInspirations(query, 'any-user');
      expect(result.items).toHaveLength(1);
      expect(result.items[0].id).toBe('mitre1');
    });

    it('should handle repository errors for MITRE inspirations', async () => {
      mockInspirationRepository.getInspirationsByUser.mockRejectedValueOnce(
        new Error('MITRE repo failed'),
      );
      const query: ListInspirationsQueryDto = {};
      await expect(
        service.listMitreInspirations(query, 'any-user'),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  // --- getInspirationsByHash ---
  describe('getInspirationsByHash', () => {
    const hashToSearch = 'abcdef123456';
    const mockHashedItem1: StoredInspiration = {
      ...mockStoredInspirationText,
      id: 'hashMatch1',
      hash: hashToSearch,
      created_at: new Date('2023-01-02T00:00:00.000Z').toISOString(), // Newer
    };
    const mockHashedItem2: StoredInspiration = {
      ...mockStoredInspirationUrl,
      id: 'hashMatch2',
      hash: hashToSearch,
      created_at: new Date('2023-01-01T00:00:00.000Z').toISOString(), // Older
    };

    it('should retrieve inspirations by hash and sort them by creation date (newest first)', async () => {
      // Ensure the items have distinctly parseable and comparable date strings.
      const itemOlder = {
        ...mockStoredInspirationUrl,
        id: 'hashMatch2',
        hash: hashToSearch,
        created_at: '2023-01-01T00:00:00.000Z',
      };
      const itemNewer = {
        ...mockStoredInspirationText,
        id: 'hashMatch1',
        hash: hashToSearch,
        created_at: '2023-01-02T00:00:00.000Z',
      };

      mockInspirationRepository.findInspirationsByHash.mockResolvedValueOnce([
        itemOlder,
        itemNewer,
      ]); // Provide in specific order
      const result = await service.getInspirationsByHash(
        hashToSearch,
        mockUserId,
      );

      expect(
        mockInspirationRepository.findInspirationsByHash,
      ).toHaveBeenCalledWith(mockUserId, hashToSearch);
      expect(result).toHaveLength(2);
      // Service sorts newest first: itemNewer (2023-01-02) should be result[0]
      expect(result[0].id).toBe('hashMatch1');
      expect(result[1].id).toBe('hashMatch2');
      expect(result[0].hash).toBe(hashToSearch);
    });

    it('should return empty array if no inspirations match the hash', async () => {
      mockInspirationRepository.findInspirationsByHash.mockResolvedValueOnce(
        [],
      );
      const result = await service.getInspirationsByHash(
        hashToSearch,
        mockUserId,
      );
      expect(result).toEqual([]);
    });

    it('should handle repository errors for getInspirationsByHash', async () => {
      mockInspirationRepository.findInspirationsByHash.mockRejectedValueOnce(
        new Error('Hash repo failed'),
      );
      await expect(
        service.getInspirationsByHash(hashToSearch, mockUserId),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });
});
