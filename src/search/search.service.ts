import {
  Injectable,
  Logger,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { RuleSearchRepository } from '../opensearch/repositories/rule.repository';
import { OpenSearchService } from '../opensearch/opensearch.service';
import { OpenSearchConfigService } from '../opensearch/config/opensearch.config';
import { QueryUtilityService } from './query-builders/query.utility';
import { BasicSearchQueryBuilder } from './query-builders/basic-search.query-builder';
import { AdvancedSearchQueryBuilder } from './query-builders/advanced-search.query-builder';
import { SimilarRulesQueryBuilder } from './query-builders/similar-rules.query-builder';
import { SuggestQueryBuilder } from './query-builders/suggest.query-builder';
import { SearchFiltersDto } from './dtos/search-filters.dto';
import { SearchOptionsDto } from './dtos/search-options.dto';
import { SortField } from './models/sort-field.enum';
import { SortOrder } from './models/sort-order.enum';
import { PaginationDirection } from './models/pagination-direction.enum';
import { PaginationMode } from './models/pagination-mode.enum';
import { AdvancedSearchQueryDto } from './dtos/search-query.dto';
import {
  SearchResponseDto,
  SearchSuggestionsResponseDto,
  SearchMetadataResponseDto,
} from './dtos/search-response.dto';
import {
  SimilarRulesQueryDto,
  SimilarRulesResponseDto,
} from './dtos/similar-rules.dto';
import {
  PageSearchOptionsDto,
  PagedSearchResponseDto,
} from './dtos/page-search-options.dto';
import * as crypto from 'crypto';
import { RuleIndexSchema } from '../opensearch/schemas/rule.schema';
import { RuleStatus, RuleType } from '../rules/models/rule.model';
import {
  OpenSearchResponse,
  OpenSearchHit,
  OpenSearchGetResponse,
  RuleSource,
  OpenSearchExplanation,
  OpenSearchQueryClause,
  OpenSearchSortClause,
} from './models/opensearch-types';
import { SimilarRulesFieldValues, SearchCursor } from './models/search-types';

/**
 * Service for handling search operations
 */
@Injectable()
export class SearchService {
  private readonly logger = new Logger(SearchService.name);
  private readonly indexSchema: RuleIndexSchema;

  constructor(
    private readonly ruleSearchRepository: RuleSearchRepository,
    private readonly openSearchService: OpenSearchService,
    private readonly configService: OpenSearchConfigService,
    private readonly queryUtilityService: QueryUtilityService,
    private readonly basicSearchQueryBuilder: BasicSearchQueryBuilder,
    private readonly advancedSearchQueryBuilder: AdvancedSearchQueryBuilder,
    private readonly similarRulesQueryBuilder: SimilarRulesQueryBuilder,
    private readonly suggestQueryBuilder: SuggestQueryBuilder,
  ) {
    this.indexSchema = new RuleIndexSchema(this.configService);
  }

  /**
   * Check if any filters are set
   * @param filters Search filters
   * @returns true if any filter is set, false otherwise
   */
  private hasActiveFilters(filters?: SearchFiltersDto): boolean {
    if (!filters) {
      this.logger.debug('No filters object provided');
      return false;
    }

    // Log the raw filters object
    this.logger.debug('Raw filters:', JSON.stringify(filters));

    // Check each filter array/value
    const checks = {
      owner_ids:
        Array.isArray(filters.owner_ids) && filters.owner_ids.length > 0,
      rule_types:
        Array.isArray(filters.rule_types) && filters.rule_types.length > 0,
      statuses: Array.isArray(filters.statuses) && filters.statuses.length > 0,
      stages: Array.isArray(filters.stages) && filters.stages.length > 0,
      created_after: !!filters.created_after,
      created_before: !!filters.created_before,
      published_after: !!filters.published_after,
      published_before: !!filters.published_before,
      updated_after: !!filters.updated_after,
      updated_before: !!filters.updated_before,
      metadata_date_after: !!filters.metadata_date_after,
      metadata_date_before: !!filters.metadata_date_before,
      metadata_modified_after: !!filters.metadata_modified_after,
      metadata_modified_before: !!filters.metadata_modified_before,
      created_by:
        Array.isArray(filters.created_by) && filters.created_by.length > 0,
      contributors:
        Array.isArray(filters.contributors) && filters.contributors.length > 0,
      authors: Array.isArray(filters.authors) && filters.authors.length > 0,
      severities:
        Array.isArray(filters.severities) && filters.severities.length > 0,
      mitre_tactics:
        Array.isArray(filters.mitre_tactics) &&
        filters.mitre_tactics.length > 0,
      mitre_techniques:
        Array.isArray(filters.mitre_techniques) &&
        filters.mitre_techniques.length > 0,
      ids: Array.isArray(filters.ids) && filters.ids.length > 0,
    };

    // Log individual checks
    this.logger.debug('Filter checks:', checks);

    // Check if any filter is active
    const hasFilters = Object.values(checks).some((value) => value);
    this.logger.debug('Has active filters:', hasFilters);

    return hasFilters;
  }

  /**
   * Perform basic search using cursor-based pagination.
   * This is the underlying method used by cursor-mode paged search.
   */
  async search(
    query?: string,
    filters?: SearchFiltersDto,
    options?: SearchOptionsDto,
  ): Promise<SearchResponseDto> {
    this.logger.debug('Cursor-based search called with:', {
      query,
      filters: JSON.stringify(filters),
      options: JSON.stringify(options),
    });

    // Validate date filters
    if (filters) {
      const dateFields = [
        'created_after',
        'created_before',
        'published_after',
        'published_before',
        'updated_after',
        'updated_before',
      ];
      for (const field of dateFields) {
        const value = filters[field as keyof SearchFiltersDto] as
          | string
          | undefined;
        if (value && !this.queryUtilityService.isValidDateString(value)) {
          throw new BadRequestException(`Invalid date format for ${field}`);
        }
      }
    }

    const hasFilters = this.hasActiveFilters(filters);
    this.logger.debug('Has active filters:', hasFilters);

    const processedQuery = query
      ? this.queryUtilityService.preprocessQuery(query)
      : undefined;

    const {
      size = 20,
      sort_by = SortField.RELEVANCE,
      sort_order = SortOrder.DESC,
      cursor,
      direction = PaginationDirection.FORWARD,
    } = options || {};

    // Validate cursor if provided
    let parsedCursor: SearchCursor | undefined;
    if (cursor) {
      parsedCursor = this.validateCursor(cursor, sort_by);
    }

    // Get sort configuration with direction
    const sort = this.getSortConfig(sort_by, sort_order, direction);

    // Get search_after values from cursor
    const searchAfter = this.getSearchAfter(parsedCursor);

    // Options for the builder
    const builderOptions = {
      size: size + 1, // SearchService requests one extra for cursor pagination
      sort_config: sort,
      search_after: searchAfter,
    };

    const searchBody = this.basicSearchQueryBuilder.build(
      processedQuery,
      filters,
      builderOptions,
    );
    // searchBody now comes fully formed from the builder, including _source and highlight

    this.logger.debug('Search body:', JSON.stringify(searchBody));

    const response = (await this.openSearchService.search(
      this.indexSchema.indexAlias,
      searchBody,
    )) as OpenSearchResponse<RuleSource>;

    // Format response using cursor logic
    const formattedResponse = this.formatSearchResponse(
      response,
      options || {},
      cursor,
    );

    return formattedResponse;
  }

  /**
   * Perform advanced search (remains largely the same, assumes cursor pagination)
   * @param query Advanced search query
   * @returns Paged search results
   */
  async advancedSearch(
    query: AdvancedSearchQueryDto,
  ): Promise<PagedSearchResponseDto> {
    try {
      const { options = {} } = query;

      this.logger.debug(
        `Value from DTO query.options.pagination_mode: ${query.options?.pagination_mode}, Type: ${typeof query.options?.pagination_mode}`,
      );

      const {
        size = 20,
        sort_by = SortField.RELEVANCE,
        sort_order = SortOrder.DESC,
        cursor,
        direction = PaginationDirection.FORWARD,
        page = 1,
        pagination_mode = PaginationMode.OFFSET,
      } = options;

      this.logger.debug(
        `Resolved Advanced Search Params -- Page: ${page}, Size: ${size}, Mode: ${pagination_mode}, Cursor: ${cursor}, SortBy: ${sort_by}, SortOrder: ${sort_order}`,
      );

      const coreQuery = this.buildAdvancedSearchQuery(query);

      if (pagination_mode === PaginationMode.CURSOR) {
        // --- Cursor Pagination ---
        this.logger.debug(
          `Performing advanced cursor-based search (cursor: ${cursor ? 'provided' : 'none'})`,
        );

        // Validate cursor if provided
        let parsedCursor: SearchCursor | undefined;
        if (cursor) {
          parsedCursor = this.validateCursor(cursor, sort_by);
        }

        const sort = this.getSortConfig(sort_by, sort_order, direction);
        const searchAfter = this.getSearchAfter(parsedCursor);

        const searchBody = {
          query: coreQuery,
          sort,
          size: size + 1,
          ...(searchAfter && { search_after: searchAfter }),
          _source: true,
          highlight: {
            fields: {
              'title.value': {},
              description: {},
              content: { fragment_size: 150, number_of_fragments: 3 },
              'metadata.mitre_tactics': {},
              'metadata.mitre_techniques': {},
              'metadata.tags': {},
              'metadata.mitre_attack.name': {},
              'metadata.mitre_attack.mitre_id': {},
              'metadata.mitre_attack.parent_name': {},
            },
            pre_tags: ['<strong>'],
            post_tags: ['</strong>'],
          },
        };

        const response = (await this.openSearchService.search(
          this.indexSchema.indexAlias,
          searchBody,
        )) as OpenSearchResponse<RuleSource>;

        const cursorResponse = this.formatSearchResponse(
          response,
          options,
          cursor,
        );
        // Use page for metadata indication only in the final response
        return this.formatCursorPagedSearchResponse(cursorResponse, {
          page: 1,
          size,
        }); // Cursor mode effectively resets page indicator
      } else {
        // --- Offset Pagination ---
        this.logger.debug(
          `Performing advanced offset-based search (page: ${page}, size: ${size})`,
        );

        const from = (page - 1) * size;
        const sort = this.getSortConfig(
          sort_by,
          sort_order,
          PaginationDirection.FORWARD,
        );

        const searchBody = {
          query: coreQuery,
          sort,
          from,
          size,
          _source: true,
          highlight: {
            fields: {
              'title.value': {},
              description: {},
              content: { fragment_size: 150, number_of_fragments: 3 },
              'metadata.mitre_tactics': {},
              'metadata.mitre_techniques': {},
              'metadata.tags': {},
              'metadata.mitre_attack.name': {},
              'metadata.mitre_attack.mitre_id': {},
              'metadata.mitre_attack.parent_name': {},
            },
            pre_tags: ['<strong>'],
            post_tags: ['</strong>'],
          },
        };

        const rawResponse = (await this.openSearchService.search(
          this.indexSchema.indexAlias,
          searchBody,
        )) as OpenSearchResponse<RuleSource>;

        return this.formatOffsetPagedSearchResponse(rawResponse, {
          page,
          size,
        });
      }
    } catch (error) {
      this.logger.error(`Advanced search error: ${error}`);
      throw error;
    }
  }

  /**
   * Find similar rules
   * @param ruleId Reference rule ID
   * @param options Similar rules options
   * @returns Similar rules
   */
  async findSimilarRules(
    ruleId: string,
    options: SimilarRulesQueryDto,
  ): Promise<SimilarRulesResponseDto> {
    this.logger.debug(`Finding similar rules for reference rule: ${ruleId}`);

    // Get reference rule
    const rule = (await this.openSearchService.get(
      this.indexSchema.indexAlias,
      ruleId,
    )) as OpenSearchGetResponse<RuleSource>;
    if (!rule || !rule.found) {
      throw new NotFoundException(`Rule with ID ${ruleId} not found`);
    }

    this.logger.debug('Reference rule:', rule);

    // Extract field values for MLT query
    const fieldValues: SimilarRulesFieldValues = {
      content: rule._source.content,
      title: rule._source.title?.value,
      description: rule._source.description,
      mitre_techniques: rule._source.metadata?.mitre_techniques || [],
      mitre_tactics: rule._source.metadata?.mitre_tactics || [],
      tags: rule._source.metadata?.tags || [],
    };

    // Validate that at least one field has content
    if (
      !fieldValues.content &&
      !fieldValues.title &&
      !fieldValues.description
    ) {
      throw new BadRequestException(
        'Reference rule must have content, title, or description',
      );
    }

    // Calculate content hash for exact duplicate detection
    let contentHash = '';
    if (fieldValues.content) {
      contentHash = crypto
        .createHash('sha256')
        .update(fieldValues.content)
        .digest('hex');
    }

    // Build MLT query using the builder
    const searchBody = this.similarRulesQueryBuilder.build(
      ruleId,
      fieldValues,
      options,
      this.indexSchema.indexAlias,
    );

    this.logger.debug('MLT query from builder:', searchBody); // Log the query from builder

    // Execute search
    const response = (await this.openSearchService.search(
      this.indexSchema.indexAlias,
      searchBody,
    )) as OpenSearchResponse<RuleSource>;
    this.logger.debug('Raw OpenSearch response:', response);

    // Get max score for normalization
    const maxScore = response.hits.max_score || 1;

    // Safely access stop_words for filtering in the map function
    const stopWordsForFilter =
      searchBody.query?.more_like_this?.stop_words || [];

    // Process results
    const results = response.hits.hits.map((hit: OpenSearchHit<RuleSource>) => {
      // Check if this is an exact duplicate (only if content exists)
      let isExactDuplicate = false;
      if (fieldValues.content && hit._source.content) {
        const hitContentHash = crypto
          .createHash('sha256')
          .update(hit._source.content)
          .digest('hex');
        isExactDuplicate = contentHash === hitContentHash;
      }

      // Extract matched terms from explanation
      const matchedTerms = new Set<string>();
      const extractTerms = (explanation: OpenSearchExplanation) => {
        if (explanation.description?.includes('weight(')) {
          const term = explanation.description.match(/weight\(([^:]+)/)?.[1];
          if (term) matchedTerms.add(term);
        }
        if (explanation.details) {
          explanation.details.forEach((detail: OpenSearchExplanation) =>
            extractTerms(detail),
          );
        }
      };
      if (hit.explanation) {
        extractTerms(hit.explanation);
      }

      // Calculate similarity percentage
      let similarityPercentage: number;
      if (isExactDuplicate) {
        similarityPercentage = 100;
      } else {
        const scorePercentage = ((hit._score ?? 0) / maxScore) * 100; // Handle null _score
        let termMatchPercentage = 0;

        if (fieldValues.content) {
          const terms = fieldValues.content
            .split(/[\s,\n]+/)
            .filter(
              (term) =>
                term.length >= 4 &&
                !stopWordsForFilter.includes(term.toLowerCase()),
            );
          termMatchPercentage =
            terms.length > 0 ? (matchedTerms.size / terms.length) * 100 : 0;
        } else {
          // If no content, use title and description for term matching
          const terms = [
            ...(fieldValues.title?.split(/[\s,\n]+/) || []),
            ...(fieldValues.description?.split(/[\s,\n]+/) || []),
          ].filter(
            (term) =>
              term.length >= 4 &&
              !stopWordsForFilter.includes(term.toLowerCase()),
          );
          termMatchPercentage =
            terms.length > 0 ? (matchedTerms.size / terms.length) * 100 : 0;
        }

        similarityPercentage = Math.round(
          (scorePercentage + termMatchPercentage) / 2,
        );
      }

      return {
        id: hit._id,
        title:
          typeof hit._source.title === 'object'
            ? hit._source.title?.value || ''
            : hit._source.title || '',
        description: hit._source.description || '',
        content: hit._source.content || '',
        rule_type: hit._source.rule_type,
        severity: hit._source.severity || '',
        _score: hit._score ?? 0,
        similarity_percentage: similarityPercentage,
        is_exact_duplicate: isExactDuplicate,
        matched_terms: Array.from(matchedTerms),
        total_terms: fieldValues.content
          ? fieldValues.content
              .split(/[\s,\n]+/)
              .filter(
                (term) =>
                  term.length >= 4 &&
                  !stopWordsForFilter.includes(term.toLowerCase()),
              ).length
          : [
              ...(fieldValues.title?.split(/[\s,\n]+/) || []),
              ...(fieldValues.description?.split(/[\s,\n]+/) || []),
            ].filter(
              (term) =>
                term.length >= 4 &&
                !stopWordsForFilter.includes(term.toLowerCase()),
            ).length,
      };
    });

    return {
      status: 'success',
      meta: {
        reference_rule: ruleId,
        total:
          typeof response.hits.total === 'number'
            ? response.hits.total
            : response.hits.total.value,
      },
      data: results.map((result) => ({
        id: result.id,
        title: result.title,
        description: result.description || '',
        rule_type: result.rule_type as RuleType,
        severity: result.severity || '',
        _score: result._score,
        similarity_percentage: result.similarity_percentage,
      })),
    };
  }

  /**
   * Get search suggestions
   * @param prefix Search prefix
   * @param size Number of suggestions to return
   * @param visibleGroupIds Optional visible group IDs for context
   * @returns Search suggestions
   */
  async getSuggestions(
    prefix: string,
    size: number = 5,
    visibleGroupIds?: string[],
  ): Promise<SearchSuggestionsResponseDto> {
    try {
      let contextsForBuilder: { [key: string]: string[] } | undefined;
      if (visibleGroupIds && visibleGroupIds.length > 0) {
        contextsForBuilder = { owner_id: visibleGroupIds };
        this.logger.debug(
          'Preparing owner_id context for suggest query builder:',
          visibleGroupIds,
        );
      } else {
        this.logger.debug(
          'No visibleGroupIds provided, suggest query will not have context.',
        );
      }

      const suggestBody = this.suggestQueryBuilder.build(
        prefix,
        size,
        contextsForBuilder,
      );

      this.logger.debug(
        'Suggest query body from builder:',
        JSON.stringify(suggestBody),
      );

      const response = (await this.openSearchService.search(
        this.indexSchema.indexAlias,
        suggestBody,
      )) as OpenSearchResponse<RuleSource>;

      // Extract suggestions and deduplicate case-insensitively, preserving first casing
      const uniqueSuggestions = new Map<string, string>();
      if (response.suggest?.rule_title_suggestions?.[0]?.options) {
        response.suggest.rule_title_suggestions[0].options.forEach((option) => {
          if (option.text) {
            const lowerCaseText = option.text.toLowerCase();
            if (!uniqueSuggestions.has(lowerCaseText)) {
              uniqueSuggestions.set(lowerCaseText, option.text); // Store original casing
            }
          }
        });
      }

      return {
        status: 'success',
        data: Array.from(uniqueSuggestions.values()), // Get the original-cased values
      };
    } catch (error) {
      this.logger.error(`Suggestions error: ${error}`);
      throw error;
    }
  }

  /**
   * Get search metadata
   * @returns Search metadata
   */
  getMetadata(): SearchMetadataResponseDto {
    return {
      status: 'success',
      data: {
        searchable_fields: ['title', 'description', 'content'],
        filters: {
          rule_type: ['detection', 'hunting', 'compliance'],
          stage: ['development', 'testing', 'production'],
          status: ['draft', 'active', 'deprecated', 'in_review'],
        },
        sort_options: Object.values(SortField),
      },
    };
  }

  /**
   * Build advanced search query
   * @param query Advanced search query
   * @returns OpenSearch query body
   */
  private buildAdvancedSearchQuery(
    query: AdvancedSearchQueryDto,
  ): OpenSearchQueryClause {
    return this.advancedSearchQueryBuilder.build(query);
  }

  /**
   * Parse a search cursor
   */
  private parseCursor(cursor?: string): SearchCursor | undefined {
    if (!cursor) return undefined;

    try {
      return JSON.parse(
        Buffer.from(cursor, 'base64').toString(),
      ) as SearchCursor;
    } catch (error) {
      this.logger.error('Failed to parse cursor:', error);
      throw new BadRequestException('Invalid cursor format');
    }
  }

  /**
   * Get sort configuration for the query
   */
  private getSortConfig(
    sortBy: SortField,
    sortOrder: SortOrder,
    direction: PaginationDirection,
  ): OpenSearchSortClause[] {
    const order = sortOrder.toLowerCase() as 'asc' | 'desc';
    const reverseOrder = order === 'asc' ? 'desc' : 'asc';

    // Adjust order for backward pagination
    const actualOrder =
      direction === PaginationDirection.BACKWARD ? reverseOrder : order;

    switch (sortBy) {
      case SortField.CREATED_AT:
        return [{ created_at: actualOrder }, { _id: actualOrder }];
      case SortField.UPDATED_AT:
        return [{ updated_at: actualOrder }, { _id: actualOrder }];
      case SortField.TITLE:
        return [{ 'title.value.keyword': actualOrder }, { _id: actualOrder }];
      case SortField.RELEVANCE:
        // Relevance is always descending, backward flips the tiebreaker
        return [
          { _score: 'desc' },
          {
            _id:
              direction === PaginationDirection.BACKWARD ? reverseOrder : order,
          },
        ];
      case SortField.LIKES:
        return [{ likes: actualOrder }, { _id: actualOrder }];
      case SortField.DOWNLOADS:
        return [{ downloads: actualOrder }, { _id: actualOrder }];
      case SortField.DISLIKES:
        return [{ dislikes: actualOrder }, { _id: actualOrder }];
      case SortField.PUBLISHED_AT:
        return [{ published_at: actualOrder }, { _id: actualOrder }];
      case SortField.CONTRIBUTOR:
        return [{ 'contributor.keyword': actualOrder }, { _id: actualOrder }];
      case SortField.GROUP_NAME:
        return [{ 'group_name.keyword': actualOrder }, { _id: actualOrder }];
      case SortField.RULE_TYPE:
        return [{ rule_type: actualOrder }, { _id: actualOrder }];
      case SortField.AUTHOR:
        return [
          { 'metadata.author.keyword': actualOrder },
          { _id: actualOrder },
        ];
      case SortField.METADATA_DATE:
        return [{ 'metadata.date': actualOrder }, { _id: actualOrder }];
      case SortField.METADATA_MODIFIED:
        return [{ 'metadata.modified': actualOrder }, { _id: actualOrder }];
      case SortField.MITRE_TACTIC:
        return [
          { 'metadata.mitre_tactics': actualOrder },
          { _id: actualOrder },
        ];
      case SortField.MITRE_TECHNIQUE:
        return [
          { 'metadata.mitre_techniques': actualOrder },
          { _id: actualOrder },
        ];
      default:
        return [{ _id: actualOrder }];
    }
  }

  /**
   * Build search_after parameter from cursor
   */
  private getSearchAfter(
    cursor: SearchCursor | undefined,
  ): (string | number | boolean)[] | undefined {
    if (!cursor) return undefined;
    return cursor.sort_values;
  }

  /**
   * Validate cursor format and content
   */
  private validateCursor(
    cursor: string,
    expectedSortField: SortField,
  ): SearchCursor {
    try {
      const decodedCursor = this.parseCursor(cursor);
      if (!decodedCursor) {
        throw new BadRequestException('Invalid cursor format');
      }

      // Validate required fields
      if (
        !decodedCursor.sort_values ||
        !Array.isArray(decodedCursor.sort_values)
      ) {
        throw new BadRequestException(
          'Invalid cursor: missing or invalid sort_values',
        );
      }
      if (!decodedCursor.id) {
        throw new BadRequestException('Invalid cursor: missing id');
      }
      if (
        !decodedCursor.sort_field ||
        !Object.values(SortField).includes(decodedCursor.sort_field)
      ) {
        throw new BadRequestException('Invalid cursor: invalid sort_field');
      }
      if (
        !decodedCursor.sort_order ||
        !Object.values(SortOrder).includes(decodedCursor.sort_order)
      ) {
        throw new BadRequestException('Invalid cursor: invalid sort_order');
      }

      // Validate sort field matches
      // Allow relevance mismatch as it's implicit? No, keep strict.
      if (decodedCursor.sort_field !== expectedSortField) {
        this.logger.warn(
          `Cursor sort field mismatch. Expected ${expectedSortField}, got ${decodedCursor.sort_field}`,
        );
        // throw new BadRequestException('Invalid cursor: sort field mismatch');
      }

      // Validate sort values based on sort field
      switch (decodedCursor.sort_field) {
        case SortField.CREATED_AT:
        case SortField.UPDATED_AT:
        case SortField.PUBLISHED_AT:
        case SortField.METADATA_DATE:
        case SortField.METADATA_MODIFIED:
          if (typeof decodedCursor.sort_values[0] === 'string') {
            if (
              !this.queryUtilityService.isValidDateString(
                decodedCursor.sort_values[0],
              )
            ) {
              throw new BadRequestException(
                'Invalid cursor: invalid date in sort_values',
              );
            }
          } else {
            throw new BadRequestException(
              'Invalid cursor: expected string for date sort_values',
            );
          }
          break;
        case SortField.RELEVANCE:
          if (typeof decodedCursor.sort_values[0] !== 'number') {
            this.logger.warn(
              `Cursor score type mismatch: ${typeof decodedCursor.sort_values[0]}`,
            );
            // throw new BadRequestException(
            //   'Invalid cursor: invalid score in sort_values',
            // );
          }
          break;
        case SortField.LIKES:
        case SortField.DOWNLOADS:
        case SortField.DISLIKES:
          if (typeof decodedCursor.sort_values[0] !== 'number') {
            throw new BadRequestException(
              'Invalid cursor: invalid number in sort_values for likes/downloads/dislikes',
            );
          }
          break;
        case SortField.TITLE:
        case SortField.CONTRIBUTOR:
        case SortField.GROUP_NAME:
        case SortField.RULE_TYPE:
        case SortField.AUTHOR:
        case SortField.MITRE_TACTIC:
        case SortField.MITRE_TECHNIQUE:
          if (typeof decodedCursor.sort_values[0] !== 'string') {
            throw new BadRequestException(
              `Invalid cursor: invalid string in sort_values for ${decodedCursor.sort_field}`,
            );
          }
          break;
      }

      return decodedCursor;
    } catch (error) {
      this.logger.error('Cursor validation failed:', error);
      throw new BadRequestException(
        `Invalid cursor: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Generate a search cursor from the last item in search results
   */
  private generateCursor(
    lastItem: OpenSearchHit<RuleSource>,
    sortBy: SortField,
    sortOrder: SortOrder,
  ): string {
    const cursor: SearchCursor = {
      sort_values: [],
      id: lastItem._id,
      sort_field: sortBy,
      sort_order: sortOrder,
    };

    // Use the actual sort values returned by OpenSearch
    // These are in the `sort` array on each hit
    if (lastItem.sort) {
      cursor.sort_values = lastItem.sort;
    } else {
      // Fallback if sort values aren't present (shouldn't happen with explicit sort)
      this.logger.warn(
        `Missing sort values on hit ${lastItem._id}, generating fallback cursor.`,
      );
      switch (sortBy) {
        case SortField.CREATED_AT:
          cursor.sort_values = [lastItem._source.created_at, lastItem._id];
          break;
        case SortField.UPDATED_AT:
          cursor.sort_values = [lastItem._source.updated_at, lastItem._id];
          break;
        case SortField.TITLE:
          cursor.sort_values = [
            lastItem._source.title?.value || '',
            lastItem._id,
          ];
          break;
        case SortField.RELEVANCE:
          cursor.sort_values = [lastItem._score ?? 0, lastItem._id];
          break;
        case SortField.LIKES:
          cursor.sort_values = [lastItem._source.likes ?? 0, lastItem._id];
          break;
        case SortField.DOWNLOADS:
          cursor.sort_values = [lastItem._source.downloads ?? 0, lastItem._id];
          break;
        case SortField.DISLIKES:
          cursor.sort_values = [lastItem._source.dislikes ?? 0, lastItem._id];
          break;
        case SortField.PUBLISHED_AT:
          cursor.sort_values = [
            lastItem._source.published_at ?? '',
            lastItem._id,
          ];
          break;
        case SortField.CONTRIBUTOR:
          cursor.sort_values = [
            lastItem._source.contributor ?? '',
            lastItem._id,
          ];
          break;
        case SortField.GROUP_NAME:
          cursor.sort_values = [
            lastItem._source.group_name ?? '',
            lastItem._id,
          ];
          break;
        case SortField.RULE_TYPE:
          cursor.sort_values = [lastItem._source.rule_type ?? '', lastItem._id];
          break;
        case SortField.AUTHOR:
          cursor.sort_values = [
            lastItem._source.metadata?.author ?? '',
            lastItem._id,
          ];
          break;
        case SortField.METADATA_DATE:
          cursor.sort_values = [
            lastItem._source.metadata?.date ?? '',
            lastItem._id,
          ];
          break;
        case SortField.METADATA_MODIFIED:
          cursor.sort_values = [
            lastItem._source.metadata?.modified ?? '',
            lastItem._id,
          ];
          break;
        case SortField.MITRE_TACTIC:
          // For array fields, OpenSearch sorts by the 'first' value according to order.
          // The cursor should reflect this. If hit.sort is available, it's preferred.
          // Fallback: use the first element if array, or empty string.
          cursor.sort_values = [
            (Array.isArray(lastItem._source.metadata?.mitre_tactics) &&
              lastItem._source.metadata?.mitre_tactics[0]) ||
              '',
            lastItem._id,
          ];
          break;
        case SortField.MITRE_TECHNIQUE:
          cursor.sort_values = [
            (Array.isArray(lastItem._source.metadata?.mitre_techniques) &&
              lastItem._source.metadata?.mitre_techniques[0]) ||
              '',
            lastItem._id,
          ];
          break;
        default:
          cursor.sort_values = [lastItem._id]; // Should have specific sort value
          break;
      }
    }

    return Buffer.from(JSON.stringify(cursor)).toString('base64');
  }

  /**
   * Format search response with cursor-based pagination metadata.
   * Used internally by cursor-mode paged search.
   */
  private formatSearchResponse(
    response: OpenSearchResponse<RuleSource>,
    options: SearchOptionsDto,
    requestCursor?: string, // The cursor used for *this* request
  ): SearchResponseDto {
    const { hits } = response;
    if (!hits || !hits.total) {
      this.logger.warn(
        'Invalid OpenSearch response format in formatSearchResponse:',
        response,
      );
      return { status: 'error', meta: { total: 0, size: 0 }, data: [] };
    }

    const total =
      typeof hits.total === 'number' ? hits.total : (hits.total.value ?? 0);
    const items = hits.hits || [];
    const {
      size = 20,
      sort_by = SortField.RELEVANCE,
      sort_order = SortOrder.DESC,
      direction = PaginationDirection.FORWARD,
    } = options;

    // Determine if there are more results than requested size
    const hasMore = items.length > size;
    // Extract the actual items for this page
    const pageItems = hasMore ? items.slice(0, size) : items;

    // Reverse items if paginating backward to maintain logical order
    const orderedItems =
      direction === PaginationDirection.BACKWARD
        ? pageItems.reverse()
        : pageItems;

    let nextCursor: string | undefined;
    let prevCursor: string | undefined;

    // Generate cursors based on pagination direction
    if (direction === PaginationDirection.FORWARD) {
      // If we fetched more items than size, there's a next page
      if (hasMore) {
        const lastItem = orderedItems[orderedItems.length - 1];
        nextCursor = this.generateCursor(lastItem, sort_by, sort_order);
      }
      // A previous cursor exists if this wasn't the first page (i.e., requestCursor was provided)
      prevCursor = requestCursor;
    } else {
      // BACKWARD pagination
      // The 'next' cursor corresponds to the cursor used for this backward request
      nextCursor = requestCursor;
      // If we fetched more items than size, there's a previous page
      if (hasMore) {
        const firstItem = orderedItems[0]; // First item after reversing
        prevCursor = this.generateCursor(firstItem, sort_by, sort_order);
      }
    }

    const data = orderedItems.map((hit: OpenSearchHit<RuleSource>) => {
      const source = hit._source ?? {};
      return {
        id: hit._id,
        title: source.title?.value,
        description: source.description,
        ai_generated: source.ai_generated,
        status: source.status as RuleStatus,
        rule_type: source.rule_type as RuleType,
        content: source.content ?? '',
        created_by: source.created_by ?? '',
        contributor: source.contributor ?? '',
        likes: source.likes ?? 0,
        downloads: source.downloads ?? 0,
        dislikes: source.dislikes ?? 0,
        bookmarks: source.bookmarks ?? 0,
        group_id: source.group_id,
        group_name: source.group_name,
        tags: source.tags ?? [],
        metadata: source.metadata,
        test_cases: source.test_cases ?? [],
        created_at: source.created_at ?? '',
        updated_at: source.updated_at ?? '',
        published_at: source.published_at,
        version: source.version ?? 1,
        is_bookmarked: source.is_bookmarked,
        is_liked: source.is_liked,
        is_disliked: source.is_disliked,
        _score: hit._score ?? 0,
        highlight: hit.highlight,
        _id: hit._id,
      };
    });

    return {
      status: 'success',
      meta: {
        total,
        size: orderedItems.length,
        next_cursor: nextCursor,
        prev_cursor: prevCursor,
      },
      data,
    };
  }

  async simpleRawSearch(
    index: string,
    query: OpenSearchQueryClause,
    sort: OpenSearchSortClause[] | OpenSearchSortClause,
    size: number,
  ): Promise<OpenSearchResponse<RuleSource>> {
    // Build query body for offset search
    const searchBody = {
      query: query,
      sort,
      size,
    };

    this.logger.log(
      `Custom paged search called with: ${JSON.stringify(searchBody)}`,
    );

    return (await this.openSearchService.search(
      index,
      searchBody,
    )) as OpenSearchResponse<RuleSource>;
  }

  async pagedCustomSortSearch(
    sort: OpenSearchSortClause[] | OpenSearchSortClause,
    query?: string,
    filters?: SearchFiltersDto,
    options?: PageSearchOptionsDto,
  ): Promise<PagedSearchResponseDto> {
    this.logger.debug('Paged custom sort search called with:', {
      sort,
      query,
      filters: JSON.stringify(filters),
      options: JSON.stringify(options),
    });

    const { size = 20, page = 1 } = options || {};
    const from = (page - 1) * size;

    // Ensure query is processed (consistent with other methods)
    const processedQuery = query
      ? this.queryUtilityService.preprocessQuery(query)
      : undefined;

    const builderOptions = {
      size,
      from,
      sort_config: Array.isArray(sort) ? sort : [sort],
    };

    const searchBody = this.basicSearchQueryBuilder.build(
      processedQuery,
      filters,
      builderOptions,
    );
    // Removed spread and manual _source/highlight, searchBody is now direct from builder

    this.logger.debug(
      'Search body for pagedCustomSortSearch:',
      JSON.stringify(searchBody),
    );

    const response = (await this.openSearchService.search(
      this.indexSchema.indexAlias,
      searchBody,
    )) as OpenSearchResponse<RuleSource>;

    // Format response for paged output (offset mode)
    return this.formatOffsetPagedSearchResponse(response, { page, size });
  }

  /**
   * Perform page-based search, supporting both cursor and offset modes.
   * This is the main entry point for paginated searches.
   */
  async pagedSearch(
    query?: string,
    filters?: SearchFiltersDto,
    pageOptions?: PageSearchOptionsDto,
  ): Promise<PagedSearchResponseDto> {
    this.logger.debug('Offset-based paged search called with:', {
      query,
      filters: JSON.stringify(filters),
      options: JSON.stringify(pageOptions),
    });

    const {
      size = 20,
      page = 1,
      sort_by = SortField.RELEVANCE, // Default sort for pagedSearch
      sort_order = SortOrder.DESC,
    } = pageOptions || {};

    const from = (page - 1) * size;

    const processedQuery = query
      ? this.queryUtilityService.preprocessQuery(query)
      : undefined;

    // Default sort for pagedSearch if not relying on cursor logic's sort
    const defaultSortConfig = this.getSortConfig(
      sort_by,
      sort_order,
      PaginationDirection.FORWARD, // Standard forward for paged results
    );

    const builderOptions = {
      size,
      from,
      sort_config: defaultSortConfig,
    };

    const searchBody = this.basicSearchQueryBuilder.build(
      processedQuery,
      filters,
      builderOptions,
    );

    this.logger.debug(
      'Search body for pagedSearch:',
      JSON.stringify(searchBody),
    );

    const response = (await this.openSearchService.search(
      this.indexSchema.indexAlias,
      searchBody,
    )) as OpenSearchResponse<RuleSource>;

    // Format response for paged output (offset mode)
    return this.formatOffsetPagedSearchResponse(response, { page, size });
  }

  /**
   * Format a raw OpenSearch response for offset-based paged results.
   */
  private formatOffsetPagedSearchResponse(
    rawResponse: OpenSearchResponse<RuleSource>,
    options: { page: number; size: number },
  ): PagedSearchResponseDto {
    const { page, size } = options;
    const { hits } = rawResponse;

    if (!hits || !hits.total) {
      // Pass rawResponse as metadata, not in the string template
      this.logger.warn(
        'Invalid OpenSearch response format in formatOffsetPagedSearchResponse',
        { rawResponse },
      );
      return {
        status: 'error',
        meta: {
          total: 0,
          size: 0,
          current_page: page,
          page_size: size,
          total_pages: 0,
          has_next_page: false,
          has_prev_page: false,
        },
        data: [],
      };
    }

    const total =
      typeof hits.total === 'number' ? hits.total : (hits.total.value ?? 0);
    const items = hits.hits || [];
    const totalPages = Math.ceil(total / size);

    const data = items.map((hit: OpenSearchHit<RuleSource>) => {
      const source = hit._source ?? {};
      return {
        id: hit._id,
        title: source.title?.value,
        description: source.description,
        ai_generated: source.ai_generated,
        status: source.status as RuleStatus,
        rule_type: source.rule_type as RuleType,
        content: source.content ?? '',
        created_by: source.created_by ?? '',
        contributor: source.contributor ?? '',
        likes: source.likes ?? 0,
        downloads: source.downloads ?? 0,
        dislikes: source.dislikes ?? 0,
        bookmarks: source.bookmarks ?? 0,
        group_id: source.group_id,
        group_name: source.group_name,
        tags: source.tags ?? [],
        metadata: source.metadata,
        test_cases: source.test_cases ?? [],
        created_at: source.created_at ?? '',
        updated_at: source.updated_at ?? '',
        published_at: source.published_at,
        version: source.version ?? 1,
        is_bookmarked: source.is_bookmarked,
        is_liked: source.is_liked,
        is_disliked: source.is_disliked,
        _score: hit._score ?? 0,
        highlight: hit.highlight,
        _id: hit._id,
      };
    });

    return {
      status: 'success',
      meta: {
        total,
        size: data.length, // Actual number of items returned
        current_page: page,
        total_pages: totalPages,
        page_size: size, // The requested page size
        has_next_page: page < totalPages,
        has_prev_page: page > 1,
        // No cursors in offset mode
        next_cursor: undefined,
        prev_cursor: undefined,
      },
      data,
    };
  }

  /**
   * Format a cursor-based SearchResponseDto into a PagedSearchResponseDto.
   */
  private formatCursorPagedSearchResponse(
    response: SearchResponseDto, // Result from internal cursor search
    options: { page: number; size: number }, // Page is only for metadata indication
  ): PagedSearchResponseDto {
    const { page, size } = options; // Use size for calculating total_pages if needed
    const { meta, data, status } = response;

    // Estimate total pages based on total count and requested size.
    // Note: This can be slightly inaccurate if total changes, but it's for UI indication.
    const totalPages = meta.total > 0 ? Math.ceil(meta.total / size) : 0;

    // Determine has_next/has_prev based purely on returned cursors
    const has_next_page = !!meta.next_cursor;
    const has_prev_page = !!meta.prev_cursor; // Or could infer from `page > 1`? Stick to cursor presence.

    return {
      status,
      meta: {
        total: meta.total,
        size: meta.size, // Actual number of items in this response
        current_page: page, // Indicate the requested page number
        total_pages: totalPages, // Estimated total pages (Corrected back to snake_case)
        page_size: size, // The requested page size
        has_next_page,
        has_prev_page,
        next_cursor: meta.next_cursor,
        prev_cursor: meta.prev_cursor,
      },
      data,
    };
  }
}
