import { Injectable, Logger } from '@nestjs/common';
import { SearchFiltersDto } from '../dtos/search-filters.dto';
import { OpenSearchQueryClause } from '../models/opensearch-types';

/**
 * Utility service for query building operations.
 */
@Injectable()
export class QueryUtilityService {
  private readonly logger = new Logger(QueryUtilityService.name);

  /**
   * Checks if an identifier matches MITRE technique ID patterns.
   * Examples: T1234, T1234.001
   * @param identifier The identifier string to check.
   * @returns True if it matches the pattern, false otherwise.
   */
  isMitreTechniqueIdPattern(identifier: string): boolean {
    if (!identifier) {
      return false;
    }
    // Matches T followed by digits, optionally followed by a dot and more digits.
    const mitrePattern = /^T\d+(\.\d+)?$/i;
    return mitrePattern.test(identifier);
  }

  /**
   * Identifies potential acronyms or specific identifiers in a query string.
   * Examples: "RDS", "S3", "T1059"
   * @param queryText The user's search query.
   * @returns An array of identified potential acronyms/identifiers.
   */
  extractPotentialIdentifiers(queryText: string): string[] {
    if (!queryText) {
      return [];
    }
    // Regex to find words that are all caps (2-5 chars),
    // or mixed case with numbers (like T1059),
    // or common separated terms like file.exe
    const identifierRegex =
      /\b([A-Z]{2,5}|[A-Za-z][0-9][A-Za-z0-9]+|[A-Za-z0-9]+\.[a-zA-Z0-9]+)\b/gi; // Added 'i' flag
    const matches = queryText.match(identifierRegex);
    return matches || [];
  }

  /**
   * Parse field-specific query
   * @param query Search query text
   * @returns Parsed query object or null if not a field query
   */
  parseFieldQuery(query: string): { field: string; value: string } | null {
    const match = query.match(/^([^:]+):(.+)$/);
    if (!match) {
      return null;
    }
    return {
      field: match[1].trim(),
      value: match[2].trim(),
    };
  }

  /**
   * Preprocess search query text
   * @param query Search query text
   * @returns Preprocessed query text
   */
  preprocessQuery(query: string): string {
    // Replace multiple spaces with a single space
    let processed = query.replace(/\s+/g, ' ').trim();

    // If it's a field query, don't modify the value part
    const fieldQuery = this.parseFieldQuery(processed);
    if (fieldQuery) {
      return processed; // Return the already trimmed and space-normalized query
    }

    // For regular queries, ensure proper spacing around special characters
    processed = processed.replace(/([+\-|"()])/g, ' $1 ').trim();
    // Collapse multiple spaces again after adding spaces around special chars
    return processed.replace(/\s+/g, ' ').trim();
  }

  /**
   * Build filters for OpenSearch query
   * @param filters Search filters
   * @returns Array of filter clauses
   */
  buildFilters(filters?: SearchFiltersDto): OpenSearchQueryClause[] {
    if (!filters) {
      this.logger.debug('No filters provided');
      return [];
    }

    this.logger.debug('Building filters from:', JSON.stringify(filters));

    const filterClauses: OpenSearchQueryClause[] = [];

    // Handle included owner_ids
    if (filters.owner_ids?.length) {
      this.logger.debug('Adding owner_ids filter:', filters.owner_ids);
      filterClauses.push({ terms: { owner_id: filters.owner_ids } });
    }

    // Handle excluded owner_ids
    if (filters.exclude?.owner_ids?.length) {
      this.logger.debug(
        'Adding excluded owner_ids filter:',
        filters.exclude.owner_ids,
      );
      filterClauses.push({
        bool: {
          must_not: [{ terms: { owner_id: filters.exclude.owner_ids } }],
        },
      });
    }

    if (filters.ids?.length) {
      this.logger.debug('Adding ids filter:', filters.ids);
      filterClauses.push({ terms: { id: filters.ids } });
    }

    if (filters.rule_types?.length) {
      this.logger.debug('Adding rule_types filter:', filters.rule_types);
      filterClauses.push({ terms: { rule_type: filters.rule_types } });
    }

    if (filters.statuses?.length) {
      this.logger.debug('Adding statuses filter:', filters.statuses);
      filterClauses.push({ terms: { status: filters.statuses } });
    }

    if (filters.stages?.length) {
      this.logger.debug('Adding stages filter:', filters.stages);
      filterClauses.push({ terms: { stage: filters.stages } });
    }

    // Handle date range filters
    const dateRangeFields = [
      {
        field: 'created_at',
        after: filters.created_after,
        before: filters.created_before,
      },
      {
        field: 'published_at',
        after: filters.published_after,
        before: filters.published_before,
      },
      {
        field: 'updated_at',
        after: filters.updated_after,
        before: filters.updated_before,
      },
    ];

    for (const { field, after, before } of dateRangeFields) {
      if (after || before) {
        const rangeFilter: OpenSearchQueryClause = {
          range: {
            [field]: {},
          },
        };

        if (after) {
          this.logger.debug(`Adding ${field}_after filter:`, after);
          rangeFilter.range![field].gte = after;
        }

        if (before) {
          this.logger.debug(`Adding ${field}_before filter:`, before);
          rangeFilter.range![field].lte = before;
        }

        filterClauses.push(rangeFilter);
      }
    }

    if (filters.created_by?.length) {
      this.logger.debug('Adding created_by filter:', filters.created_by);
      filterClauses.push({ terms: { created_by: filters.created_by } });
    }

    if (filters.contributors?.length) {
      this.logger.debug(
        'Adding contributors filter (partial, case-insensitive):',
        filters.contributors,
      );
      const contributorMatchQueries = filters.contributors.map(
        (contributorName) => ({
          match: {
            contributor: {
              // Querying the analyzed text field 'contributor'
              query: contributorName,
              operator: 'OR',
            },
          },
        }),
      );
      filterClauses.push({
        bool: {
          should: contributorMatchQueries,
          minimum_should_match: 1,
        },
      });
    }

    if (filters.authors?.length) {
      this.logger.debug(
        'Adding authors filter (partial, case-insensitive):',
        filters.authors,
      );
      const authorMatchQueries = filters.authors.map((authorName) => ({
        match: {
          'metadata.author': {
            // Querying the analyzed text field 'metadata.author'
            query: authorName,
            operator: 'OR',
          },
        },
      }));
      filterClauses.push({
        bool: {
          should: authorMatchQueries,
          minimum_should_match: 1,
        },
      });
    }

    if (filters.severities?.length) {
      this.logger.debug('Adding severities filter:', filters.severities);
      filterClauses.push({ terms: { severity: filters.severities } });
    }

    if (filters.mitre_tactics?.length) {
      const lowercasedTactics = filters.mitre_tactics.map((tactic) =>
        tactic.toLowerCase(),
      );
      this.logger.debug(
        'Adding lowercased mitre_tactics filter:',
        lowercasedTactics,
      );
      filterClauses.push({
        terms: { 'metadata.mitre_tactics': lowercasedTactics },
      });
    }

    if (filters.mitre_techniques?.length) {
      const lowercasedTechniques = filters.mitre_techniques.map((technique) =>
        technique.toLowerCase(),
      );
      this.logger.debug(
        'Adding lowercased mitre_techniques filter:',
        lowercasedTechniques,
      );
      filterClauses.push({
        terms: { 'metadata.mitre_techniques': lowercasedTechniques },
      });
    }

    if (filters.mitre_attack_ids?.length) {
      this.logger.debug(
        'Adding mitre_attack_ids filter:',
        filters.mitre_attack_ids,
      );
      filterClauses.push({
        nested: {
          path: 'metadata.mitre_attack',
          query: {
            terms: {
              'metadata.mitre_attack.mitre_id': filters.mitre_attack_ids,
            },
          },
        },
      });
    }

    // Handle metadata.date range
    if (filters.metadata_date_after || filters.metadata_date_before) {
      const rangeQuery: OpenSearchQueryClause = {
        range: { 'metadata.date': {} },
      };
      if (filters.metadata_date_after) {
        rangeQuery.range!['metadata.date'].gte = filters.metadata_date_after;
      }
      if (filters.metadata_date_before) {
        rangeQuery.range!['metadata.date'].lte = filters.metadata_date_before;
      }
      filterClauses.push(rangeQuery);
      this.logger.debug(
        'Adding metadata.date range filter:',
        rangeQuery.range!['metadata.date'],
      );
    }

    // Handle metadata.modified range
    if (filters.metadata_modified_after || filters.metadata_modified_before) {
      const rangeQuery: OpenSearchQueryClause = {
        range: { 'metadata.modified': {} },
      };
      if (filters.metadata_modified_after) {
        rangeQuery.range!['metadata.modified'].gte =
          filters.metadata_modified_after;
      }
      if (filters.metadata_modified_before) {
        rangeQuery.range!['metadata.modified'].lte =
          filters.metadata_modified_before;
      }
      filterClauses.push(rangeQuery);
      this.logger.debug(
        'Adding metadata.modified range filter:',
        rangeQuery.range!['metadata.modified'],
      );
    }

    this.logger.debug('Built filter clauses:', JSON.stringify(filterClauses));
    return filterClauses;
  }

  /**
   * Check if a string is a valid ISO date
   */
  isValidDateString(dateStr: string): boolean {
    const date = new Date(dateStr);
    return date instanceof Date && !isNaN(date.getTime());
  }
}
