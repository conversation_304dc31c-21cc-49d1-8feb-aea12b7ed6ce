import { Injectable, Logger } from '@nestjs/common';
import { OpenSearchConfigService } from '../../opensearch/config/opensearch.config'; // Adjusted path
import { SimilarRulesQueryDto } from '../dtos/similar-rules.dto';
import { OpenSearchSearchRequestBody } from '../models/opensearch-types';
import { SimilarRulesFieldValues } from '../models/search-types'; // Added import

@Injectable()
export class SimilarRulesQueryBuilder {
  private readonly logger = new Logger(SimilarRulesQueryBuilder.name);

  constructor(private readonly configService: OpenSearchConfigService) {}

  build(
    ruleId: string,
    fieldValues: SimilarRulesFieldValues, // Uses imported interface
    options: SimilarRulesQueryDto,
    indexAlias: string, // e.g., this.indexSchema.indexAlias from SearchService
  ): OpenSearchSearchRequestBody {
    this.logger.debug(
      `Building similar rules query for ruleId: ${ruleId}, options: ${JSON.stringify(options)}`,
    );

    const searchBody = {
      min_score: options.min_similarity || 0.7,
      size: options.size || 5,
      explain: true,
      query: {
        more_like_this: {
          fields: [
            ...(fieldValues.content ? ['content^4'] : []),
            'title.value^2',
            'description^1.5',
            'metadata.mitre_techniques^3',
            'metadata.mitre_tactics^2',
            'metadata.tags^1.5',
          ],
          like: [
            {
              _index: this.configService.getIndexName(indexAlias),
              _id: ruleId,
            },
            {
              doc: {
                ...(fieldValues.content && { content: fieldValues.content }),
                'title.value': fieldValues.title,
                description: fieldValues.description,
                'metadata.mitre_techniques': fieldValues.mitre_techniques,
                'metadata.mitre_tactics': fieldValues.mitre_tactics,
                'metadata.tags': fieldValues.tags,
              },
            },
          ],
          unlike: [
            {
              _index: this.configService.getIndexName(indexAlias),
              _id: ruleId,
            },
          ],
          min_term_freq: 3,
          max_query_terms: 25,
          minimum_should_match: '50%',
          min_doc_freq: 2,
          max_doc_freq: 100,
          min_word_length: 4,
          max_word_length: 30,
          stop_words: [
            'the',
            'and',
            'or',
            'a',
            'an',
            'of',
            'to',
            'in',
            'for',
            'on',
            'with',
            'by',
            'at',
            'from',
            'up',
            'about',
            'into',
            'over',
            'after',
            'rule',
            'detection',
            'alert',
            'sigma',
            'condition',
            'pattern',
            'title',
            'description',
            'status',
            'author',
            'date',
            'level',
            'true',
            'false',
            'null',
            'undefined',
            'select',
            'where',
            'group',
          ],
          boost_terms: 1,
        },
      },
      // Highlights are not typically part of the MLT query itself for generating candidates,
      // but rather applied when displaying results. If SearchService adds highlights later, that's fine.
      // For now, this builder focuses on the core query.
    };
    this.logger.debug('Built MLT query:', JSON.stringify(searchBody, null, 2));
    return searchBody;
  }
}
