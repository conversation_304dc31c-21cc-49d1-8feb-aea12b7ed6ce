import { Injectable, Logger } from '@nestjs/common';
import { OpenSearchSearchRequestBody } from '../models/opensearch-types';

// Updated interface to match the expected OpenSearch contexts structure
interface SuggestContexts {
  [key: string]: (string | { id: string; category: string })[];
}

@Injectable()
export class SuggestQueryBuilder {
  private readonly logger = new Logger(SuggestQueryBuilder.name);

  build(
    prefix: string,
    size: number,
    contexts?: SuggestContexts, // Parameter type uses the updated interface
  ): OpenSearchSearchRequestBody {
    this.logger.debug(
      `Building suggest query with prefix: '${prefix}', size: ${size}, contexts: ${JSON.stringify(contexts)}`,
    );

    const suggestBody: OpenSearchSearchRequestBody = {
      suggest: {
        rule_title_suggestions: {
          prefix,
          completion: {
            field: 'title.suggest',
            size,
          },
        },
      },
    };

    if (contexts && suggestBody.suggest?.rule_title_suggestions?.completion) {
      suggestBody.suggest.rule_title_suggestions.completion.contexts = contexts; // This assignment should now be type-correct
      this.logger.debug('Added contexts to suggestions query:', contexts);
    }

    return suggestBody;
  }
}
