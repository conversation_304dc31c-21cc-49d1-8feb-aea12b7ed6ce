import { Injectable, Logger } from '@nestjs/common';
import { QueryUtilityService } from './query.utility';
import { AdvancedSearchQueryDto } from '../dtos/search-query.dto';
import { OpenSearchQueryClause } from '../models/opensearch-types';

@Injectable()
export class AdvancedSearchQueryBuilder {
  private readonly logger = new Logger(AdvancedSearchQueryBuilder.name);

  constructor(private readonly queryUtilityService: QueryUtilityService) {}

  build(query: AdvancedSearchQueryDto): OpenSearchQueryClause {
    this.logger.debug(
      `Building advanced search query with: ${JSON.stringify(query)}`,
    );

    const {
      filters,
      search_fields = ['title.value', 'description', 'content', 'all_text'],
      boost,
    } = query;

    const filterClauses = this.queryUtilityService.buildFilters(filters);
    this.logger.debug('Built filter clauses:', JSON.stringify(filterClauses));

    const queryText = query.query_text?.trim();

    if (!queryText) {
      // No query text, only filters apply or match_all if no filters
      if (filterClauses.length > 0) {
        return { bool: { filter: filterClauses } };
      }
      this.logger.debug(
        'Building advanced query: No queryText and no filters, defaulting to match_all',
      );
      return { match_all: {} };
    }

    this.logger.debug(`Original queryText for advanced search: "${queryText}"`);

    const isMultiWordQuery = queryText.includes(' ') && queryText.length > 1;
    const mainQueryClauses: { must?: any[]; should?: any[]; filter?: any[] } = {
      filter: filterClauses.length > 0 ? filterClauses : undefined,
    };
    const shouldClauses: any[] = [];
    const mustClauses: any[] = [];

    const phraseSpecificBoostedFields = search_fields.map((field) => {
      const fieldName = field.replace('.value', '');
      const boostValue =
        boost?.[fieldName] ||
        (field.startsWith('title')
          ? 4
          : field.startsWith('description')
            ? 3
            : field.includes('all_text') // all_text is important for phrase
              ? 2.5
              : 2);
      return `${field}^${boostValue}`;
    });

    const generalPurposeBoostedFields = search_fields.map((field) => {
      const fieldName = field.replace('.value', '');
      const boostValue =
        boost?.[fieldName] ||
        (field.startsWith('title')
          ? 3
          : field.startsWith('description')
            ? 2
            : 1);
      return `${field}^${boostValue}`;
    });

    if (isMultiWordQuery) {
      this.logger.debug(`Multi-word query detected: "${queryText}"`);
      // MUST MATCH EXACT PHRASE
      mustClauses.push({
        multi_match: {
          query: queryText,
          fields: phraseSpecificBoostedFields,
          type: 'phrase',
          analyzer: 'synonym_analyzer',
          slop: 0,
          boost: 500, // Extremely high boost to ensure it's prioritized
        },
      });
      this.logger.debug(
        `Added MUST exact phrase match for "${queryText}" with boost 500`,
        mustClauses[mustClauses.length - 1],
      );

      // SHOULD: Relaxed phrase match (for scoring of docs already matching exact phrase)
      shouldClauses.push({
        multi_match: {
          query: queryText,
          fields: phraseSpecificBoostedFields,
          type: 'phrase',
          analyzer: 'synonym_analyzer',
          slop: 2, // Allow a few words in between
          boost: 50, // Moderate boost for relaxed phrase
        },
      });
      this.logger.debug(
        `Added SHOULD relaxed phrase match for "${queryText}" with boost 50`,
        shouldClauses[shouldClauses.length - 1],
      );

      // SHOULD: All terms present (AND logic, for scoring)
      shouldClauses.push({
        multi_match: {
          query: queryText,
          fields: generalPurposeBoostedFields,
          type: 'cross_fields',
          operator: 'AND',
          analyzer: 'synonym_analyzer',
          boost: 30, // Lower boost for AND terms if exact phrase already matched
        },
      });
      this.logger.debug(
        `Added SHOULD AND terms match for "${queryText}" with boost 30`,
        shouldClauses[shouldClauses.length - 1],
      );
    } else {
      // Single-word query
      this.logger.debug(`Single-word query detected: "${queryText}"`);
      shouldClauses.push({
        multi_match: {
          query: queryText,
          fields: phraseSpecificBoostedFields, // Use higher boosts for primary term
          type: 'phrase', // Effectively a term query with the specified analyzer
          analyzer: 'synonym_analyzer',
          boost: 200, // Still a high boost
        },
      });
      this.logger.debug(
        `Added SHOULD single-word primary match for "${queryText}" with boost 200`,
        shouldClauses[shouldClauses.length - 1],
      );
    }

    // --- Identifier Logic (SHOULD clauses, for scoring) ---
    const potentialIdentifiers =
      this.queryUtilityService.extractPotentialIdentifiers(queryText);
    if (potentialIdentifiers.length > 0) {
      this.logger.debug(
        'Potential identifiers extracted for SHOULD clauses:',
        potentialIdentifiers,
      );
      potentialIdentifiers.forEach((identifier) => {
        if (this.queryUtilityService.isMitreTechniqueIdPattern(identifier)) {
          shouldClauses.push({
            nested: {
              path: 'metadata.mitre_attack',
              query: {
                prefix: {
                  'metadata.mitre_attack.mitre_id': {
                    value: identifier.toUpperCase(),
                    boost: 180,
                  },
                },
              },
            },
          });
          this.logger.debug(
            `Added SHOULD MITRE ID prefix match for ${identifier.toUpperCase()} with boost 180`,
            shouldClauses[shouldClauses.length - 1],
          );
        }

        if (search_fields.includes('title.value')) {
          shouldClauses.push({
            term: {
              'title.value.keyword': {
                value: identifier.toLowerCase(),
                boost: 150,
              },
            },
          });
        }
        if (search_fields.includes('description')) {
          shouldClauses.push({
            term: {
              'description.keyword': {
                value: identifier.toLowerCase(),
                boost: 130,
              },
            },
          });
        }

        if (
          identifier.includes(' ') &&
          !this.queryUtilityService.isMitreTechniqueIdPattern(identifier)
        ) {
          if (search_fields.includes('title.value')) {
            shouldClauses.push({
              match_phrase: {
                'title.value': {
                  query: identifier,
                  boost: 120,
                  analyzer: 'simple_text_analyzer',
                },
              },
            });
          }
        }
      });
    }

    shouldClauses.push({
      multi_match: {
        query: queryText,
        fields: generalPurposeBoostedFields,
        type: 'cross_fields',
        operator: 'OR',
        minimum_should_match: '1',
        analyzer: 'synonym_analyzer',
        boost: 5,
      },
    });
    this.logger.debug(
      `Added SHOULD general cross_fields OR match for "${queryText}" with boost 5`,
      shouldClauses[shouldClauses.length - 1],
    );

    const queryTerms = queryText
      .toLowerCase()
      .split(/\s+/)
      .filter((t) => t.length > 1);
    if (queryTerms.length > 0) {
      shouldClauses.push({
        terms: {
          'metadata.tags': queryTerms,
          boost: 40,
        },
      });
      shouldClauses.push({
        terms: {
          'metadata.mitre_tactics': queryTerms,
          boost: 40,
        },
      });
      shouldClauses.push({
        terms: {
          'metadata.mitre_techniques': queryTerms,
          boost: 40,
        },
      });
      this.logger.debug(
        `Added SHOULD terms match for metadata (tags, tactics, techniques) for terms: ${queryTerms.join(
          ', ',
        )} with boost 40`,
      );
    }

    const mitreIdAsQuery = queryText.toUpperCase();
    shouldClauses.push({
      nested: {
        path: 'metadata.mitre_attack',
        query: {
          bool: {
            should: [
              {
                prefix: {
                  'metadata.mitre_attack.mitre_id': {
                    value: mitreIdAsQuery,
                    boost: 60,
                  },
                },
              },
              {
                match: {
                  'metadata.mitre_attack.name': {
                    query: queryText,
                    analyzer: 'synonym_analyzer',
                    boost: 40,
                  },
                },
              },
              {
                match: {
                  'metadata.mitre_attack.parent_name': {
                    query: queryText,
                    analyzer: 'synonym_analyzer',
                    boost: 30,
                  },
                },
              },
            ],
            minimum_should_match: 1,
          },
        },
        boost: 50,
      },
    });
    this.logger.debug(
      `Added SHOULD nested MITRE ATT&CK match for "${queryText}" with block boost 50`,
      shouldClauses[shouldClauses.length - 1],
    );

    shouldClauses.push({
      multi_match: {
        query: queryText,
        fields: [
          'title.value.edge_ngram^0.7',
          'description.edge_ngram^0.6',
          'content.edge_ngram^0.5',
          'all_text.edge_ngram^0.4',
        ],
        type: 'most_fields',
        operator: 'OR',
        boost: 2,
      },
    });
    this.logger.debug(
      `Added SHOULD edge_ngram match for "${queryText}" with boost 2`,
      shouldClauses[shouldClauses.length - 1],
    );

    if (mustClauses.length > 0) {
      mainQueryClauses.must = mustClauses;
    }

    let minimumShouldMatchValue: number | undefined = undefined;
    if (shouldClauses.length > 0) {
      mainQueryClauses.should = shouldClauses;
      minimumShouldMatchValue = mustClauses.length > 0 ? 0 : 1;
    } else if (mustClauses.length === 0) {
      this.logger.warn(
        `Query text "${queryText}" resulted in no MUST or SHOULD clauses. Returning match_none if no filters.`,
      );
      if (!mainQueryClauses.filter) return { match_none: {} };
    }

    const finalQueryBody: OpenSearchQueryClause = { bool: mainQueryClauses };
    if (minimumShouldMatchValue !== undefined && mainQueryClauses.should) {
      finalQueryBody.bool!.minimum_should_match = minimumShouldMatchValue;
    }

    this.logger.debug(
      'Final advanced search query bool structure:',
      JSON.stringify(finalQueryBody, null, 2),
    );
    return finalQueryBody;
  }
}
