import {
  Controller,
  Get,
  Post,
  Query,
  Body,
  Param,
  HttpStatus,
  UsePipes,
  ValidationPipe,
  Req,
  ForbiddenException,
  InternalServerErrorException,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { SearchService } from './search.service';
import {
  BasicSearchQueryDto,
  AdvancedSearchQueryDto,
} from './dtos/search-query.dto';
import {
  SearchResponseDto,
  SearchSuggestionsResponseDto,
  SearchMetadataResponseDto,
} from './dtos/search-response.dto';
import {
  SimilarRulesQueryDto,
  SimilarRulesResponseDto,
} from './dtos/similar-rules.dto';
import { SearchQuery } from './decorators/search-query.decorator';
import { RequireFgaPermission } from '../auth/decorators/require-fga-permission.decorator';
import {
  WithVisibleResources,
  getVisibleResources,
} from '../auth/decorators/with-visible-resources.decorator';
import { FGAType, FGARelation } from '../auth/fga/fga.enums';
import { getWorkspaceId } from '../common/config-helpers';
import { FgaService } from '../auth/fga/fga.service';
import {
  PageSearchOptionsDto,
  PagedSearchResponseDto,
} from './dtos/page-search-options.dto';
import { PaginationMode } from './models/pagination-mode.enum';

/**
 * Controller for search operations
 */
@ApiTags('Search')
@ApiBearerAuth()
@Controller('api')
export class SearchController {
  private readonly logger = new Logger(SearchController.name);

  constructor(
    private readonly searchService: SearchService,
    private readonly fgaService: FgaService,
  ) {}

  /**
   * Process owner_ids based on request and visible groups
   * @param requestedOwnerIds Optional array of requested owner IDs to include
   * @param excludedOwnerIds Optional array of owner IDs to exclude
   * @param visibleGroupIds Array of group IDs visible to the user
   * @returns Object containing arrays of owner IDs to include and exclude
   */
  private processOwnerIds(
    requestedOwnerIds: string[] | undefined,
    excludedOwnerIds: string[] | undefined,
    visibleGroupIds: string[],
  ): { include: string[]; exclude: string[] } {
    return {
      // If specific owner_ids requested, use them (after validation)
      // Otherwise use all visible groups
      include: requestedOwnerIds?.length ? requestedOwnerIds : visibleGroupIds,
      // Use excluded owner_ids if provided, otherwise empty array
      exclude: excludedOwnerIds || [],
    };
  }

  /**
   * Validates that the user has access to all requested groups
   * @param userId The external user ID
   * @param groupIds Array of group IDs to validate access for
   * @param excludedGroupIds Optional array of excluded group IDs to validate access for
   * @throws ForbiddenException if user doesn't have access to any of the groups
   */
  private async validateGroupAccess(
    userId: string,
    groupIds: string[],
    excludedGroupIds?: string[],
  ): Promise<void> {
    // Validate included groups
    if (groupIds?.length) {
      for (const groupId of groupIds) {
        const hasAccess = await this.validateSingleGroupAccess(userId, groupId);
        if (!hasAccess) {
          throw new ForbiddenException(
            `User does not have access to group: ${groupId}`,
          );
        }
      }
    }

    // Validate excluded groups
    if (excludedGroupIds?.length) {
      for (const groupId of excludedGroupIds) {
        const hasAccess = await this.validateSingleGroupAccess(userId, groupId);
        if (!hasAccess) {
          throw new ForbiddenException(
            `User does not have access to excluded group: ${groupId}`,
          );
        }
      }
    }
  }

  /**
   * Validates user's access to a single group
   * @param userId The external user ID
   * @param groupId Group ID to validate access for
   * @returns Boolean indicating if user has access
   */
  private async validateSingleGroupAccess(
    userId: string,
    groupId: string,
  ): Promise<boolean> {
    return this.fgaService.checkUserAuthorization({
      user_type: FGAType.USER,
      user_id: userId,
      relation: FGARelation.GROUP_CONTENT_VIEWER,
      object_type: FGAType.GROUP,
      object_id: groupId,
    });
  }

  /**
   * Basic search endpoint (Legacy - Prefer GET /v2/search)
   * @deprecated Use GET /v2/search instead.
   * @param query Search query parameters
   * @returns Search results (Cursor-based)
   */
  @ApiOperation({
    summary: '[V1 DEPRECATED] Search detection rules (Use GET /v2/search)',
    description:
      '[DEPRECATED] Performs a basic search. Primarily uses cursor-based pagination internally. ' +
      'Please migrate to the GET /v2/search endpoint which defaults to offset pagination and allows choosing the mode via query parameters.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Search results returned successfully.',
    type: SearchResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Neither search query nor filters provided.',
  })
  @ApiQuery({
    name: 'q',
    required: false,
    description: 'Search query text. If not provided, returns all rules.',
    example: 'process execution',
  })
  @ApiQuery({
    name: 'created_after',
    required: false,
    description:
      'Filter rules created after this date. Format: YYYY-MM-DD (e.g., 2025-01-01) or full ISO string (e.g., 2025-01-01T00:00:00.000Z)',
    example: '2025-01-01',
  })
  @ApiQuery({
    name: 'created_before',
    required: false,
    description:
      'Filter rules created before this date. Format: YYYY-MM-DD (e.g., 2025-01-01) or full ISO string (e.g., 2025-01-01T23:59:59.999Z)',
    example: '2025-01-01',
  })
  @ApiQuery({
    name: 'rule_types',
    required: false,
    description: 'Filter by rule types',
    isArray: true,
    example: ['SIGMA', 'KQL'],
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description:
      'Page number (1-based). Note: This endpoint primarily uses cursor pagination; page is informational.',
    example: 1,
  })
  @ApiQuery({
    name: 'size',
    required: false,
    description: 'Number of results per page',
    example: 20,
  })
  @Get('v1/search')
  @UsePipes(new ValidationPipe({ transform: true }))
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,
    (req) => getWorkspaceId(),
  )
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_CONTENT_VIEWER)
  async search(
    @Req() req: any,
    @SearchQuery() query: BasicSearchQueryDto,
    @Query('page') page?: number,
  ): Promise<SearchResponseDto> {
    const visibleGroupIds = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_CONTENT_VIEWER,
    );

    // Process owner_ids based on visible groups and user's request
    const { include: ownerIds, exclude: excludedOwnerIds } =
      this.processOwnerIds(
        query.filters?.owner_ids,
        query.filters?.exclude?.owner_ids,
        visibleGroupIds,
      );

    // Validate that user has access to all requested groups
    await this.validateGroupAccess(req.user.sub, ownerIds, excludedOwnerIds);

    // Update filters with processed owner_ids
    const filters = {
      ...query.filters,
      owner_ids: ownerIds,
      exclude: {
        ...query.filters?.exclude,
        owner_ids: excludedOwnerIds,
      },
    };

    // If page is provided as a query parameter, add it to the options
    if (page) {
      query.options = {
        ...query.options,
        page,
      };
    }

    return this.searchService.search(query.q, filters, query.options);
  }

  /**
   * Advanced search endpoint (Uses cursor pagination via request body options)
   * @param req
   * @param query Advanced search query
   * @returns Search results (Cursor-based metadata)
   */
  @ApiOperation({
    summary: 'Advanced search for detection rules (Cursor/Offset Pagination)',
    description: `Advanced search with more complex query options and filters. Pagination (cursor or offset) is controlled via the \'options.pagination_mode\' field in the request body. Defaults to cursor pagination if not specified.`,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Advanced search results returned successfully.',
    type: PagedSearchResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid request body structure or missing required fields.',
  })
  @ApiBody({ type: AdvancedSearchQueryDto })
  @Post('v1/search/advanced')
  @UsePipes(new ValidationPipe({ transform: true }))
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,
    (req) => getWorkspaceId(),
  )
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_CONTENT_VIEWER)
  async advancedSearch(
    @Req() req: any,
    @Body() query: AdvancedSearchQueryDto,
  ): Promise<PagedSearchResponseDto> {
    this.logger.debug(
      `Controller ENTRY: query.options: ${JSON.stringify(query.options)}, query.options.pagination_mode: ${query.options?.pagination_mode}`,
    );
    this.logger.log(
      `Advanced search by '${req.user.sub}' query: '${query.query_text}' filters: ${JSON.stringify(query.filters)}`,
    );

    const visibleGroupIds = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_CONTENT_VIEWER,
    );

    // Validate access to requested groups if present
    await this.validateGroupAccess(
      req.user.sub,
      query.filters?.owner_ids || [],
      query.filters?.exclude?.owner_ids,
    );

    // Process and override owner_ids
    const processedOwnerIds = this.processOwnerIds(
      query.filters?.owner_ids,
      query.filters?.exclude?.owner_ids,
      visibleGroupIds,
    );

    // Ensure query.options exists, defaulting if necessary
    const queryOptions = query.options || {};

    // Log the state of queryOptions before calling the service
    this.logger.debug(
      `Controller: query.options (pre-service): ${JSON.stringify(queryOptions)}`,
    );
    this.logger.debug(
      `Controller: query.options.pagination_mode (pre-service): ${queryOptions.pagination_mode}`,
    );

    const modifiedQuery = {
      ...query,
      options: queryOptions,
      filters: {
        ...query.filters,
        owner_ids: processedOwnerIds.include,
        exclude: {
          ...query.filters?.exclude,
          owner_ids: processedOwnerIds.exclude,
        },
      },
    };
    // Call the service, which now returns PagedSearchResponseDto
    return this.searchService.advancedSearch(modifiedQuery);
  }

  /**
   * Similar rules search endpoint
   * @param ruleId Reference rule ID
   * @param query Similar rules query parameters
   * @returns Similar rules
   */
  @ApiOperation({ summary: 'Find similar rules' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Similar rules returned successfully.',
    type: SimilarRulesResponseDto,
  })
  @ApiParam({
    name: 'ruleId',
    description: 'Reference rule ID',
    example: 'rule-123',
  })
  @Get('v1/search/similar/:ruleId')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,
    (req) => getWorkspaceId(),
  )
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_CONTENT_VIEWER)
  async findSimilarRules(
    @Req() req: any,
    @Param('ruleId') ruleId: string,
    @Query() query: SimilarRulesQueryDto,
  ): Promise<SimilarRulesResponseDto> {
    const visibleGroupIds = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_CONTENT_VIEWER,
    );
    console.log(
      'Visible group IDs for similar rules endpoint:',
      visibleGroupIds,
    );
    return this.searchService.findSimilarRules(ruleId, query);
  }

  /**
   * Search suggestions endpoint
   * @param q Search prefix
   * @param size Number of suggestions to return
   * @returns Search suggestions
   */
  @Get('v1/search/suggestions')
  @ApiOperation({ summary: 'Get search suggestions' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Search suggestions returned successfully.',
    type: SearchSuggestionsResponseDto,
  })
  @ApiQuery({
    name: 'q',
    description: 'Search prefix',
    example: 'lateral',
  })
  @ApiQuery({
    name: 'size',
    description: 'Number of suggestions to return',
    required: false,
    example: 5,
  })
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,
    (req) => getWorkspaceId(),
  )
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_CONTENT_VIEWER)
  async getSuggestions(
    @Req() req: any,
    @Query('q') q: string,
    @Query('size') size?: number,
  ): Promise<SearchSuggestionsResponseDto> {
    const visibleGroupIds = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_CONTENT_VIEWER,
    );
    this.logger.debug(
      'Visible group IDs for suggestions endpoint:',
      visibleGroupIds,
    );
    return this.searchService.getSuggestions(q, size, visibleGroupIds);
  }

  /**
   * Search metadata endpoint
   * @returns Search metadata
   */
  @Get('v1/search/metadata')
  @ApiOperation({ summary: 'Get search metadata' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Search metadata returned successfully.',
    type: SearchMetadataResponseDto,
  })
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,
    (req) => getWorkspaceId(),
  )
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_CONTENT_VIEWER)
  async getMetadata(@Req() req: any): Promise<SearchMetadataResponseDto> {
    const visibleGroupIds = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_CONTENT_VIEWER,
    );
    console.log('Visible group IDs for metadata endpoint:', visibleGroupIds);
    return this.searchService.getMetadata();
  }

  /**
   * Page-based search endpoint (Legacy - Prefer GET /v2/search)
   * @deprecated Use GET /v2/search instead.
   * @param query Search query parameters
   * @returns Search results with page information
   */
  @Get('v1/search/paged')
  @ApiOperation({
    summary:
      '[V1 DEPRECATED] Search detection rules with pagination (Use GET /v2/search)',
    description:
      '[DEPRECATED] Search for detection rules using query text and/or filters, with pagination support. ' +
      "Supports both cursor-based and offset-based pagination via the 'pagination_mode' parameter. " +
      'Please migrate to GET /v2/search.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Search results returned successfully.',
    type: PagedSearchResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Neither search query nor filters provided.',
  })
  @ApiQuery({
    name: 'pagination_mode',
    required: false,
    description: 'Pagination strategy to use',
    enum: PaginationMode,
    default: PaginationMode.CURSOR,
  })
  @ApiQuery({
    name: 'cursor',
    required: false,
    description:
      "Cursor for cursor-based pagination. Used only when 'pagination_mode' is 'cursor'.",
    example:
      'eyJzb3J0X3ZhbHVlcyI6WzE2NzgwOTYwMDAwMDAsIjQyIl0sImlkIjoiNDIiLCJzb3J0X2ZpZWxkIjoiY3JlYXRlZF9hdCIsInNvcnRfb3JkZXIiOiJkZXNjIn0=',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description:
      "Page number (1-based). Used only when 'pagination_mode' is 'offset'.",
    example: 1,
  })
  @ApiQuery({
    name: 'size',
    required: false,
    description: 'Number of results per page',
    example: 20,
  })
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,
    (req) => getWorkspaceId(),
  )
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_CONTENT_VIEWER)
  async pagedSearch(
    @Req() req: any,
    @SearchQuery() query: BasicSearchQueryDto,
    @Query('page') page?: number,
    @Query('size') size?: number,
    @Query('cursor') cursor?: string,
    @Query('pagination_mode') paginationMode?: PaginationMode,
  ): Promise<PagedSearchResponseDto> {
    const visibleGroupIds = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_CONTENT_VIEWER,
    );

    // Process owner_ids for filtering
    const { include: ownerIds, exclude: excludedOwnerIds } =
      this.processOwnerIds(
        query.filters?.owner_ids,
        query.filters?.exclude?.owner_ids,
        visibleGroupIds,
      );

    // Validate access to all requested groups
    await this.validateGroupAccess(req.user.sub, ownerIds, excludedOwnerIds);

    // Override options with page information
    const options: PageSearchOptionsDto = {
      ...query.options,
      page: page || 1,
      size: size || 20,
      cursor: cursor,
      pagination_mode: paginationMode || PaginationMode.CURSOR,
    };

    // Update filters with processed owner_ids
    const filters = {
      ...query.filters,
      owner_ids: ownerIds,
      exclude: {
        ...query.filters?.exclude,
        owner_ids: excludedOwnerIds,
      },
    };

    return this.searchService.pagedSearch(query.q, filters, options);
  }

  // --- V2 Endpoints --- //

  /**
   * V2 Search Endpoint: Basic search with configurable pagination.
   * Defaults to offset pagination.
   * @param query Search query parameters from decorator
   * @param page Page number for offset pagination
   * @param size Number of results per page
   * @param cursor Cursor for cursor pagination
   * @param paginationMode Pagination mode ('offset' or 'cursor')
   * @returns Paged search results
   */
  @Get('v2/search')
  @ApiOperation({
    summary:
      'V2 Search: Search detection rules with pagination (default: offset)',
    description:
      'Search for detection rules using query text and/or filters. ' +
      "Defaults to offset-based pagination. Use 'pagination_mode=cursor' and the 'cursor' parameter for cursor-based pagination. " +
      'Filters can be provided either as top-level query parameters or nested under filters[].',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Search results returned successfully.',
    type: PagedSearchResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid query parameters or filters provided.',
  })
  @ApiQuery({ name: 'q', required: false /*...*/ })
  @ApiQuery({ name: 'created_after', required: false /*...*/ })
  @ApiQuery({
    name: 'pagination_mode',
    required: false,
    description: 'Pagination strategy to use (offset or cursor)',
    enum: PaginationMode,
    default: PaginationMode.OFFSET,
  })
  @ApiQuery({
    name: 'cursor',
    required: false,
    description:
      "Cursor for cursor-based pagination. Used only when 'pagination_mode' is 'cursor'.",
    example:
      'eyJzb3J0X3ZhbHVlcyI6WzE2NzgwOTYwMDAwMDAsIjQyIl0sImlkIjoiNDIiLCJzb3J0X2ZpZWxkIjoiY3JlYXRlZF9hdCIsInNvcnRfb3JkZXIiOiJkZXNjIn0=',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description:
      "Page number (1-based). Used primarily when 'pagination_mode' is 'offset'.",
    default: 1,
    example: 1,
  })
  @ApiQuery({
    name: 'size',
    required: false,
    description: 'Number of results per page',
    default: 20,
    example: 20,
  })
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,
    (req) => getWorkspaceId(),
  )
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_CONTENT_VIEWER)
  @UsePipes(new ValidationPipe({ transform: true }))
  async searchV2(
    @Req() req: any,
    @SearchQuery() query: BasicSearchQueryDto,
    @Query('page') page?: number,
    @Query('size') size?: number,
    @Query('cursor') cursor?: string,
    @Query('pagination_mode') paginationMode?: PaginationMode,
  ): Promise<PagedSearchResponseDto> {
    const visibleGroupIds = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_CONTENT_VIEWER,
    );

    const { include: ownerIds, exclude: excludedOwnerIds } =
      this.processOwnerIds(
        query.filters?.owner_ids,
        query.filters?.exclude?.owner_ids,
        visibleGroupIds,
      );

    await this.validateGroupAccess(req.user.sub, ownerIds, excludedOwnerIds);

    const options: PageSearchOptionsDto = {
      ...query.options,
      page: page || 1,
      size: size || 20,
      cursor: cursor,
      pagination_mode: paginationMode || PaginationMode.OFFSET,
    };

    const filters = {
      ...query.filters,
      owner_ids: ownerIds,
      exclude: {
        ...query.filters?.exclude,
        owner_ids: excludedOwnerIds,
      },
    };

    return this.searchService.pagedSearch(query.q, filters, options);
  }
}
