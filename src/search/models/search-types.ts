import { SortField } from './sort-field.enum';
import { SortOrder } from './sort-order.enum';

/**
 * Interface for field values used in constructing a 'more_like_this' query
 * for finding similar rules.
 */
export interface SimilarRulesFieldValues {
  content?: string;
  title?: string;
  description?: string;
  mitre_techniques?: string[];
  mitre_tactics?: string[];
  tags?: string[];
}

// Add other shared search-related types here in the future if needed.

// Added SearchCursor interface
export interface SearchCursor {
  sort_values: (string | number | boolean)[];
  id: string;
  sort_field: SortField;
  sort_order: SortOrder;
}
