import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  Query,
  HttpStatus,
  HttpCode,
  Req,
  ForbiddenException,
  UnauthorizedException,
  NotFoundException,
  BadRequestException,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { RulesService } from './rules.service';
import {
  CreateRuleDto,
  UpdateRuleDto,
  Rule,
  BulkDownloadRulesDto,
  BulkDownloadResponseDto,
  RuleWithUserAndGroup,
  RuleStatus,
  SingleRuleDownloadResponseDto,
  RuleOrderBy,
  ResetRulesToDraftResponseDto,
  UserBulkLookupRequestDto,
  UserBulkLookupResponseDto,
  GroupBulkLookupRequestDto,
  GroupBulkLookupResponseDto,
} from './models/rule.model';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { UserGroupsService } from '../auth/services/user-groups.service';
import { FgaService, FGATuple } from '../auth/fga/fga.service';
import { FGARelation } from '../auth/fga/fga.enums';
import { FGAType } from '../auth/fga/fga.enums';
import { setConfigService, getWorkspaceId } from '../common/config-helpers';
import {
  WithVisibleResources,
  getVisibleResources,
} from '../auth/decorators/with-visible-resources.decorator';
import { ConfigService } from '@nestjs/config';
import { RequireFgaPermission } from '../auth/decorators/require-fga-permission.decorator';
import { RequireFgaPermissionOnAll } from '../auth/decorators/require-fga-permission-on-all.decorator';
import { DetailedUserResponse } from '../auth/interfaces/user-response.interface';
import {
  ApiResponseDto,
  createApiResponse,
} from '../common/dto/api-response.dto';
import {
  PaginatedApiResponseDto,
  ApiPaginationParams,
  createPaginatedApiResponse,
} from '../common/dto/pagination.dto';
import { ErrorResponseDto } from '../common/dto/error-response.dto';
import { BookmarksService } from '../bookmarks/bookmarks.service';
import { EnrichmentHelper } from '../common/enrichment-helper';
import { Auth0Guard } from '../auth/guards/auth0.guard';
import { RuleInteractionsService } from '../rule-interactions/rule-interactions.service';
import {
  RuleInteractionType,
  RuleInteractionResponse,
  RuleInteraction,
  RuleInteractionWithRuleDetails,
  RuleInteractionWithUserDetails,
} from '../rule-interactions/models/rule-interaction.model';
import { getPaginationSizeAndPage } from '../common/helpers/pagination-helpers';
import { SearchService } from '../search/search.service';
import { SortOrder } from '../search/models/sort-order.enum';
import { mapOpenSearchRuleToRule } from '../common/helpers/rule-mappers';
import { PagedSearchResponseDto } from '../search/dtos/page-search-options.dto';
import { getSortParams } from '../common/helpers/sort-helpers';
import { ParsingService } from '../parsing/parsing.service';
import { ParseRuleResult } from '../parsing/parsing.types';
import { RuleType } from './models/rule.model';
import { RuleMetadata } from './models/rule-metadata.model';
/**
 * Controller for rule management endpoints
 */
@ApiTags('Rules')
@ApiBearerAuth()
@Controller('api/v1/rules')
export class RulesController {
  private readonly logger: Logger;

  constructor(
    private readonly rulesService: RulesService,
    private readonly userService: UserGroupsService,
    private readonly fgaService: FgaService,
    private readonly configService: ConfigService,
    private readonly bookmarksService: BookmarksService,
    private readonly enrichmentHelper: EnrichmentHelper,
    private readonly ruleInteractionsService: RuleInteractionsService,
    private readonly searchService: SearchService,
    private readonly parsingService: ParsingService,
  ) {
    this.logger = new Logger(RulesController.name);
  }

  onModuleInit() {
    setConfigService(this.configService);
  }

  /**
   * Get rules created by the current user
   * @param req
   * @param paginationParams
   * @param sortBy
   * @param sortOrder
   * @param q
   * @param status Optional rule status filter
   * @param mitre_attack_ids
   * @param mitre_tactics
   * @param mitre_techniques
   * @returns List of rules created by the current user
   */
  @ApiOperation({ summary: 'Get rules created by the current user' })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'size',
    description: 'Number of rules per page',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiQuery({
    name: 'sort_by',
    description: 'Sort by',
    required: false,
    type: String,
    enum: RuleOrderBy,
    example: 'published_at',
  })
  @ApiQuery({
    name: 'sort_order',
    description: 'Sort order',
    required: false,
    type: String,
    enum: SortOrder,
    example: 'asc',
  })
  @ApiQuery({
    name: 'q',
    description: 'Search query',
    required: false,
    type: String,
    example: 'process execution',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter rules by status',
    enum: RuleStatus,
    enumName: 'RuleStatus',
  })
  @ApiQuery({
    name: 'mitre_attack_ids',
    required: false,
    description:
      'Filter rules by specific MITRE ATT&CK object IDs (e.g., T1059, TA0002)',
    type: [String],
    isArray: true,
  })
  @ApiQuery({
    name: 'mitre_tactics',
    required: false,
    description: 'Filter rules by MITRE ATT&CK tactic names or IDs',
    type: [String],
    isArray: true,
  })
  @ApiQuery({
    name: 'mitre_techniques',
    required: false,
    description: 'Filter rules by MITRE ATT&CK technique IDs',
    type: [String],
    isArray: true,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of user rules returned successfully.',
    type: () => PaginatedApiResponseDto.getType(RuleWithUserAndGroup),
  })
  @Get('my-rules')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,
    (req) => getWorkspaceId(),
  )
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_CONTENT_VIEWER)
  async getMyRules(
    @Req() req: any,
    @Query() paginationParams?: ApiPaginationParams,
    @Query('sort_by') sortBy?: RuleOrderBy,
    @Query('sort_order') sortOrder?: SortOrder,
    @Query('q') q?: string,
    @Query('status') status?: RuleStatus,
    @Query('mitre_attack_ids') mitre_attack_ids?: string[],
    @Query('mitre_tactics') mitre_tactics?: string[],
    @Query('mitre_techniques') mitre_techniques?: string[],
  ): Promise<PaginatedApiResponseDto<RuleWithUserAndGroup>> {
    const internalUser = await this.getInternalUserFromRequest(req);
    const visibleGroupContent = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_CONTENT_VIEWER,
    );
    visibleGroupContent.push(internalUser.id); // Add user as owner_id to include draft rules

    const { size, page } = getPaginationSizeAndPage(paginationParams, 100);
    const { orderByField, orderByDirection } = getSortParams(sortBy, sortOrder);

    const response: PagedSearchResponseDto =
      await this.searchService.pagedSearch(
        q,
        {
          owner_ids: visibleGroupContent,
          created_by: [internalUser.id],
          statuses: status ? [status] : undefined,
          mitre_attack_ids,
          mitre_tactics,
          mitre_techniques,
        },
        {
          size,
          page,
          sort_by: orderByField,
          sort_order: orderByDirection,
        },
      );
    const result: { items: Rule[]; total: number } = {
      items: response.data.map(mapOpenSearchRuleToRule),
      total: response.meta.total,
    };

    // Enrich rules with user and group details and bookmark information
    const authorizationHeader = this.getAuthorizationHeader(req);
    const enrichedRules = await this.enrichmentHelper.enrichRulesWithDetails(
      result.items,
      authorizationHeader,
      internalUser.id,
    );

    return createPaginatedApiResponse(enrichedRules, result.total, page, size);
  }

  @ApiOperation({ summary: 'Get bookmarked rules' })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'size',
    description: 'Number of rules per page',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiQuery({
    name: 'sort_by',
    description: 'Sort by',
    required: false,
    type: String,
    enum: RuleOrderBy,
    example: 'published_at',
  })
  @ApiQuery({
    name: 'sort_order',
    description: 'Sort order',
    required: false,
    type: String,
    enum: SortOrder,
    example: 'asc',
  })
  @ApiQuery({
    name: 'mitre_attack_ids',
    required: false,
    description:
      'Filter rules by specific MITRE ATT&CK object IDs (e.g., T1059, TA0002)',
    type: [String],
    isArray: true,
  })
  @ApiQuery({
    name: 'mitre_tactics',
    required: false,
    description: 'Filter rules by MITRE ATT&CK tactic names or IDs',
    type: [String],
    isArray: true,
  })
  @ApiQuery({
    name: 'mitre_techniques',
    required: false,
    description: 'Filter rules by MITRE ATT&CK technique IDs',
    type: [String],
    isArray: true,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of bookmarked rules returned successfully.',
    type: () => PaginatedApiResponseDto.getType(RuleWithUserAndGroup),
  })
  @Get('bookmarks')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,
    (req) => getWorkspaceId(),
  )
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_CONTENT_VIEWER)
  async getBookmarkedRules(
    @Req() req: any,
    @Query() paginationParams?: ApiPaginationParams,
    @Query('sort_by') sortBy?: RuleOrderBy,
    @Query('sort_order') sortOrder?: SortOrder,
    @Query('mitre_attack_ids') mitre_attack_ids?: string[],
    @Query('mitre_tactics') mitre_tactics?: string[],
    @Query('mitre_techniques') mitre_techniques?: string[],
  ): Promise<PaginatedApiResponseDto<RuleWithUserAndGroup>> {
    const internalUser = await this.getInternalUserFromRequest(req);
    const { size, page } = getPaginationSizeAndPage(paginationParams, 100);
    const { orderByField, orderByDirection } = getSortParams(sortBy, sortOrder);

    // Get visible groups for the user
    const visibleGroupIds = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_CONTENT_VIEWER,
    );

    // Get bookmarked rule IDs with pagination
    const ruleIds = await this.bookmarksService.getAllUserBookmarkedRuleIds(
      internalUser.id,
      visibleGroupIds, // Filter by visible groups
    );

    if (ruleIds.length === 0) {
      return createPaginatedApiResponse([], 0, page, size);
    }

    const response: PagedSearchResponseDto =
      await this.searchService.pagedSearch(
        undefined,
        {
          ids: ruleIds,
          mitre_attack_ids,
          mitre_tactics,
          mitre_techniques,
        },
        {
          size,
          page,
          sort_by: orderByField,
          sort_order: orderByDirection,
        },
      );
    const result: { items: Rule[]; total: number } = {
      items: response.data.map(mapOpenSearchRuleToRule),
      total: response.meta.total,
    };

    // Enrich rules with user and group details and bookmark information
    const authorizationHeader = this.getAuthorizationHeader(req);
    const enrichedRules = await this.enrichmentHelper.enrichRulesWithDetails(
      result.items,
      authorizationHeader,
      internalUser.id,
    );

    return createPaginatedApiResponse(enrichedRules, result.total, page, size);
  }

  @Post('counts_by_user')
  @ApiOperation({
    summary: 'Get rule counts for multiple users',
    operationId: 'getCountsByUser',
  })
  @ApiResponse({
    status: 200,
    description: 'Rule counts retrieved successfully',
    type: () => ApiResponseDto.getType(UserBulkLookupResponseDto),
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  @UseGuards(Auth0Guard)
  @WithVisibleResources(
    FGAType.USER_PROFILE,
    FGARelation.USER_PROFILE_VIEWER_PUBLIC,
  )
  async getCountsByUser(
    @Req() req: any,
    @Body() bulkLookupDto: UserBulkLookupRequestDto,
  ): Promise<ApiResponseDto<UserBulkLookupResponseDto>> {
    if (!req.headers?.authorization) {
      throw new UnauthorizedException('Missing authorization header');
    }

    // Check for user in request
    if (!req.user?.sub) {
      throw new UnauthorizedException('User not found in request');
    }

    // Get the list of user IDs that the requester has permission to view
    const visibleUserIds = getVisibleResources(
      req,
      FGAType.USER_PROFILE,
      FGARelation.USER_PROFILE_VIEWER_PUBLIC,
    );

    if (bulkLookupDto.group_id) {
      const isAllowed = await this.fgaService.checkUserAuthorization({
        user_type: FGAType.USER,
        user_id: this.getExternalUserIdFromRequest(req),
        relation: FGARelation.CAN_VIEW_GROUP_DETAILS,
        object_type: FGAType.GROUP,
        object_id: bulkLookupDto.group_id,
      });
      if (!isAllowed) {
        throw new ForbiddenException('User is not allowed to view this group');
      }
    }

    // Filter the requested user IDs to only include those the user has permission to view
    const authorizedUserIds = bulkLookupDto.user_ids.filter((id) =>
      visibleUserIds.includes(id),
    );
    const counts = await this.rulesService.getCountsByUser(
      authorizedUserIds,
      bulkLookupDto.group_id,
    );
    return createApiResponse({ users: counts });
  }

  @Post('counts_by_group')
  @ApiOperation({
    summary: 'Get rule counts for multiple groups',
    operationId: 'getCountsByGroup',
  })
  @ApiResponse({
    status: 200,
    description: 'Rule counts retrieved successfully',
    type: () => ApiResponseDto.getType(GroupBulkLookupResponseDto),
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  @UseGuards(Auth0Guard)
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_CONTENT_VIEWER)
  async getCountsByGroup(
    @Req() req: any,
    @Body() bulkLookupDto: GroupBulkLookupRequestDto,
  ): Promise<ApiResponseDto<GroupBulkLookupResponseDto>> {
    if (!req.headers?.authorization) {
      throw new UnauthorizedException('Missing authorization header');
    }

    // Check for user in request
    if (!req.user?.sub) {
      throw new UnauthorizedException('User not found in request');
    }

    // Get the list of group IDs that the requester has permission to view
    const visibleGroupIds = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_CONTENT_VIEWER,
    );

    // Filter the requested group IDs to only include those the user has permission to view
    const authorizedGroupIds = bulkLookupDto.group_ids.filter((id) =>
      visibleGroupIds.includes(id),
    );
    const counts = await this.rulesService.getCountsByGroup(authorizedGroupIds);
    return createApiResponse({ groups: counts });
  }

  /**
   * Create a new rule
   * @param createRuleDto Rule creation data
   * @returns Created rule
   */
  @ApiOperation({ summary: 'Create a new rule' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'The rule has been successfully created.',
    type: () => ApiResponseDto.getType(RuleWithUserAndGroup),
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data.',
    type: ErrorResponseDto,
  })
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,
    (req) => getWorkspaceId(),
  )
  async create(
    @Body() createRuleDto: CreateRuleDto,
    @Req() req: any,
  ): Promise<ApiResponseDto<RuleWithUserAndGroup>> {
    // Set default values for title and description if they are missing or empty
    if (!createRuleDto.title) {
      createRuleDto.title = 'Untitled Detection';
    }

    if (!createRuleDto.description) {
      createRuleDto.description = '(no description)';
    }

    // Validate rule status and group_id relationship
    this.enrichmentHelper.validateRuleStatusAndGroup(
      createRuleDto.status || RuleStatus.DRAFT, // Default to DRAFT if not provided
      createRuleDto.group_id,
    );

    const internalUser = await this.getInternalUserFromRequest(req);
    const authorizationHeader = this.getAuthorizationHeader(req);

    // Fetch group details before creating the rule
    const groupDetails = await this.enrichmentHelper.fetchGroupDetails(
      createRuleDto.group_id,
      authorizationHeader,
    );

    if (createRuleDto.group_id) {
      if (!groupDetails) {
        throw new NotFoundException(
          `Group '${createRuleDto.group_id}' not found`,
        );
      }
      // Check if the user has permission to publish to the group
      const isAllowed = await this.fgaService.checkUserAuthorization({
        user_type: FGAType.USER,
        user_id: this.getExternalUserIdFromRequest(req),
        relation: FGARelation.GROUP_CONTENT_PUBLISHER,
        object_type: FGAType.GROUP,
        object_id: createRuleDto.group_id,
      });
      if (!isAllowed) {
        throw new ForbiddenException(
          'User is not allowed to create rules for this group',
        );
      }
    }

    const effectiveCreateDto = { ...createRuleDto }; // Start with the input DTO

    // Check if content needs parsing (e.g., SIGMA rule with content provided)
    if (createRuleDto.rule_type && createRuleDto.content) {
      // Further check if the rule_type is one that we actually parse, e.g., SIGMA, KQL
      // This prevents trying to parse, for example, an UNKNOWN type that has content by mistake.
      if (createRuleDto.rule_type === RuleType.SIGMA) {
        this.logger.debug(
          `Parsing content for rule: ${createRuleDto.title}, type: ${createRuleDto.rule_type}`,
        );
        const parsedRuleData: ParseRuleResult =
          await this.parsingService.parseRule({
            content: createRuleDto.content,
            fileName: createRuleDto.title || 'untitled_rule',
            existingMitreAttack: createRuleDto.metadata?.mitre_attack,
          });

        if (!parsedRuleData.success || !parsedRuleData.metadata) {
          this.logger.error(
            `Failed to parse rule content for "${createRuleDto.title}": ${parsedRuleData.error}`,
          );
          throw new BadRequestException(
            `Failed to parse rule content: ${parsedRuleData.error || 'Unknown parsing error'}`,
          );
        }

        // Update effectiveCreateDto with parsed data
        // Parsed data should take precedence for relevant fields
        effectiveCreateDto.title = parsedRuleData.title || createRuleDto.title;
        effectiveCreateDto.description =
          parsedRuleData.description || createRuleDto.description;

        if (parsedRuleData.tags && parsedRuleData.tags.length > 0) {
          effectiveCreateDto.tags = parsedRuleData.tags;
        }

        if (parsedRuleData.metadata) {
          const dtoMeta = createRuleDto.metadata || {};
          const parsedMetaFromSigma = parsedRuleData.metadata; // Known to be StrictRuleMetadata

          // Start with dtoMeta, then selectively overwrite/add from parsedMetaFromSigma
          // only if the value in parsedMetaFromSigma is not undefined.
          const mergedMetadata: RuleMetadata = { ...dtoMeta };

          for (const key in parsedMetaFromSigma) {
            if (
              Object.prototype.hasOwnProperty.call(parsedMetaFromSigma, key)
            ) {
              const parsedValue = parsedMetaFromSigma[key];
              if (parsedValue !== undefined) {
                mergedMetadata[key] = parsedValue;
              }
              // If parsedValue is undefined, we keep what was in dtoMeta (or nothing if dtoMeta didn't have the key)
            }
          }
          effectiveCreateDto.metadata = mergedMetadata;
        }

        if (parsedRuleData.test_cases && parsedRuleData.test_cases.length > 0) {
          effectiveCreateDto.test_cases = parsedRuleData.test_cases;
        } else if (!createRuleDto.test_cases) {
          effectiveCreateDto.test_cases = [];
        }

        this.logger.debug(
          `Rule content parsed successfully for "${effectiveCreateDto.title}". Effective DTO metadata: ${JSON.stringify(effectiveCreateDto.metadata)}`,
        );
      }
    }

    const createdRule = await this.rulesService.create(
      effectiveCreateDto, // Use the potentially modified DTO
      internalUser.id,
      internalUser.username,
      groupDetails?.name,
    );

    await this.createNewRuleTuples(
      createdRule.id,
      this.getExternalUserIdFromRequest(req),
      createRuleDto.group_id,
    );

    // Use pre-fetched details to enrich the rule
    const enrichedRule = this.enrichmentHelper.enrichSingleRuleWithDetails(
      createdRule,
      internalUser,
      groupDetails,
    );
    return createApiResponse(enrichedRule);
  }

  /**
   * Get all rules with optional filtering
   * @param ruleType Optional rule type filter
   * @param tag Optional tag filter
   * @returns List of rules
   */
  @ApiOperation({ summary: 'Get all published rules' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of rules returned successfully.',
    type: () => PaginatedApiResponseDto.getType(RuleWithUserAndGroup),
  })
  @ApiQuery({
    name: 'q',
    required: false,
    description: 'Search query text. If not provided, returns all rules.',
    example: 'process execution',
  })
  @ApiQuery({
    name: 'sort_by',
    required: false,
    description: 'Order rules by',
    enum: RuleOrderBy,
    enumName: 'RuleOrderBy',
    example: RuleOrderBy.PUBLISHED_AT,
    default: RuleOrderBy.PUBLISHED_AT,
  })
  @ApiQuery({
    name: 'sort_order',
    required: false,
    description: 'Sort order',
    enum: SortOrder,
    enumName: 'SortOrder',
    example: SortOrder.DESC,
    default: SortOrder.DESC,
  })
  @ApiQuery({
    name: 'mitre_attack_ids',
    required: false,
    description:
      'Filter rules by specific MITRE ATT&CK object IDs (e.g., T1059, TA0002)',
    type: [String],
    isArray: true,
  })
  @ApiQuery({
    name: 'mitre_tactics',
    required: false,
    description: 'Filter rules by MITRE ATT&CK tactic names or IDs',
    type: [String],
    isArray: true,
  })
  @ApiQuery({
    name: 'mitre_techniques',
    required: false,
    description: 'Filter rules by MITRE ATT&CK technique IDs',
    type: [String],
    isArray: true,
  })
  @Get()
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,
    (req) => getWorkspaceId(),
  )
  @WithVisibleResources(FGAType.GROUP, FGARelation.GROUP_CONTENT_VIEWER)
  async findAll(
    @Req() req: any,
    @Query('q') q?: string,
    @Query('sort_by') sort_by?: RuleOrderBy,
    @Query('sort_order') sortOrder?: SortOrder,
    @Query() paginationParams?: ApiPaginationParams,
    @Query('mitre_attack_ids') mitre_attack_ids?: string[],
    @Query('mitre_tactics') mitre_tactics?: string[],
    @Query('mitre_techniques') mitre_techniques?: string[],
  ): Promise<PaginatedApiResponseDto<RuleWithUserAndGroup>> {
    const visibleGroupContent = getVisibleResources(
      req,
      FGAType.GROUP,
      FGARelation.GROUP_CONTENT_VIEWER,
    );

    // Set default limit if not provided
    const { size, page } = getPaginationSizeAndPage(paginationParams, 100);

    const { orderByField, orderByDirection } = getSortParams(
      sort_by,
      sortOrder,
    );

    const response: PagedSearchResponseDto =
      await this.searchService.pagedSearch(
        q,
        {
          owner_ids: visibleGroupContent,
          mitre_attack_ids,
          mitre_tactics,
          mitre_techniques,
        },
        {
          size,
          page,
          sort_by: orderByField,
          sort_order: orderByDirection,
        },
      );
    const result: { items: Rule[]; total: number } = {
      items: response.data.map(mapOpenSearchRuleToRule),
      total: response.meta.total,
    };

    // Enrich rules with user and group details and bookmark information
    const authorizationHeader = this.getAuthorizationHeader(req);
    const internalUser = await this.getInternalUserFromRequest(req);
    const enrichedRules = await this.enrichmentHelper.enrichRulesWithDetails(
      result.items,
      authorizationHeader,
      internalUser.id,
    );

    return createPaginatedApiResponse(enrichedRules, result.total, page, size);
  }

  @ApiOperation({ summary: 'Get a rule by ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Rule found and returned successfully.',
    type: () => ApiResponseDto.getType(RuleWithUserAndGroup),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Rule with the specified ID not found.',
    type: ErrorResponseDto,
  })
  @ApiParam({
    name: 'id',
    description: 'Rule ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @Get(':id')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.CONTENT_VIEWER,
    FGAType.CONTENT,
    (req) => req.params.id,
  )
  async findOne(
    @Param('id') id: string,
    @Req() req: any,
  ): Promise<ApiResponseDto<RuleWithUserAndGroup>> {
    const rule = await this.rulesService.findOne(id);
    const internalUser = await this.getInternalUserFromRequest(req);

    // Enrich rule with user and group details and bookmark information
    const authorizationHeader = this.getAuthorizationHeader(req);
    const [enrichedRule] = await this.enrichmentHelper.enrichRulesWithDetails(
      [rule],
      authorizationHeader,
      internalUser.id,
    );

    return createApiResponse(enrichedRule);
  }

  @ApiOperation({ summary: 'Update a rule' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Rule updated successfully.',
    type: () => ApiResponseDto.getType(RuleWithUserAndGroup),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Rule with the specified ID not found.',
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data.',
    type: ErrorResponseDto,
  })
  @ApiParam({
    name: 'id',
    description: 'Rule ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @Put(':id')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.CONTENT_EDITOR,
    FGAType.CONTENT,
    (req) => req.params.id,
  )
  async update(
    @Param('id') id: string,
    @Body() updateRuleDto: UpdateRuleDto,
    @Req() req: any,
  ): Promise<ApiResponseDto<RuleWithUserAndGroup>> {
    const authorizationHeader = this.getAuthorizationHeader(req);
    const internalUser = await this.getInternalUserFromRequest(req);

    // Get the rule to update
    const existingRule = await this.rulesService.findOne(id);

    // Determine the final status and group_id after the update
    const finalStatus =
      updateRuleDto.status !== undefined
        ? updateRuleDto.status
        : existingRule.status;
    let finalGroupId;
    if (finalStatus === RuleStatus.PUBLISHED) {
      finalGroupId =
        updateRuleDto.group_id !== undefined
          ? updateRuleDto.group_id
          : existingRule.group_id;
    } else {
      finalGroupId = null;
    }

    // Validate rule status and group_id relationship
    this.enrichmentHelper.validateRuleStatusAndGroup(finalStatus, finalGroupId);

    // Fetch user and group details before updating the rule
    const groupDetails = await this.enrichmentHelper.fetchGroupDetails(
      finalGroupId,
      authorizationHeader,
    );

    // Validate group existence
    if (updateRuleDto.group_id && !groupDetails) {
      throw new NotFoundException(
        `Group '${updateRuleDto.group_id}' not found`,
      );
    }

    const updatedRule = await this.rulesService.update(
      id,
      updateRuleDto,
      groupDetails?.name,
    );

    // add/remove tuples if the group_id is changed to null or vice versa
    if (updatedRule.group_id !== existingRule.group_id) {
      if (existingRule.group_id) {
        await this.deleteRuleGroupTuples(id, existingRule.group_id);
      }
      if (updatedRule.group_id) {
        await this.createNewRuleGroupTuples(id, updatedRule.group_id);
      }
    }

    // Enrich rule with user and group details
    const [enrichedRule] = await this.enrichmentHelper.enrichRulesWithDetails(
      [updatedRule],
      authorizationHeader,
      internalUser.id,
      true, // Ignore fetching my bookmarking after update
      true, // Ignore fetching my interactions after update
    );

    return createApiResponse(enrichedRule);
  }

  @ApiOperation({ summary: 'Update my rules contributor username' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Rule updated successfully.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Rule with the specified ID not found.',
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data.',
    type: ErrorResponseDto,
  })
  @ApiParam({
    name: 'id',
    description: 'Rule ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @Post('update-username')
  @UseGuards(Auth0Guard)
  @WithVisibleResources(FGAType.CONTENT, FGARelation.CONTENT_CREATOR)
  async updateMyRulesUsername(@Req() req: any): Promise<void> {
    const internalUser = await this.getInternalUserFromRequest(req);
    const visibleRuleIds = getVisibleResources(
      req,
      FGAType.CONTENT,
      FGARelation.CONTENT_CREATOR,
    );

    if (!internalUser.username) {
      throw new BadRequestException('Username is not set for this user');
    }

    await this.rulesService.updateMyRulesUsername(
      internalUser.id,
      internalUser.username,
      visibleRuleIds,
    );
  }

  @ApiOperation({ summary: 'Delete a rule' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Rule deleted successfully.',
    type: () => ApiResponseDto.getType(Rule),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Rule with the specified ID not found.',
    type: ErrorResponseDto,
  })
  @ApiParam({
    name: 'id',
    description: 'Rule ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @Delete(':id')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.CONTENT_EDITOR,
    FGAType.CONTENT,
    (req) => req.params.id,
  )
  async remove(
    @Param('id') id: string,
    @Req() req: any,
  ): Promise<ApiResponseDto<void>> {
    const rule = await this.rulesService.remove(id);

    // Delete all associated data in parallel
    await Promise.all([
      this.deleteRuleTuples(
        rule.id,
        this.getExternalUserIdFromRequest(req),
        rule.group_id,
      ),
      this.bookmarksService.deleteBookmarksForRule(id),
      this.ruleInteractionsService.deleteInteractionsForRule(id),
    ]);

    return createApiResponse(undefined);
  }

  @ApiOperation({ summary: 'Download multiple rules as a ZIP file' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Rules packaged successfully, download URL returned.',
    type: () => ApiResponseDto.getType(BulkDownloadResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input or no IDs provided.',
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'None of the requested rules were found.',
    type: ErrorResponseDto,
  })
  @Post('bulk-download')
  @RequireFgaPermissionOnAll(
    FGAType.USER,
    FGARelation.CONTENT_VIEWER,
    FGAType.CONTENT,
    (req) => req.body.ids,
  )
  async bulkDownload(
    @Req() req: any,
    @Body() bulkDownloadDto: BulkDownloadRulesDto,
  ): Promise<ApiResponseDto<BulkDownloadResponseDto>> {
    const result = await this.rulesService.bulkDownload(bulkDownloadDto.ids);
    const internalUser = await this.getInternalUserFromRequest(req);

    // Fire and forget all download interactions in parallel
    void Promise.all(
      bulkDownloadDto.ids.map((id) =>
        this.ruleInteractionsService
          .createInteraction(internalUser.id, id, RuleInteractionType.DOWNLOAD)
          .catch((error) => {
            // Log error but don't let it affect the response
            this.logger.error(
              `Failed to create download interaction for rule ${id}:`,
              error,
            );
          }),
      ),
    );

    return createApiResponse({
      downloadUrl: result.downloadUrl,
      expiresIn: result.expiresIn,
    });
  }

  @ApiOperation({ summary: 'Download a single rule by ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Rule download URL returned successfully.',
    type: () => ApiResponseDto.getType(SingleRuleDownloadResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Rule with the specified ID not found.',
    type: ErrorResponseDto,
  })
  @ApiParam({
    name: 'id',
    description: 'Rule ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @Get(':id/download')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.CONTENT_VIEWER,
    FGAType.CONTENT,
    (req) => req.params.id,
  )
  async downloadSingleRule(
    @Req() req: any,
    @Param('id') id: string,
  ): Promise<ApiResponseDto<SingleRuleDownloadResponseDto>> {
    const result = await this.rulesService.singleRuleDownload(id);
    const internalUser = await this.getInternalUserFromRequest(req);

    // Fire and forget the download interaction
    void this.ruleInteractionsService
      .createInteraction(internalUser.id, id, RuleInteractionType.DOWNLOAD)
      .catch((error) => {
        // Log error but don't let it affect the response
        this.logger.error(
          `Failed to create download interaction for rule ${id}:`,
          error,
        );
      });

    return createApiResponse({
      downloadUrl: result.downloadUrl,
      expiresIn: result.expiresIn,
    });
  }

  @ApiOperation({ summary: 'Increment the download count for a rule' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Rule download URL returned successfully.',
    type: () => ApiResponseDto.getType(SingleRuleDownloadResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Rule with the specified ID not found.',
    type: ErrorResponseDto,
  })
  @ApiParam({
    name: 'id',
    description: 'Rule ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @Post(':id/download-increment')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.CONTENT_VIEWER,
    FGAType.CONTENT,
    (req) => req.params.id,
  )
  async incrementDownloadCountForRule(
    @Req() req: any,
    @Param('id') id: string,
  ): Promise<ApiResponseDto<RuleInteractionResponse>> {
    const internalUser = await this.getInternalUserFromRequest(req);

    await this.ruleInteractionsService
      .createInteraction(internalUser.id, id, RuleInteractionType.DOWNLOAD)
      .catch((error) => {
        // Log error but don't let it affect the response
        this.logger.error(
          `Failed to create download interaction for rule ${id}:`,
          error,
        );
      });

    const rule = await this.rulesService.findOne(id);

    return createApiResponse({
      success: true,
      likes: rule.likes,
      dislikes: rule.dislikes,
      downloads: rule.downloads,
      message: 'Download count incremented successfully',
    });
  }

  @ApiOperation({ summary: 'Bookmark a rule' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Rule bookmarked successfully.',
    type: () => ApiResponseDto.getType(Object),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Rule with the specified ID not found.',
    type: ErrorResponseDto,
  })
  @ApiParam({
    name: 'id',
    description: 'Rule ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @Post(':id/bookmark')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.CONTENT_VIEWER,
    FGAType.CONTENT,
    (req) => req.params.id,
  )
  async bookmarkRule(
    @Param('id') id: string,
    @Req() req: any,
  ): Promise<ApiResponseDto<{ success: boolean; message: string }>> {
    const internalUser = await this.getInternalUserFromRequest(req);
    const result = await this.bookmarksService.bookmarkRule(
      internalUser.id,
      id,
    );

    return createApiResponse({
      success: true,
      message: result
        ? 'Rule bookmarked successfully'
        : 'Rule was already bookmarked',
    });
  }

  /**
   * Delete the bookmark for a rule
   * @param id Rule ID to unbookmark
   * @returns Success message
   */
  @ApiOperation({ summary: 'Unbookmark a rule' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Rule unbookmarked successfully.',
    type: () => ApiResponseDto.getType(Object),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Rule with the specified ID not found.',
    type: ErrorResponseDto,
  })
  @ApiParam({
    name: 'id',
    description: 'Rule ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @Delete(':id/bookmark')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.CONTENT_VIEWER,
    FGAType.CONTENT,
    (req) => req.params.id,
  )
  async unbookmarkRule(
    @Param('id') id: string,
    @Req() req: any,
  ): Promise<ApiResponseDto<{ success: boolean; message: string }>> {
    const internalUser = await this.getInternalUserFromRequest(req);
    const result = await this.bookmarksService.unbookmarkRule(
      internalUser.id,
      id,
    );

    return createApiResponse({
      success: true,
      message: result
        ? 'Rule unbookmarked successfully'
        : 'Rule was not bookmarked',
    });
  }

  @ApiOperation({ summary: 'Like a rule' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Rule liked successfully.',
    type: () => ApiResponseDto.getType(RuleInteractionResponse),
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Rule already liked by user.',
    type: ErrorResponseDto,
  })
  @Post(':id/like')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.CONTENT_VIEWER,
    FGAType.CONTENT,
    (req) => req.params.id,
  )
  async likeRule(
    @Param('id') id: string,
    @Req() req: any,
  ): Promise<ApiResponseDto<RuleInteractionResponse>> {
    const internalUser = await this.getInternalUserFromRequest(req);
    await this.ruleInteractionsService.createInteraction(
      internalUser.id,
      id,
      RuleInteractionType.LIKE,
    );

    // Get updated counts
    const rule = await this.rulesService.findOne(id);

    return createApiResponse({
      success: true,
      likes: rule.likes,
      dislikes: rule.dislikes,
      downloads: rule.downloads,
      message: 'Rule liked successfully',
    });
  }

  @ApiOperation({ summary: 'Dislike a rule' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Rule disliked successfully.',
    type: () => ApiResponseDto.getType(RuleInteractionResponse),
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Rule already disliked by user.',
    type: ErrorResponseDto,
  })
  @Post(':id/dislike')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.CONTENT_VIEWER,
    FGAType.CONTENT,
    (req) => req.params.id,
  )
  async dislikeRule(
    @Param('id') id: string,
    @Req() req: any,
  ): Promise<ApiResponseDto<RuleInteractionResponse>> {
    const internalUser = await this.getInternalUserFromRequest(req);
    await this.ruleInteractionsService.createInteraction(
      internalUser.id,
      id,
      RuleInteractionType.DISLIKE,
    );

    // Get updated counts
    const rule = await this.rulesService.findOne(id);

    return createApiResponse({
      success: true,
      likes: rule.likes,
      dislikes: rule.dislikes,
      message: 'Rule disliked successfully',
    });
  }

  @ApiOperation({ summary: 'Remove like from a rule' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Like removed successfully.',
    type: () => ApiResponseDto.getType(RuleInteractionResponse),
  })
  @Delete(':id/like')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.CONTENT_VIEWER,
    FGAType.CONTENT,
    (req) => req.params.id,
  )
  async unlikeRule(
    @Param('id') id: string,
    @Req() req: any,
  ): Promise<ApiResponseDto<RuleInteractionResponse>> {
    const internalUser = await this.getInternalUserFromRequest(req);
    await this.ruleInteractionsService.deleteInteraction(
      internalUser.id,
      id,
      RuleInteractionType.LIKE,
    );

    // Get updated counts
    const rule = await this.rulesService.findOne(id);

    return createApiResponse({
      success: true,
      likes: rule.likes,
      dislikes: rule.dislikes,
      message: 'Like removed successfully',
    });
  }

  @ApiOperation({ summary: 'Remove dislike from a rule' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Dislike removed successfully.',
    type: () => ApiResponseDto.getType(RuleInteractionResponse),
  })
  @Delete(':id/dislike')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.CONTENT_VIEWER,
    FGAType.CONTENT,
    (req) => req.params.id,
  )
  async undislikeRule(
    @Param('id') id: string,
    @Req() req: any,
  ): Promise<ApiResponseDto<RuleInteractionResponse>> {
    const internalUser = await this.getInternalUserFromRequest(req);
    await this.ruleInteractionsService.deleteInteraction(
      internalUser.id,
      id,
      RuleInteractionType.DISLIKE,
    );

    // Get updated counts
    const rule = await this.rulesService.findOne(id);

    return createApiResponse({
      success: true,
      likes: rule.likes,
      dislikes: rule.dislikes,
      message: 'Dislike removed successfully',
    });
  }

  @ApiOperation({ summary: 'Get rule interactions' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Rule interactions returned successfully.',
    type: () => PaginatedApiResponseDto.getType(RuleInteractionWithUserDetails),
  })
  @Get(':id/interactions')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.CONTENT_VIEWER,
    FGAType.CONTENT,
    (req) => req.params.id,
  )
  async getRuleInteractions(
    @Req() req: any,
    @Param('id') id: string,
    @Query('type') type?: RuleInteractionType,
    @Query() paginationParams?: ApiPaginationParams,
  ): Promise<PaginatedApiResponseDto<RuleInteraction>> {
    const { size, page } = getPaginationSizeAndPage(paginationParams, 100);

    const { items, total } =
      await this.ruleInteractionsService.getRuleInteractions(
        id,
        type,
        page,
        size,
      );

    // Enrich the rule interactions with user details
    const users = await this.userService.getBulkUserDetails(
      items.map((interaction) => interaction.user_id),
      this.getAuthorizationHeader(req),
    );

    // Enrich the rule interactions with user details
    const enrichedInteractions = items.map((interaction) => {
      const user = users.find((user) => user.id === interaction.user_id);
      return {
        ...interaction,
        user: user ?? null,
      };
    });

    // Remove null users
    const filteredInteractions: RuleInteractionWithUserDetails[] =
      enrichedInteractions.filter(
        (interaction) => interaction.user !== null,
      ) as RuleInteractionWithUserDetails[];

    return createPaginatedApiResponse(filteredInteractions, total, page, size);
  }

  @ApiOperation({ summary: "Get current user's rule interactions" })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User interactions returned successfully.',
    type: () => PaginatedApiResponseDto.getType(RuleInteractionWithRuleDetails),
  })
  @Get('interactions/me')
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.MEMBER,
    FGAType.WORKSPACE,
    (req) => getWorkspaceId(),
  )
  async getUserInteractions(
    @Req() req: any,
    @Query() paginationParams?: ApiPaginationParams,
  ): Promise<PaginatedApiResponseDto<RuleInteractionWithRuleDetails>> {
    const internalUser = await this.getInternalUserFromRequest(req);
    const { size, page } = getPaginationSizeAndPage(paginationParams, 100);
    const result = await this.ruleInteractionsService.getUserInteractions(
      internalUser.id,
      page,
      size,
    );

    // Enrich the rule interactions with rule details
    const enrichedInteractions: RuleInteractionWithRuleDetails[] =
      await Promise.all(
        result.items.map(async (interaction) => {
          const rule = await this.rulesService.findOne(interaction.rule_id);
          return {
            ...interaction,
            rule,
          };
        }),
      );

    return createPaginatedApiResponse(
      enrichedInteractions,
      result.total,
      page,
      size,
    );
  }

  /**
   * Reset all rules in a group to draft status and remove group association
   * @param groupId Group ID
   * @returns Number of rules reset to draft status
   */
  @Post('/groups/:id/reset-rules-to-draft')
  @ApiOperation({
    summary: 'Reset all rules in a group to draft status',
    description: 'Changes status of all rules in a group to draft',
  })
  @ApiResponse({
    status: 200,
    description: 'Rules successfully reset to draft status',
    type: ApiResponseDto,
  })
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.GROUP_CONTENT_EDITOR,
    FGAType.GROUP,
    (req) => req.params.id,
  )
  async resetGroupRulesToDraft(
    @Param('id') groupId: string,
  ): Promise<ApiResponseDto<ResetRulesToDraftResponseDto>> {
    const count = await this.rulesService.resetGroupRulesToDraft(groupId);
    // TODO: Do we need to delete the bookmarks?
    return createApiResponse({
      count,
      message: `Successfully reset ${count} rules to draft status`,
    });
  }

  @Post('/users/:id/reset-rules-to-draft')
  @ApiOperation({
    summary:
      'Reset all published rules created by a user but owned by groups to draft status',
    description:
      'Changes all rules that were created by the user but published to groups back to private drafts owned by the user',
  })
  @ApiResponse({
    status: 200,
    description: 'Rules successfully reset to draft status',
    type: ApiResponseDto,
  })
  @RequireFgaPermission(
    FGAType.USER,
    FGARelation.USER_PROFILE_EDITOR,
    FGAType.USER_PROFILE,
    (req) => req.params.id,
  )
  async resetUserPublishedRulesToDraft(
    @Param('id') userId: string,
  ): Promise<ApiResponseDto<ResetRulesToDraftResponseDto>> {
    const count =
      await this.rulesService.resetUserPublishedRulesToDraft(userId);
    // TODO: Do we need to delete the bookmarks?
    return createApiResponse({
      count,
      message: `Successfully reset ${count} rules to draft status`,
    });
  }

  private getAuthorizationHeader(request: any): string {
    const authorizationHeader = request?.headers['authorization'];
    if (!authorizationHeader) {
      throw new UnauthorizedException('No authorization header provided');
    }
    return authorizationHeader;
  }

  private getExternalUserIdFromRequest(req: any): string {
    const externalUserId = req?.user?.sub;
    if (!externalUserId) {
      throw new UnauthorizedException('No user object provided');
    }
    return externalUserId;
  }

  private async getInternalUserFromRequest(
    req: any,
  ): Promise<DetailedUserResponse> {
    const authorizationHeader = this.getAuthorizationHeader(req);
    const internalUser =
      await this.userService.getInternalUser(authorizationHeader);
    return internalUser;
  }

  private async createNewRuleTuples(
    ruleId: string,
    extUserId: string,
    groupId?: string | null,
  ): Promise<void> {
    await this.fgaService.writeTuples(
      this.getRuleTuples(ruleId, extUserId, groupId),
    );
  }

  private async createNewRuleGroupTuples(
    ruleId: string,
    groupId: string,
  ): Promise<void> {
    await this.fgaService.writeTuples(this.getRuleGroupTuples(ruleId, groupId));
  }

  private async deleteRuleTuples(
    ruleId: string,
    extUserId: string,
    groupId?: string | null,
  ): Promise<void> {
    await this.fgaService.deleteTuples(
      this.getRuleTuples(ruleId, extUserId, groupId),
    );
  }

  private async deleteRuleGroupTuples(
    ruleId: string,
    groupId: string,
  ): Promise<void> {
    await this.fgaService.deleteTuples(
      this.getRuleGroupTuples(ruleId, groupId),
    );
  }

  private getRuleTuples(
    ruleId: string,
    extUserId: string,
    groupId?: string | null,
  ): FGATuple[] {
    const tuples: FGATuple[] = [
      // Add the user as content creator of the rule
      {
        user_type: FGAType.USER,
        user_id: extUserId,
        relation: FGARelation.CONTENT_CREATOR,
        object_type: FGAType.CONTENT,
        object_id: ruleId,
      },
      {
        user_type: FGAType.WORKSPACE,
        user_id: getWorkspaceId(),
        relation: FGARelation.WORKSPACE,
        object_type: FGAType.CONTENT,
        object_id: ruleId,
      },
    ];
    if (groupId) {
      // If the content is published to a group, add the group as a published group of the rule
      tuples.push(...this.getRuleGroupTuples(ruleId, groupId));
    }
    return tuples;
  }

  private getRuleGroupTuples(ruleId: string, groupId: string): FGATuple[] {
    return [
      {
        user_type: FGAType.GROUP,
        user_id: groupId,
        relation: FGARelation.PUBLISHED_GROUP,
        object_type: FGAType.CONTENT,
        object_id: ruleId,
      },
    ];
  }
}
