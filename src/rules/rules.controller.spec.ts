import { Test, TestingModule } from '@nestjs/testing';
import {
  NotFoundException,
  BadRequestException,
  UnauthorizedException,
  ForbiddenException,
  InternalServerErrorException,
} from '@nestjs/common';
import { RulesController } from './rules.controller';
import { RulesService } from './rules.service';
import {
  Rule,
  RuleType,
  CreateRuleDto,
  UpdateRuleDto,
  BulkDownloadRulesDto,
  RuleStatus,
  RuleWithUserAndGroup,
  RuleOrderBy,
} from './models/rule.model';
import { FgaService } from '../auth/fga/fga.service';
import { UserGroupsService } from '../auth/services/user-groups.service';
import {
  DetailedUserResponse,
  UserResponse,
} from '../auth/interfaces/user-response.interface';
import { GroupResponse } from '../auth/interfaces/group-response.interface';
import { FGARelation } from '../auth/fga/fga.enums';
import { FGAType } from '../auth/fga/fga.enums';
import { ConfigService } from '@nestjs/config';
import { GroupType } from '../auth/interfaces/group-response.interface';
import { BookmarksService } from '../bookmarks/bookmarks.service';
import { EnrichmentHelper } from '../common/enrichment-helper';
import { RuleInteractionsService } from '../rule-interactions/rule-interactions.service';
import { RuleInteractionType } from '../rule-interactions/models/rule-interaction.model';
import { SearchService } from '../search/search.service';
import { SortOrder } from '../search/models/sort-order.enum';
import { SortField } from '../search/models/sort-field.enum';
import { PaginatedApiResponseDto } from '../common/dto/pagination.dto';
import { OpenSearchRuleResult } from '../common/helpers/rule-mappers';
import { ParsingService } from '../parsing/parsing.service';
import { ParseRuleResult } from '../parsing/parsing.types';
import { OpenSearchService } from '../opensearch/opensearch.service';

describe('RulesController', () => {
  let controller: RulesController;
  let service: jest.Mocked<RulesService>;
  let mockFgaService: jest.Mocked<FgaService>;
  let mockUserGroupsService: jest.Mocked<UserGroupsService>;
  let mockConfigService: jest.Mocked<ConfigService>;
  let mockBookmarksService: jest.Mocked<BookmarksService>;
  let mockEnrichmentHelper: jest.Mocked<EnrichmentHelper>;
  let mockRuleInteractionsService: jest.Mocked<RuleInteractionsService>;
  let mockSearchService: jest.Mocked<SearchService>;
  let mockParsingService: jest.Mocked<ParsingService>;
  let mockOpenSearchService: jest.Mocked<OpenSearchService>;

  const mockUser: UserResponse = {
    id: 'user-id-1',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    username: 'test-user',
    display_picture_url: 'https://test-url.com/display-picture.jpg',
  };

  const mockGroup: GroupResponse = {
    id: 'group-id-1',
    name: 'Test Group',
    description: 'A test group for unit tests',
    logo_url: 'https://test-url.com/logo.jpg',
    type: GroupType.PUBLIC,
  };

  const mockRule: Rule = {
    id: 'rule-id-1',
    title: 'Test Rule 1',
    description: 'Test Description 1',
    rule_type: RuleType.SIGMA,
    content: 'Test Content 1',
    created_by: 'user-id-1',
    group_id: 'group-id-1',
    group_name: 'Test Group',
    status: RuleStatus.PUBLISHED,
    tags: [],
    created_at: '2021-01-01',
    updated_at: '2021-01-01',
    metadata: {},
    test_cases: [],
    version: 1,
    contributor: 'test-contributor-1',
    likes: 0,
    downloads: 0,
    dislikes: 0,
    bookmarks: 0,
    ai_generated: {
      description: false,
      title: false,
      content: false,
      tags: false,
    },
  };

  const mockRuleWithDetails: RuleWithUserAndGroup = {
    ...mockRule,
    created_by_user: mockUser,
    group: mockGroup,
    is_bookmarked: false,
  };

  const mockRules: Rule[] = [
    mockRule,
    {
      ...mockRule,
      id: 'rule-id-2',
      title: 'Another Test Rule',
      rule_type: RuleType.KQL,
      tags: ['test', 'performance'],
      ai_generated: {
        description: false,
        title: false,
        content: false,
        tags: false,
      },
    },
  ];

  const mockOpenSearchRules: OpenSearchRuleResult[] = [
    {
      id: 'rule-id-1',
      title: {
        value: 'Test Rule 1',
        suggest: {
          input: 'Test Rule 1',
          contexts: {
            rule_type: ['SIGMA'],
            owner_id: ['test-group'],
          },
        },
      },
      description: 'Test Description 1',
      content: 'Test Content 1',
      created_by: 'test-user',
      owner_id: 'test-group',
      status: RuleStatus.PUBLISHED,
      created_at: '2021-01-01',
      updated_at: '2021-01-01',
      metadata: {},
      version: 1,
      contributor: 'test-contributor-1',
      likes: 0,
      downloads: 0,
      dislikes: 0,
      rule_type: RuleType.SIGMA,
      ai_generated: {
        description: false,
        title: false,
        content: false,
        tags: false,
      },
    },
    {
      id: 'rule-id-2',
      title: {
        value: 'Test Rule 2',
        suggest: {
          input: 'Test Rule 2',
          contexts: {
            rule_type: ['SIGMA'],
            owner_id: ['test-group'],
          },
        },
      },
      description: 'Test Description 2',
      content: 'Test Content 2',
      created_by: 'test-user',
      owner_id: 'test-group',
      status: RuleStatus.DRAFT,
      rule_type: RuleType.SIGMA,
      created_at: '2021-01-01',
      updated_at: '2021-01-01',
      metadata: {},
      version: 1,
      contributor: 'test-contributor-2',
      likes: 0,
      downloads: 0,
      dislikes: 0,
      ai_generated: {
        description: false,
        title: false,
        content: false,
        tags: false,
      },
    },
  ];

  const mockUnenrichedRules: Rule[] = [
    {
      id: 'rule-id-1',
      title: 'Test Rule 1',
      description: 'Test Description 1',
      rule_type: RuleType.SIGMA,
      content: 'Test Content 1',
      created_by: 'test-user',
      group_id: 'test-group',
      group_name: undefined,
      status: RuleStatus.PUBLISHED,
      tags: [],
      created_at: '2021-01-01',
      updated_at: '2021-01-01',
      metadata: {},
      test_cases: [],
      version: 1,
      contributor: 'test-contributor-1',
      likes: 0,
      downloads: 0,
      dislikes: 0,
      is_bookmarked: undefined,
      is_disliked: undefined,
      is_liked: undefined,
      published_at: '2021-01-01',
      ai_generated: {
        description: false,
        title: false,
        content: false,
        tags: false,
      },
    },
    {
      id: 'rule-id-2',
      title: 'Test Rule 2',
      description: 'Test Description 2',
      rule_type: RuleType.SIGMA,
      content: 'Test Content 2',
      created_by: 'test-user',
      status: RuleStatus.DRAFT,
      group_id: 'test-group',
      group_name: undefined,
      tags: [],
      created_at: '2021-01-01',
      updated_at: '2021-01-01',
      metadata: {},
      test_cases: [],
      version: 1,
      contributor: 'test-contributor-2',
      likes: 0,
      downloads: 0,
      dislikes: 0,
      is_bookmarked: undefined,
      is_disliked: undefined,
      is_liked: undefined,
      published_at: '2021-01-01',
      ai_generated: {
        description: false,
        title: false,
        content: false,
        tags: false,
      },
    },
  ];

  const mockRequest = {
    user: {
      sub: 'external-user-id-1',
    },
    headers: {
      authorization: 'test',
    },
  };

  beforeEach(async () => {
    // Create mock implementation for RulesService
    const mockRulesService = {
      create: jest.fn(),
      findOne: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
      bulkDownload: jest.fn(),
      findByIds: jest.fn(),
      findByUserId: jest.fn(),
      singleRuleDownload: jest.fn(),
      getRuleCounts: jest.fn(),
      findMyRules: jest.fn(),
      resetGroupRulesToDraft: jest.fn(),
      resetUserPublishedRulesToDraft: jest.fn(),
      updateMyRulesUsername: jest.fn(),
      getCountsByUser: jest.fn(),
      getCountsByGroup: jest.fn(),
    };

    const mockFga = {
      checkUserAuthorization: jest.fn().mockResolvedValue(true),
      writeTuples: jest.fn().mockResolvedValue(undefined),
      getAccessibleResourceIds: jest.fn().mockResolvedValue(['group-id-1']),
      deleteTuples: jest.fn().mockResolvedValue(undefined),
    };

    const mockUserGroups = {
      getInternalUser: jest.fn().mockResolvedValue(mockUser),
      getBulkUserDetails: jest.fn().mockResolvedValue([mockUser]),
      getBulkGroupDetails: jest.fn().mockResolvedValue([mockGroup]),
    };

    const mockConfig = {
      get: jest.fn().mockReturnValue('detections-ai-dev'),
    };

    const mockBookmarks = {
      bookmarkRule: jest.fn(),
      unbookmarkRule: jest.fn(),
      addBookmarkInfoToRules: jest.fn(),
      getUserBookmarkedRuleIds: jest.fn(),
      deleteBookmarksForRule: jest.fn().mockResolvedValue(5),
      getBookmarkMapForRules: jest.fn().mockResolvedValue(
        new Map([
          ['rule-id-1', true],
          ['rule-id-2', false],
        ]),
      ),
      getAllUserBookmarkedRuleIds: jest
        .fn()
        .mockResolvedValue(['rule-id-1', 'rule-id-2']),
    };

    const mockRuleInteractions = {
      createInteraction: jest.fn().mockImplementation(() => {
        return Promise.resolve(); // Return a resolved promise
      }),
      getUserInteractions: jest.fn(),
      getRuleInteractions: jest.fn(),
      deleteInteraction: jest.fn(),
      deleteInteractionsForRule: jest.fn().mockResolvedValue(3),
    };

    const mockEnrichment = {
      enrichRulesWithDetails: jest
        .fn()
        .mockImplementation((rules, auth, userId) => {
          return Promise.resolve(
            rules.map((rule) => ({
              ...rule,
              created_by_user: mockUser,
              group: rule.group_id ? mockGroup : undefined,
              is_bookmarked: rule.id === 'rule-id-1',
            })),
          );
        }),
      fetchGroupDetails: jest.fn().mockResolvedValue(mockGroup),
      validateUserAndGroupExist: jest.fn(),
      enrichSingleRuleWithDetails: jest
        .fn()
        .mockImplementation((rule, user, group) => ({
          ...rule,
          created_by_user: user,
          group: rule.group_id ? group : undefined,
        })),
      validateRuleStatusAndGroup: jest.fn(),
    };

    const mockParsing = {
      parseRule: jest.fn().mockResolvedValue(mockRule),
    };

    const mockSearch = {
      pagedSearch: jest.fn().mockResolvedValue({
        data: mockOpenSearchRules,
        meta: {
          total: mockOpenSearchRules.length,
          current_page: 1,
          page_size: 100,
          total_pages: 1,
          size: mockOpenSearchRules.length,
          has_next_page: false,
          has_prev_page: false,
        },
        status: 'success',
      }),
    };

    const mockOpenSearch = {
      connect: jest.fn(),
      disconnect: jest.fn(),
      getClient: jest.fn(),
      indexExists: jest.fn(),
      createIndex: jest.fn(),
      updateMappings: jest.fn(),
      search: jest.fn(),
      getAliasTarget: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [RulesController],
      providers: [
        {
          provide: RulesService,
          useValue: mockRulesService,
        },
        {
          provide: FgaService,
          useValue: mockFga,
        },
        {
          provide: UserGroupsService,
          useValue: mockUserGroups,
        },
        {
          provide: ConfigService,
          useValue: mockConfig,
        },
        {
          provide: BookmarksService,
          useValue: mockBookmarks,
        },
        {
          provide: EnrichmentHelper,
          useValue: mockEnrichment,
        },
        {
          provide: RuleInteractionsService,
          useValue: mockRuleInteractions,
        },
        {
          provide: SearchService,
          useValue: mockSearch,
        },
        {
          provide: ParsingService,
          useValue: mockParsing,
        },
        {
          provide: OpenSearchService,
          useValue: mockOpenSearch,
        },
      ],
    }).compile();

    controller = module.get<RulesController>(RulesController);
    service = module.get(RulesService);
    mockFgaService = module.get(FgaService);
    mockUserGroupsService = module.get(UserGroupsService);
    mockConfigService = module.get(ConfigService);
    mockBookmarksService = module.get(BookmarksService);
    mockEnrichmentHelper = module.get(EnrichmentHelper);
    mockRuleInteractionsService = module.get(RuleInteractionsService);
    mockSearchService = module.get(SearchService);
    mockParsingService = module.get(ParsingService);
    mockOpenSearchService = module.get(OpenSearchService);

    // Manually invoke onModuleInit to initialize ConfigService
    controller.onModuleInit();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new rule', async () => {
      // Arrange
      const createRuleDto: CreateRuleDto = {
        title: 'New Rule',
        description: 'A new rule for testing',
        rule_type: RuleType.SIGMA,
        content: 'rule content',
        tags: ['test'],
        metadata: { severity: 'medium' },
        test_cases: [],
        status: RuleStatus.DRAFT,
      };

      mockEnrichmentHelper.fetchGroupDetails.mockResolvedValueOnce(null);
      mockParsingService.parseRule.mockResolvedValue({
        success: true,
        metadata: {},
      } as ParseRuleResult);

      service.create.mockResolvedValue(mockRule);

      // Act
      const result = await controller.create(createRuleDto, mockRequest);

      // Assert
      expect(mockUserGroupsService.getInternalUser).toHaveBeenCalledWith(
        'test',
      );
      expect(service.create).toHaveBeenCalledWith(
        createRuleDto,
        'user-id-1',
        'test-user',
        undefined,
      );
      expect(mockFgaService.writeTuples).toHaveBeenCalled();
      expect(mockEnrichmentHelper.fetchGroupDetails).toHaveBeenCalledWith(
        undefined,
        'test',
      );
      expect(
        mockEnrichmentHelper.enrichSingleRuleWithDetails,
      ).toHaveBeenCalledWith(mockRule, mockUser, null);
      expect(result.data).toBeDefined();
    });

    it('should throw BadRequestException when user is not found', async () => {
      // Arrange
      const createRuleDto: CreateRuleDto = {
        title: 'New Rule',
        description: 'A new rule description',
        rule_type: RuleType.SIGMA,
        content: 'rule content',
      };

      mockUserGroupsService.getInternalUser.mockRejectedValue(
        new UnauthorizedException(),
      );

      // Act & Assert
      await expect(
        controller.create(createRuleDto, mockRequest),
      ).rejects.toThrow(
        new UnauthorizedException({
          message: 'Unauthorized',
          details: ['User not found'],
        }),
      );
      expect(service.create).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException when group is not found', async () => {
      // Arrange
      const createRuleDto: CreateRuleDto = {
        title: 'New Rule',
        description: 'A new rule description',
        rule_type: RuleType.SIGMA,
        content: 'rule content',
        group_id: 'non-existent-group-id',
        status: RuleStatus.PUBLISHED,
      };

      mockUserGroupsService.getInternalUser.mockResolvedValue(
        mockUser as DetailedUserResponse,
      );
      mockEnrichmentHelper.fetchGroupDetails.mockResolvedValueOnce(null);
      mockEnrichmentHelper.validateUserAndGroupExist.mockImplementation(() => {
        throw new BadRequestException('Invalid references in rule data');
      });

      // Act & Assert
      await expect(
        controller.create(createRuleDto, mockRequest),
      ).rejects.toThrow(
        new NotFoundException({
          message: `Group '${createRuleDto.group_id}' not found`,
        }),
      );
      expect(service.create).not.toHaveBeenCalled();
    });
  });

  describe('findAll', () => {
    it('should return all rules with default sorting when no parameters are provided', async () => {
      // Arrange
      mockSearchService.pagedSearch.mockResolvedValue({
        data: mockOpenSearchRules,
        meta: {
          total: 2,
          current_page: 1,
          page_size: 100,
          total_pages: 1,
          size: 2,
          has_next_page: false,
          has_prev_page: false,
        },
        status: 'success',
      });

      // Act
      const result: PaginatedApiResponseDto<RuleWithUserAndGroup> =
        await controller.findAll(mockRequest);

      // Assert
      expect(mockSearchService.pagedSearch).toHaveBeenCalledWith(
        undefined,
        {
          owner_ids: ['group-id-1'],
        },
        {
          size: 100,
          page: 1,
          sort_by: SortField.PUBLISHED_AT, // Default sort field
          sort_order: SortOrder.DESC, // Default sort order
        },
      );
      expect(mockEnrichmentHelper.enrichRulesWithDetails).toHaveBeenCalledWith(
        mockUnenrichedRules,
        'test',
        mockUser.id,
      );
      expect(result.data.length).toBe(2);
      expect(result.meta.total).toBe(2);
      expect(result.meta.current_page).toBe(1);
      expect(result.meta.page_size).toBe(100);
    });

    it('should filter and sort by LIKES when specified', async () => {
      // Arrange
      mockSearchService.pagedSearch.mockResolvedValue({
        data: [mockOpenSearchRules[0]],
        meta: {
          total: 1,
          current_page: 1,
          page_size: 100,
          has_next_page: false,
          has_prev_page: false,
          size: 1,
          total_pages: 1,
        },
        status: 'success',
      });

      // Act
      const result = await controller.findAll(
        mockRequest,
        undefined,
        RuleOrderBy.LIKES,
        SortOrder.DESC,
      );

      // Assert
      expect(mockSearchService.pagedSearch).toHaveBeenCalledWith(
        undefined,
        {
          owner_ids: ['group-id-1'],
        },
        {
          size: 100,
          page: 1,
          sort_by: SortField.LIKES,
          sort_order: SortOrder.DESC,
        },
      );
      expect(result.data.length).toBe(1);
    });

    it('should filter and sort by DOWNLOADS when specified', async () => {
      // Arrange
      mockSearchService.pagedSearch.mockResolvedValue({
        data: [mockOpenSearchRules[0]],
        meta: {
          total: 1,
          current_page: 1,
          page_size: 100,
          has_next_page: false,
          has_prev_page: false,
          size: 1,
          total_pages: 1,
        },
        status: 'success',
      });

      // Act
      const result = await controller.findAll(
        mockRequest,
        undefined,
        RuleOrderBy.DOWNLOADS,
        SortOrder.DESC,
      );

      // Assert
      expect(mockSearchService.pagedSearch).toHaveBeenCalledWith(
        undefined,
        {
          owner_ids: ['group-id-1'],
        },
        {
          size: 100,
          page: 1,
          sort_by: SortField.DOWNLOADS,
          sort_order: SortOrder.DESC,
        },
      );
      expect(mockEnrichmentHelper.enrichRulesWithDetails).toHaveBeenCalledWith(
        [mockUnenrichedRules[0]],
        'test',
        mockUser.id,
      );
      expect(result.data.length).toBe(1);
    });

    it('should handle custom pagination parameters', async () => {
      // Arrange
      mockSearchService.pagedSearch.mockResolvedValue({
        data: mockOpenSearchRules,
        meta: {
          total: 4,
          current_page: 2,
          page_size: 2,
          size: 2,
          total_pages: 2,
          has_next_page: false,
          has_prev_page: true,
        },
        status: 'success',
      });

      // Act
      const result = await controller.findAll(
        mockRequest,
        undefined,
        undefined,
        undefined,
        { page: 2, size: 2 },
      );

      // Assert
      expect(mockSearchService.pagedSearch).toHaveBeenCalledWith(
        undefined,
        {
          owner_ids: ['group-id-1'],
        },
        {
          size: 2,
          page: 2,
          sort_by: SortField.PUBLISHED_AT,
          sort_order: SortOrder.DESC,
        },
      );
      expect(result.meta).toEqual({
        total: 4,
        current_page: 2,
        page_size: 2,
        total_pages: 2,
        size: 2,
        has_next_page: false,
        has_prev_page: true,
      });
    });

    it('should search rules by query text', async () => {
      // Arrange
      mockSearchService.pagedSearch.mockResolvedValue({
        data: [mockOpenSearchRules[0]],
        meta: {
          total: 1,
          current_page: 1,
          page_size: 100,
          has_next_page: false,
          has_prev_page: false,
          size: 1,
          total_pages: 1,
        },
        status: 'success',
      });

      const searchQuery = 'process execution';

      // Act
      const result = await controller.findAll(
        mockRequest,
        searchQuery,
        RuleOrderBy.PUBLISHED_AT,
        SortOrder.DESC,
      );

      // Assert
      expect(mockSearchService.pagedSearch).toHaveBeenCalledWith(
        searchQuery,
        {
          owner_ids: ['group-id-1'],
        },
        {
          size: 100,
          page: 1,
          sort_by: SortField.PUBLISHED_AT,
          sort_order: SortOrder.DESC,
        },
      );
      expect(mockEnrichmentHelper.enrichRulesWithDetails).toHaveBeenCalled();
      expect(result.data.length).toBe(1);
    });

    it('should sort by DISLIKES when specified', async () => {
      // Arrange
      mockSearchService.pagedSearch.mockResolvedValue({
        data: mockOpenSearchRules,
        meta: {
          total: 2,
          current_page: 1,
          page_size: 100,
          has_next_page: false,
          has_prev_page: false,
          size: 2,
          total_pages: 1,
        },
        status: 'success',
      });

      // Act
      const result = await controller.findAll(
        mockRequest,
        undefined,
        RuleOrderBy.DISLIKES,
        SortOrder.DESC,
      );

      // Assert
      expect(mockSearchService.pagedSearch).toHaveBeenCalledWith(
        undefined,
        {
          owner_ids: ['group-id-1'],
        },
        {
          size: 100,
          page: 1,
          sort_by: SortField.DISLIKES,
          sort_order: SortOrder.DESC,
        },
      );
      expect(result.data.length).toBe(2);
    });

    it('should sort by CREATED_AT when specified', async () => {
      // Arrange
      mockSearchService.pagedSearch.mockResolvedValue({
        data: mockOpenSearchRules,
        meta: {
          total: 2,
          current_page: 1,
          page_size: 100,
          has_next_page: false,
          has_prev_page: false,
          size: 2,
          total_pages: 1,
        },
        status: 'success',
      });

      // Act
      const result = await controller.findAll(
        mockRequest,
        undefined,
        RuleOrderBy.CREATED_AT,
        SortOrder.ASC, // Test ascending order
      );

      // Assert
      expect(mockSearchService.pagedSearch).toHaveBeenCalledWith(
        undefined,
        {
          owner_ids: ['group-id-1'],
        },
        {
          size: 100,
          page: 1,
          sort_by: SortField.CREATED_AT,
          sort_order: SortOrder.ASC,
        },
      );
      expect(result.data.length).toBe(2);
    });

    it('should sort by CONTRIBUTOR when specified', async () => {
      // Arrange
      mockSearchService.pagedSearch.mockResolvedValue({
        data: mockOpenSearchRules,
        meta: {
          total: 2,
          current_page: 1,
          page_size: 100,
          has_next_page: false,
          has_prev_page: false,
          size: 2,
          total_pages: 1,
        },
        status: 'success',
      });

      // Act
      const result = await controller.findAll(
        mockRequest,
        undefined,
        RuleOrderBy.CONTRIBUTOR,
        SortOrder.ASC, // Test ascending order for contributor
      );

      // Assert
      expect(mockSearchService.pagedSearch).toHaveBeenCalledWith(
        undefined,
        {
          owner_ids: ['group-id-1'],
        },
        {
          size: 100,
          page: 1,
          sort_by: SortField.CONTRIBUTOR,
          sort_order: SortOrder.ASC,
        },
      );
      expect(result.data.length).toBe(2);
    });

    it('should sort by GROUP_NAME when specified', async () => {
      // Arrange
      mockSearchService.pagedSearch.mockResolvedValue({
        data: mockOpenSearchRules,
        meta: {
          total: 2,
          current_page: 1,
          page_size: 100,
          has_next_page: false,
          has_prev_page: false,
          size: 2,
          total_pages: 1,
        },
        status: 'success',
      });

      // Act
      const result = await controller.findAll(
        mockRequest,
        undefined,
        RuleOrderBy.GROUP_NAME,
        SortOrder.ASC, // Test ascending order for group name
      );

      // Assert
      expect(mockSearchService.pagedSearch).toHaveBeenCalledWith(
        undefined,
        {
          owner_ids: ['group-id-1'],
        },
        {
          size: 100,
          page: 1,
          sort_by: SortField.GROUP_NAME,
          sort_order: SortOrder.ASC,
        },
      );
      expect(result.data.length).toBe(2);
    });

    it('should sort by RULE_TYPE when specified', async () => {
      // Arrange
      mockSearchService.pagedSearch.mockResolvedValue({
        data: mockOpenSearchRules,
        meta: {
          total: 2,
          current_page: 1,
          page_size: 100,
          has_next_page: false,
          has_prev_page: false,
          size: 2,
          total_pages: 1,
        },
        status: 'success',
      });

      // Act
      const result = await controller.findAll(
        mockRequest,
        undefined,
        RuleOrderBy.RULE_TYPE,
        SortOrder.DESC, // Test descending order for rule type
      );

      // Assert
      expect(mockSearchService.pagedSearch).toHaveBeenCalledWith(
        undefined,
        {
          owner_ids: ['group-id-1'],
        },
        {
          size: 100,
          page: 1,
          sort_by: SortField.RULE_TYPE,
          sort_order: SortOrder.DESC,
        },
      );
      expect(result.data.length).toBe(2);
    });

    it('should use default sort values when sort_by is not specified', async () => {
      // Arrange
      mockSearchService.pagedSearch.mockResolvedValue({
        data: mockOpenSearchRules,
        meta: {
          total: 2,
          current_page: 1,
          page_size: 100,
          has_next_page: false,
          has_prev_page: false,
          size: 2,
          total_pages: 1,
        },
        status: 'success',
      });

      // Act
      const result = await controller.findAll(
        mockRequest,
        undefined, // No query
        undefined, // No sort_by
        SortOrder.ASC, // Only specify sort order
      );

      // Assert
      expect(mockSearchService.pagedSearch).toHaveBeenCalledWith(
        undefined,
        {
          owner_ids: ['group-id-1'],
        },
        {
          size: 100,
          page: 1,
          sort_by: SortField.PUBLISHED_AT, // Default field
          sort_order: SortOrder.ASC, // Provided order
        },
      );
      expect(result.data.length).toBe(2);
    });
  });

  describe('findOne', () => {
    it('should return a rule when it exists', async () => {
      // Arrange
      service.findOne.mockResolvedValue(mockRule);

      // Act
      const result = await controller.findOne('rule-id-1', mockRequest);

      // Assert
      expect(service.findOne).toHaveBeenCalledWith('rule-id-1');
      expect(mockEnrichmentHelper.enrichRulesWithDetails).toHaveBeenCalledWith(
        [mockRule],
        'test',
        mockUser.id,
      );
      expect(result.data).toBeDefined();
    });

    it('should propagate NotFoundException when rule is not found', async () => {
      // Arrange
      service.findOne.mockRejectedValue(
        new NotFoundException('Rule not found'),
      );

      // Act & Assert
      await expect(
        controller.findOne('non-existent-id', mockRequest),
      ).rejects.toThrow(NotFoundException);
      expect(service.findOne).toHaveBeenCalledWith('non-existent-id');
    });
  });

  describe('update', () => {
    it('should update a rule when it exists', async () => {
      // Arrange
      const updateRuleDto: UpdateRuleDto = {
        title: 'Updated Rule',
        description: 'Updated description',
      };

      service.findOne.mockResolvedValue(mockRule);
      service.update.mockResolvedValue({
        ...mockRule,
        ...updateRuleDto,
      });

      mockEnrichmentHelper.fetchGroupDetails.mockResolvedValueOnce(null);

      // Act
      const result = await controller.update(
        'rule-id-1',
        updateRuleDto,
        mockRequest,
      );

      // Assert
      expect(service.findOne).toHaveBeenCalledWith('rule-id-1');
      expect(mockEnrichmentHelper.fetchGroupDetails).toHaveBeenCalledWith(
        mockRule.group_id,
        'test',
      );
      expect(service.update).toHaveBeenCalledWith(
        'rule-id-1',
        updateRuleDto,
        undefined,
      );
      expect(result.data).toBeDefined();
    });

    it('should update a rule with new group_id', async () => {
      // Arrange
      const updateRuleDto: UpdateRuleDto = {
        group_id: 'new-group-id',
        status: RuleStatus.PUBLISHED,
      };

      // Mock a draft rule with no group_id
      const draftRule: Rule = {
        ...mockRule,
        group_id: undefined,
        status: RuleStatus.DRAFT,
      };

      // Mock the updated rule
      const updatedRule: Rule = {
        ...draftRule,
        group_id: 'new-group-id',
        status: RuleStatus.PUBLISHED,
      };

      // Setup mocks
      service.findOne.mockResolvedValue(draftRule);
      service.update.mockResolvedValue(updatedRule);
      mockEnrichmentHelper.fetchGroupDetails.mockResolvedValueOnce({
        ...mockGroup,
        id: 'new-group-id',
      });

      // Act
      await controller.update('rule-id-1', updateRuleDto, mockRequest);

      // Assert
      expect(service.findOne).toHaveBeenCalledWith('rule-id-1');
      expect(
        mockEnrichmentHelper.validateRuleStatusAndGroup,
      ).toHaveBeenCalledWith(RuleStatus.PUBLISHED, 'new-group-id');
      expect(mockEnrichmentHelper.fetchGroupDetails).toHaveBeenCalledWith(
        'new-group-id',
        'test',
      );
      expect(service.update).toHaveBeenCalledWith(
        'rule-id-1',
        updateRuleDto,
        mockGroup.name,
      );
      expect(mockFgaService.writeTuples).toHaveBeenCalledWith([
        {
          user_type: FGAType.GROUP,
          user_id: 'new-group-id',
          relation: FGARelation.PUBLISHED_GROUP,
          object_type: FGAType.CONTENT,
          object_id: 'rule-id-1',
        },
      ]);
      expect(mockFgaService.deleteTuples).not.toHaveBeenCalled();
    });

    it('should update a rule with changed group_id', async () => {
      // Arrange
      const oldGroupId = 'old-group-id';
      const newGroupId = 'new-group-id';
      const updateRuleDto: UpdateRuleDto = {
        group_id: newGroupId,
      };

      // Mock a rule with existing group_id
      const existingRule: Rule = {
        ...mockRule,
        group_id: oldGroupId,
      };

      // Mock the updated rule
      const updatedRule: Rule = {
        ...existingRule,
        group_id: newGroupId,
      };

      // Setup mocks
      service.findOne.mockResolvedValue(existingRule);
      service.update.mockResolvedValue(updatedRule);
      mockEnrichmentHelper.fetchGroupDetails.mockResolvedValueOnce({
        ...mockGroup,
        id: newGroupId,
      });

      // Act
      await controller.update('rule-id-1', updateRuleDto, mockRequest);

      // Assert
      expect(service.findOne).toHaveBeenCalledWith('rule-id-1');
      expect(mockEnrichmentHelper.fetchGroupDetails).toHaveBeenCalledWith(
        newGroupId,
        'test',
      );
      expect(service.update).toHaveBeenCalledWith(
        'rule-id-1',
        updateRuleDto,
        mockGroup.name,
      );
      expect(mockFgaService.deleteTuples).toHaveBeenCalledWith([
        {
          user_type: FGAType.GROUP,
          user_id: oldGroupId,
          relation: FGARelation.PUBLISHED_GROUP,
          object_type: FGAType.CONTENT,
          object_id: 'rule-id-1',
        },
      ]);
      expect(mockFgaService.writeTuples).toHaveBeenCalledWith([
        {
          user_type: FGAType.GROUP,
          user_id: newGroupId,
          relation: FGARelation.PUBLISHED_GROUP,
          object_type: FGAType.CONTENT,
          object_id: 'rule-id-1',
        },
      ]);
    });

    it('should update a rule with removed group_id', async () => {
      // Arrange
      const oldGroupId = 'old-group-id';
      const updateRuleDto: UpdateRuleDto = {
        group_id: undefined,
        status: RuleStatus.DRAFT,
      };

      // Mock a rule with existing group_id
      const existingRule: Rule = {
        ...mockRule,
        group_id: oldGroupId,
        status: RuleStatus.PUBLISHED,
      };

      // Mock the updated rule
      const updatedRule: Rule = {
        ...existingRule,
        group_id: undefined,
        status: RuleStatus.DRAFT,
      };

      // Setup mocks
      service.findOne.mockResolvedValue(existingRule);
      service.update.mockResolvedValue(updatedRule);
      mockEnrichmentHelper.fetchGroupDetails.mockResolvedValueOnce(null);

      // Act
      await controller.update('rule-id-1', updateRuleDto, mockRequest);

      // Assert
      expect(service.findOne).toHaveBeenCalledWith('rule-id-1');
      expect(mockEnrichmentHelper.fetchGroupDetails).toHaveBeenCalledWith(
        null,
        'test',
      );
      expect(service.update).toHaveBeenCalledWith(
        'rule-id-1',
        updateRuleDto,
        undefined,
      );
      expect(mockFgaService.deleteTuples).toHaveBeenCalledWith([
        {
          user_type: FGAType.GROUP,
          user_id: oldGroupId,
          relation: FGARelation.PUBLISHED_GROUP,
          object_type: FGAType.CONTENT,
          object_id: 'rule-id-1',
        },
      ]);
      expect(mockFgaService.writeTuples).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException when group is not found during update', async () => {
      // Arrange
      const updateRuleDto: UpdateRuleDto = {
        group_id: 'non-existent-group-id',
      };

      service.findOne.mockResolvedValue(mockRule);

      // Mock the fetchSingleUserAndGroupDetails to return null for groupDetails
      mockEnrichmentHelper.fetchGroupDetails.mockResolvedValueOnce(null);

      // Act & Assert
      await expect(
        controller.update('rule-id-1', updateRuleDto, mockRequest),
      ).rejects.toThrow(
        new NotFoundException(`Group 'non-existent-group-id' not found`),
      );
      expect(service.update).not.toHaveBeenCalled();
    });

    it('should update FGA tuples when group_id changes', async () => {
      // Arrange
      const updateRuleDto: UpdateRuleDto = {
        group_id: 'new-group-id',
      };

      const existingRule = { ...mockRule, group_id: 'old-group-id' };
      const updatedRule = { ...existingRule, group_id: 'new-group-id' };

      service.findOne.mockResolvedValue(existingRule);
      service.update.mockResolvedValue(updatedRule);

      // Mock the fetchSingleUserAndGroupDetails to return a valid group
      mockEnrichmentHelper.fetchGroupDetails.mockResolvedValueOnce({
        ...mockGroup,
        id: 'new-group-id',
      });

      // Act
      await controller.update('rule-id-1', updateRuleDto, mockRequest);

      // Assert
      expect(mockFgaService.deleteTuples).toHaveBeenCalled();
      expect(mockFgaService.writeTuples).toHaveBeenCalled();
    });

    it('should propagate NotFoundException when rule is not found', async () => {
      // Arrange
      service.findOne.mockRejectedValue(
        new NotFoundException('Rule not found'),
      );

      // Act & Assert
      await expect(
        controller.update('non-existent-id', {}, mockRequest),
      ).rejects.toThrow(NotFoundException);
      expect(service.findOne).toHaveBeenCalledWith('non-existent-id');
      expect(service.update).not.toHaveBeenCalled();
    });

    it('should not change FGA group tuples when group_id is unchanged and not in DTO', async () => {
      const ruleIdToTest = 'rule-for-no-fga-change';
      const groupIdToTest = 'group-for-no-fga-change';
      const groupNameToTest = 'Group For No FGA Change';

      const currentRuleState: Rule = {
        ...mockRule, // Base
        id: ruleIdToTest,
        group_id: groupIdToTest,
        group_name: groupNameToTest,
        status: RuleStatus.PUBLISHED,
        title: 'An Old Title',
      };

      const ruleUpdateDto: UpdateRuleDto = {
        title: 'A Brand New Title',
        // NO group_id specified in DTO, so it should remain currentRuleState.group_id
      };

      // service.update should return the rule with the same group_id
      const expectedRuleAfterUpdate: Rule = {
        ...currentRuleState,
        title: ruleUpdateDto.title,
        // group_id remains groupIdToTest
      };

      service.findOne.mockResolvedValue(currentRuleState);
      mockEnrichmentHelper.fetchGroupDetails.mockResolvedValue({
        // Group is found and valid
        ...mockGroup,
        id: groupIdToTest,
        name: groupNameToTest,
      });
      service.update.mockResolvedValue(expectedRuleAfterUpdate);

      // Clear any prior FGA mock calls from other tests in case mocks are not auto-cleared per test.
      // Though Jest typically sandboxes mocks per test.
      mockFgaService.deleteTuples.mockClear();
      mockFgaService.writeTuples.mockClear();

      await controller.update(ruleIdToTest, ruleUpdateDto, mockRequest);

      expect(service.findOne).toHaveBeenCalledWith(ruleIdToTest);
      // finalGroupId should be currentRuleState.group_id because DTO doesn't override it
      expect(mockEnrichmentHelper.fetchGroupDetails).toHaveBeenCalledWith(
        groupIdToTest,
        mockRequest.headers.authorization,
      );
      expect(service.update).toHaveBeenCalledWith(
        ruleIdToTest,
        ruleUpdateDto,
        groupNameToTest, // name from fetched groupDetails
      );

      // Core of the test: Since existingRule.group_id === updatedRule.group_id,
      // the FGA tuple modification block should not be entered.
      expect(mockFgaService.deleteTuples).not.toHaveBeenCalled();
      expect(mockFgaService.writeTuples).not.toHaveBeenCalled();
    });
  });

  describe('remove', () => {
    it('should remove a rule when it exists', async () => {
      // Arrange
      service.remove.mockResolvedValue(mockRule);
      mockFgaService.deleteTuples.mockResolvedValue(undefined);
      mockBookmarksService.deleteBookmarksForRule.mockResolvedValue(5);
      mockRuleInteractionsService.deleteInteractionsForRule.mockResolvedValue(
        3,
      );

      // Act
      const result = await controller.remove('rule-id-1', mockRequest);

      // Assert
      expect(service.remove).toHaveBeenCalledWith('rule-id-1');
      expect(mockFgaService.deleteTuples).toHaveBeenCalledWith([
        {
          user_type: FGAType.USER,
          user_id: 'external-user-id-1',
          relation: FGARelation.CONTENT_CREATOR,
          object_type: FGAType.CONTENT,
          object_id: 'rule-id-1',
        },
        {
          user_type: FGAType.WORKSPACE,
          user_id: 'detections-ai-dev',
          relation: FGARelation.WORKSPACE,
          object_type: FGAType.CONTENT,
          object_id: 'rule-id-1',
        },
        {
          user_type: FGAType.GROUP,
          user_id: 'group-id-1',
          relation: FGARelation.PUBLISHED_GROUP,
          object_type: FGAType.CONTENT,
          object_id: 'rule-id-1',
        },
      ]);
      // Assert that bookmarks and rule interactions are also deleted
      expect(mockBookmarksService.deleteBookmarksForRule).toHaveBeenCalledWith(
        'rule-id-1',
      );
      expect(
        mockRuleInteractionsService.deleteInteractionsForRule,
      ).toHaveBeenCalledWith('rule-id-1');
      expect(result.data).toBeUndefined();
    });

    it('should propagate NotFoundException when rule is not found', async () => {
      // Arrange
      service.remove.mockRejectedValue(
        new NotFoundException('Rule with ID non-existent-id not found'),
      );

      // Act & Assert
      await expect(
        controller.remove('non-existent-id', mockRequest),
      ).rejects.toThrow(
        new NotFoundException('Rule with ID non-existent-id not found'),
      );
      expect(service.remove).toHaveBeenCalledWith('non-existent-id');
      expect(mockFgaService.deleteTuples).not.toHaveBeenCalled();
      // Assert that cleanup methods are not called when rule is not found
      expect(
        mockBookmarksService.deleteBookmarksForRule,
      ).not.toHaveBeenCalled();
      expect(
        mockRuleInteractionsService.deleteInteractionsForRule,
      ).not.toHaveBeenCalled();
    });
  });

  describe('bulkDownload', () => {
    it('should return a download URL for bulk download', async () => {
      // Arrange
      const bulkDownloadDto: BulkDownloadRulesDto = {
        ids: ['rule-id-1', 'rule-id-2'],
      };

      service.bulkDownload.mockResolvedValue({
        downloadUrl: 'https://test-url.com/download.zip',
        expiresIn: 3600,
      });

      mockRuleInteractionsService.createInteraction.mockImplementation(() =>
        Promise.resolve(),
      );

      // Act
      const result = await controller.bulkDownload(
        mockRequest,
        bulkDownloadDto,
      );

      // Assert
      expect(service.bulkDownload).toHaveBeenCalledWith(bulkDownloadDto.ids);
      expect(result.data).toEqual({
        downloadUrl: 'https://test-url.com/download.zip',
        expiresIn: 3600,
      });

      // Wait a tick to allow the fire-and-forget promises to complete
      await new Promise((resolve) => setImmediate(resolve));

      expect(
        mockRuleInteractionsService.createInteraction,
      ).toHaveBeenCalledTimes(2);
      expect(
        mockRuleInteractionsService.createInteraction,
      ).toHaveBeenCalledWith(
        mockUser.id,
        'rule-id-1',
        RuleInteractionType.DOWNLOAD,
      );
      expect(
        mockRuleInteractionsService.createInteraction,
      ).toHaveBeenCalledWith(
        mockUser.id,
        'rule-id-2',
        RuleInteractionType.DOWNLOAD,
      );
    });

    it('should propagate error when bulk download fails', async () => {
      // Arrange
      const bulkDownloadDto: BulkDownloadRulesDto = {
        ids: [],
      };

      service.bulkDownload.mockRejectedValue(
        new Error('No rule IDs provided for bulk download'),
      );

      // Act & Assert
      await expect(
        controller.bulkDownload(mockRequest, bulkDownloadDto),
      ).rejects.toThrow('No rule IDs provided for bulk download');
      expect(service.bulkDownload).toHaveBeenCalledWith([]);
      expect(
        mockRuleInteractionsService.createInteraction,
      ).not.toHaveBeenCalled();
    });

    it('should propagate NotFoundException when no rules are found', async () => {
      // Arrange
      const bulkDownloadDto: BulkDownloadRulesDto = {
        ids: ['non-existent-id'],
      };

      service.bulkDownload.mockRejectedValue(
        new NotFoundException('No rules found with the provided IDs'),
      );

      // Act & Assert
      await expect(
        controller.bulkDownload(mockRequest, bulkDownloadDto),
      ).rejects.toThrow(
        new NotFoundException('No rules found with the provided IDs'),
      );
      expect(service.bulkDownload).toHaveBeenCalledWith(['non-existent-id']);
      expect(
        mockRuleInteractionsService.createInteraction,
      ).not.toHaveBeenCalled();
    });
  });

  describe('downloadSingleRule', () => {
    it('should return a download URL for a single rule', async () => {
      // Arrange
      const ruleId = 'rule-id-1';

      service.singleRuleDownload.mockResolvedValue({
        downloadUrl: 'https://test-url.com/rule.json',
        expiresIn: 3600,
      });

      mockRuleInteractionsService.createInteraction.mockImplementation(() =>
        Promise.resolve(),
      );

      // Act
      const result = await controller.downloadSingleRule(mockRequest, ruleId);

      // Assert
      expect(service.singleRuleDownload).toHaveBeenCalledWith(ruleId);
      expect(result.data).toEqual({
        downloadUrl: 'https://test-url.com/rule.json',
        expiresIn: 3600,
      });

      // Wait a tick to allow the fire-and-forget promise to complete
      await new Promise((resolve) => setImmediate(resolve));

      expect(
        mockRuleInteractionsService.createInteraction,
      ).toHaveBeenCalledWith(mockUser.id, ruleId, RuleInteractionType.DOWNLOAD);
    });

    it('should propagate errors when single rule download fails', async () => {
      // Arrange
      const ruleId = 'non-existent-rule';

      service.singleRuleDownload.mockRejectedValue(
        new NotFoundException(`Rule with ID ${ruleId} not found`),
      );

      // Act & Assert
      await expect(
        controller.downloadSingleRule(mockRequest, ruleId),
      ).rejects.toThrow(
        new NotFoundException(`Rule with ID ${ruleId} not found`),
      );
      expect(service.singleRuleDownload).toHaveBeenCalledWith(ruleId);
      expect(
        mockRuleInteractionsService.createInteraction,
      ).not.toHaveBeenCalled();
    });
  });

  describe('bookmarkRule', () => {
    it('should bookmark a rule successfully', async () => {
      // Arrange
      mockBookmarksService.bookmarkRule.mockResolvedValue(true);

      // Act
      const result = await controller.bookmarkRule('rule-id-1', mockRequest);

      // Assert
      expect(mockUserGroupsService.getInternalUser).toHaveBeenCalledWith(
        'test',
      );
      expect(mockBookmarksService.bookmarkRule).toHaveBeenCalledWith(
        mockUser.id,
        'rule-id-1',
      );
      expect(result.data).toEqual({
        success: true,
        message: 'Rule bookmarked successfully',
      });
    });

    it('should return appropriate message when rule is already bookmarked', async () => {
      // Arrange
      mockBookmarksService.bookmarkRule.mockResolvedValue(false);

      // Act
      const result = await controller.bookmarkRule('rule-id-1', mockRequest);

      // Assert
      expect(mockBookmarksService.bookmarkRule).toHaveBeenCalledWith(
        mockUser.id,
        'rule-id-1',
      );
      expect(result.data).toEqual({
        success: true,
        message: 'Rule was already bookmarked',
      });
    });

    it('should propagate errors from the bookmark service', async () => {
      // Arrange
      const error = new NotFoundException('Rule not found or not published');
      mockBookmarksService.bookmarkRule.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.bookmarkRule('non-existent-id', mockRequest),
      ).rejects.toThrow(error);
      expect(mockBookmarksService.bookmarkRule).toHaveBeenCalledWith(
        mockUser.id,
        'non-existent-id',
      );
    });
  });

  describe('unbookmarkRule', () => {
    it('should unbookmark a rule successfully', async () => {
      // Arrange
      mockBookmarksService.unbookmarkRule.mockResolvedValue(true);

      // Act
      const result = await controller.unbookmarkRule('rule-id-1', mockRequest);

      // Assert
      expect(mockUserGroupsService.getInternalUser).toHaveBeenCalledWith(
        'test',
      );
      expect(mockBookmarksService.unbookmarkRule).toHaveBeenCalledWith(
        mockUser.id,
        'rule-id-1',
      );
      expect(result.data).toEqual({
        success: true,
        message: 'Rule unbookmarked successfully',
      });
    });

    it('should return appropriate message when rule was not bookmarked', async () => {
      // Arrange
      mockBookmarksService.unbookmarkRule.mockResolvedValue(false);

      // Act
      const result = await controller.unbookmarkRule('rule-id-1', mockRequest);

      // Assert
      expect(mockBookmarksService.unbookmarkRule).toHaveBeenCalledWith(
        mockUser.id,
        'rule-id-1',
      );
      expect(result.data).toEqual({
        success: true,
        message: 'Rule was not bookmarked',
      });
    });

    it('should propagate errors from the bookmark service', async () => {
      // Arrange
      const error = new NotFoundException('Rule not found');
      mockBookmarksService.unbookmarkRule.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.unbookmarkRule('non-existent-id', mockRequest),
      ).rejects.toThrow(error);
      expect(mockBookmarksService.unbookmarkRule).toHaveBeenCalledWith(
        mockUser.id,
        'non-existent-id',
      );
    });
  });

  describe('getBookmarkedRules', () => {
    it('should return bookmarked rules with pagination', async () => {
      // Arrange
      const paginationParams = { page: 1, size: 10 };

      mockFgaService.getAccessibleResourceIds.mockResolvedValue([
        'group-id-1',
        'group-id-2',
      ]);
      mockBookmarksService.getAllUserBookmarkedRuleIds.mockResolvedValue([
        'rule-id-1',
        'rule-id-2',
      ]);
      mockSearchService.pagedSearch.mockResolvedValue({
        data: [
          { id: 'rule-id-1', title: 'Test Rule 1' },
          { id: 'rule-id-2', title: 'Test Rule 2' },
        ],
        meta: {
          total: 2,
          size: 10,
          current_page: 1,
          total_pages: 1,
          has_next_page: false,
          has_prev_page: false,
        },
        status: 'success',
      });

      // Act
      const result = await controller.getBookmarkedRules(
        mockRequest,
        paginationParams,
      );

      // Assert
      expect(mockUserGroupsService.getInternalUser).toHaveBeenCalledWith(
        'test',
      );
      expect(mockFgaService.getAccessibleResourceIds).toHaveBeenCalledWith(
        FGAType.USER,
        'external-user-id-1',
        FGARelation.GROUP_CONTENT_VIEWER,
        FGAType.GROUP,
      );
      expect(
        mockBookmarksService.getAllUserBookmarkedRuleIds,
      ).toHaveBeenCalledWith(mockUser.id, ['group-id-1', 'group-id-2']);
      expect(mockSearchService.pagedSearch).toHaveBeenCalledWith(
        undefined,
        { ids: ['rule-id-1', 'rule-id-2'] },
        { size: 10, page: 1, sort_by: 'published_at', sort_order: 'desc' },
      );
      expect(mockEnrichmentHelper.enrichRulesWithDetails).toHaveBeenCalled();
      expect(result.meta.total).toBe(2);
    });

    it('should return empty array when no bookmarks exist', async () => {
      // Arrange
      const paginationParams = { page: 1, size: 10 };

      mockFgaService.getAccessibleResourceIds.mockResolvedValue(['group-id-1']);
      mockBookmarksService.getAllUserBookmarkedRuleIds.mockResolvedValue([]);
      // No need to call pagedSearch when there are no bookmarks

      // Act
      const result = await controller.getBookmarkedRules(
        mockRequest,
        paginationParams,
      );

      // Assert
      expect(
        mockBookmarksService.getAllUserBookmarkedRuleIds,
      ).toHaveBeenCalledWith(mockUser.id, ['group-id-1']);
      // searchService should not be called when there are no bookmarks
      expect(mockSearchService.pagedSearch).not.toHaveBeenCalled();
      expect(result.data).toEqual([]);
      expect(result.meta.total).toBe(0);
    });

    it('should propagate errors from the bookmark service', async () => {
      // Arrange
      const error = new Error('Failed to get bookmarked rules');
      mockBookmarksService.getAllUserBookmarkedRuleIds.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getBookmarkedRules(mockRequest)).rejects.toThrow(
        error,
      );
    });
  });

  describe('unlikeRule', () => {
    it('should remove a like from a rule', async () => {
      // Mock data
      const ruleId = 'test-rule-id';
      const userId = 'test-user-id';
      const mockRule = {
        id: ruleId,
        likes: 0,
        dislikes: 0,
      };

      // Mock request
      const mockReq = {
        user: { sub: 'external-user-id' },
        headers: { authorization: 'test' },
        params: { id: ruleId },
      };

      // Mock services
      const mockInternalUser = { id: userId, username: 'testuser' };
      mockUserGroupsService.getInternalUser.mockResolvedValue(
        mockInternalUser as DetailedUserResponse,
      );
      service.findOne.mockResolvedValue(mockRule as Rule);
      mockRuleInteractionsService.deleteInteraction.mockResolvedValue(
        undefined,
      );

      // Execute
      const result = await controller.unlikeRule(ruleId, mockReq);

      // Assert
      expect(
        mockRuleInteractionsService.deleteInteraction,
      ).toHaveBeenCalledWith(userId, ruleId, RuleInteractionType.LIKE);
      expect(result.data).toEqual({
        success: true,
        likes: 0,
        dislikes: 0,
        message: 'Like removed successfully',
      });
    });
  });

  describe('undislikeRule', () => {
    it('should remove a dislike from a rule', async () => {
      // Mock data
      const ruleId = 'test-rule-id';
      const userId = 'test-user-id';
      const mockRule = {
        id: ruleId,
        likes: 0,
        dislikes: 0,
      };

      // Mock request
      const mockReq = {
        user: { sub: 'external-user-id' },
        headers: { authorization: 'test' },
        params: { id: ruleId },
      };

      // Mock services
      const mockInternalUser = { id: userId, username: 'testuser' };
      mockUserGroupsService.getInternalUser.mockResolvedValue(
        mockInternalUser as DetailedUserResponse,
      );
      service.findOne.mockResolvedValue(mockRule as Rule);
      mockRuleInteractionsService.deleteInteraction.mockResolvedValue(
        undefined,
      );

      // Execute
      const result = await controller.undislikeRule(ruleId, mockReq);

      // Assert
      expect(
        mockRuleInteractionsService.deleteInteraction,
      ).toHaveBeenCalledWith(userId, ruleId, RuleInteractionType.DISLIKE);
      expect(result.data).toEqual({
        success: true,
        likes: 0,
        dislikes: 0,
        message: 'Dislike removed successfully',
      });
    });
  });

  describe('likeRule', () => {
    it('should like a rule', async () => {
      // Mock data
      const ruleId = 'test-rule-id';
      const userId = 'test-user-id';
      const mockRule = {
        id: ruleId,
        likes: 1,
        dislikes: 0,
      };

      // Mock request
      const mockReq = {
        user: { sub: 'external-user-id' },
        headers: { authorization: 'test' },
        params: { id: ruleId },
      };

      // Mock services
      const mockInternalUser = { id: userId, username: 'testuser' };
      mockUserGroupsService.getInternalUser.mockResolvedValue(
        mockInternalUser as DetailedUserResponse,
      );
      service.findOne.mockResolvedValue(mockRule as Rule);
      mockRuleInteractionsService.createInteraction.mockResolvedValue(
        undefined,
      );

      // Execute
      const result = await controller.likeRule(ruleId, mockReq);

      // Assert
      expect(
        mockRuleInteractionsService.createInteraction,
      ).toHaveBeenCalledWith(userId, ruleId, RuleInteractionType.LIKE);
      expect(result.data).toEqual({
        success: true,
        likes: 1,
        dislikes: 0,
        message: 'Rule liked successfully',
      });
    });
  });

  describe('dislikeRule', () => {
    it('should dislike a rule', async () => {
      // Mock data
      const ruleId = 'test-rule-id';
      const userId = 'test-user-id';
      const mockRule = {
        id: ruleId,
        likes: 0,
        dislikes: 1,
      };

      // Mock request
      const mockReq = {
        user: { sub: 'external-user-id' },
        headers: { authorization: 'test' },
        params: { id: ruleId },
      };

      // Mock services
      const mockInternalUser = { id: userId, username: 'testuser' };
      mockUserGroupsService.getInternalUser.mockResolvedValue(
        mockInternalUser as DetailedUserResponse,
      );
      service.findOne.mockResolvedValue(mockRule as Rule);
      mockRuleInteractionsService.createInteraction.mockResolvedValue(
        undefined,
      );

      // Execute
      const result = await controller.dislikeRule(ruleId, mockReq);

      // Assert
      expect(
        mockRuleInteractionsService.createInteraction,
      ).toHaveBeenCalledWith(userId, ruleId, RuleInteractionType.DISLIKE);
      expect(result.data).toEqual({
        success: true,
        likes: 0,
        dislikes: 1,
        message: 'Rule disliked successfully',
      });
    });
  });

  describe('incrementDownloadCountForRule', () => {
    it('should increment the download count for a rule', async () => {
      // Mock data
      const ruleId = 'test-rule-id';
      const userId = 'test-user-id';
      const mockRule = {
        id: ruleId,
        likes: 5,
        dislikes: 2,
        downloads: 10,
      };

      // Mock request
      const mockReq = {
        user: { sub: 'external-user-id' },
        headers: { authorization: 'test' },
      };

      // Mock services
      const mockInternalUser = { id: userId, username: 'testuser' };
      mockUserGroupsService.getInternalUser.mockResolvedValue(
        mockInternalUser as DetailedUserResponse,
      );
      service.findOne.mockResolvedValue(mockRule as Rule);
      mockRuleInteractionsService.createInteraction.mockResolvedValue(
        undefined,
      );

      // Execute
      const result = await controller.incrementDownloadCountForRule(
        mockReq,
        ruleId,
      );

      // Assert
      expect(
        mockRuleInteractionsService.createInteraction,
      ).toHaveBeenCalledWith(userId, ruleId, RuleInteractionType.DOWNLOAD);
      expect(service.findOne).toHaveBeenCalledWith(ruleId);
      expect(result.data).toEqual({
        success: true,
        likes: 5,
        dislikes: 2,
        downloads: 10,
        message: 'Download count incremented successfully',
      });
    });

    it('should handle errors from rule interactions service', async () => {
      // Mock data
      const ruleId = 'test-rule-id';
      const userId = 'test-user-id';
      const mockRule = {
        id: ruleId,
        likes: 5,
        dislikes: 2,
        downloads: 10,
      };

      // Mock request
      const mockReq = {
        user: { sub: 'external-user-id' },
        headers: { authorization: 'test' },
      };

      // Mock services
      const mockInternalUser = { id: userId, username: 'testuser' };
      mockUserGroupsService.getInternalUser.mockResolvedValue(
        mockInternalUser as DetailedUserResponse,
      );
      service.findOne.mockResolvedValue(mockRule as Rule);

      // Mock the interaction service to throw an error
      const mockError = new Error('Failed to create interaction');
      mockRuleInteractionsService.createInteraction.mockRejectedValue(
        mockError,
      );

      // Spy on logger.error
      const loggerErrorSpy = jest.spyOn(controller['logger'], 'error');

      // Execute
      const result = await controller.incrementDownloadCountForRule(
        mockReq,
        ruleId,
      );

      // Assert
      expect(
        mockRuleInteractionsService.createInteraction,
      ).toHaveBeenCalledWith(userId, ruleId, RuleInteractionType.DOWNLOAD);
      expect(service.findOne).toHaveBeenCalledWith(ruleId);
      expect(result.data).toEqual({
        success: true,
        likes: 5,
        dislikes: 2,
        downloads: 10,
        message: 'Download count incremented successfully',
      });

      // Verify error was logged
      expect(loggerErrorSpy).toHaveBeenCalledWith(
        `Failed to create download interaction for rule ${ruleId}:`,
        mockError,
      );
    });

    it('should propagate errors from findOne', async () => {
      // Mock data
      const ruleId = 'non-existent-rule-id';
      const userId = 'test-user-id';
      const notFoundError = new NotFoundException(
        `Rule with ID ${ruleId} not found`,
      );

      // Mock request
      const mockReq = {
        user: { sub: 'external-user-id' },
        headers: { authorization: 'test' },
      };

      // Mock services
      const mockInternalUser = { id: userId, username: 'testuser' };
      mockUserGroupsService.getInternalUser.mockResolvedValue(
        mockInternalUser as DetailedUserResponse,
      );
      service.findOne.mockRejectedValue(notFoundError);
      mockRuleInteractionsService.createInteraction.mockResolvedValue(
        undefined,
      );

      // Execute & Assert
      await expect(
        controller.incrementDownloadCountForRule(mockReq, ruleId),
      ).rejects.toThrow(notFoundError);

      // Verify interaction was attempted
      expect(
        mockRuleInteractionsService.createInteraction,
      ).toHaveBeenCalledWith(userId, ruleId, RuleInteractionType.DOWNLOAD);
    });
  });

  describe('resetGroupRulesToDraft', () => {
    it('should reset all rules in a group to draft status', async () => {
      // Arrange
      const groupId = 'group-id-1';
      service.resetGroupRulesToDraft = jest.fn().mockResolvedValue(5); // 5 rules reset

      // Act
      const result = await controller.resetGroupRulesToDraft(groupId);

      // Assert
      expect(service.resetGroupRulesToDraft).toHaveBeenCalledWith(groupId);
      expect(result.data).toEqual({
        count: 5,
        message: 'Successfully reset 5 rules to draft status',
      });
    });

    it('should return count 0 if no rules were reset', async () => {
      // Arrange
      const groupId = 'empty-group';
      service.resetGroupRulesToDraft = jest.fn().mockResolvedValue(0); // No rules reset

      // Act
      const result = await controller.resetGroupRulesToDraft(groupId);

      // Assert
      expect(service.resetGroupRulesToDraft).toHaveBeenCalledWith(groupId);
      expect(result.data).toEqual({
        count: 0,
        message: 'Successfully reset 0 rules to draft status',
      });
    });

    it('should propagate errors from the service', async () => {
      // Arrange
      const groupId = 'error-group';
      const error = new Error('Service error');
      service.resetGroupRulesToDraft = jest.fn().mockRejectedValue(error);

      // Act & Assert
      await expect(controller.resetGroupRulesToDraft(groupId)).rejects.toThrow(
        error,
      );
      expect(service.resetGroupRulesToDraft).toHaveBeenCalledWith(groupId);
    });
  });

  describe('resetUserPublishedRulesToDraft', () => {
    it('should reset all published rules created by a user but owned by groups to draft status', async () => {
      // Arrange
      const userId = 'user-id-1';
      service.resetUserPublishedRulesToDraft = jest.fn().mockResolvedValue(5); // 5 rules reset

      // Act
      const result = await controller.resetUserPublishedRulesToDraft(userId);

      // Assert
      expect(service.resetUserPublishedRulesToDraft).toHaveBeenCalledWith(
        userId,
      );
      expect(result.data).toEqual({
        count: 5,
        message: 'Successfully reset 5 rules to draft status',
      });
    });

    it('should return count 0 if no rules were reset', async () => {
      // Arrange
      const userId = 'user-with-no-rules';
      service.resetUserPublishedRulesToDraft = jest.fn().mockResolvedValue(0); // No rules reset

      // Act
      const result = await controller.resetUserPublishedRulesToDraft(userId);

      // Assert
      expect(service.resetUserPublishedRulesToDraft).toHaveBeenCalledWith(
        userId,
      );
      expect(result.data).toEqual({
        count: 0,
        message: 'Successfully reset 0 rules to draft status',
      });
    });

    it('should propagate errors from the service', async () => {
      // Arrange
      const userId = 'error-user';
      const error = new Error('Service error');
      service.resetUserPublishedRulesToDraft = jest
        .fn()
        .mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.resetUserPublishedRulesToDraft(userId),
      ).rejects.toThrow(error);
      expect(service.resetUserPublishedRulesToDraft).toHaveBeenCalledWith(
        userId,
      );
    });
  });

  describe('updateMyRulesUsername', () => {
    it('should update username for rules created by the user', async () => {
      // Arrange
      const mockRequestWithUser = {
        ...mockRequest,
        user: {
          sub: 'external-user-id-1',
        },
        headers: {
          authorization: 'TEST_TOKEN',
        },
      };

      // Mock resources from with-visible-resources decorator
      const visibleRuleIds = ['rule-id-1', 'rule-id-2', 'rule-id-3'];
      mockFgaService.getAccessibleResourceIds.mockResolvedValue(visibleRuleIds);

      // Mock internal user
      const mockInternalUser: DetailedUserResponse = {
        ...mockUser,
        username: 'updated-username',
        preferences: {
          preferred_detection_language: null,
        },
      };
      mockUserGroupsService.getInternalUser.mockResolvedValue(mockInternalUser);

      // Mock service method
      service.updateMyRulesUsername.mockResolvedValue(undefined);

      // Act
      await controller.updateMyRulesUsername(mockRequestWithUser);

      // Assert
      expect(mockUserGroupsService.getInternalUser).toHaveBeenCalledWith(
        'TEST_TOKEN',
      );
      expect(service.updateMyRulesUsername).toHaveBeenCalledWith(
        'user-id-1',
        'updated-username',
        visibleRuleIds,
      );
    });

    it('should throw BadRequestException when user has no username', async () => {
      // Arrange
      const mockRequestWithUser = {
        ...mockRequest,
        user: {
          sub: 'external-user-id-1',
        },
        headers: {
          authorization: 'TEST_TOKEN',
        },
      };

      // Mock internal user with no username
      const mockInternalUserNoUsername: DetailedUserResponse = {
        ...mockUser,
        username: null,
        preferences: {
          preferred_detection_language: null,
        },
      };
      mockUserGroupsService.getInternalUser.mockResolvedValue(
        mockInternalUserNoUsername,
      );

      // Act & Assert
      await expect(
        controller.updateMyRulesUsername(mockRequestWithUser),
      ).rejects.toThrow(BadRequestException);

      expect(mockUserGroupsService.getInternalUser).toHaveBeenCalledWith(
        'TEST_TOKEN',
      );
      expect(service.updateMyRulesUsername).not.toHaveBeenCalled();
    });
  });

  describe('getMyRules', () => {
    it('should return rules created by the user with default parameters', async () => {
      // Arrange
      mockSearchService.pagedSearch.mockResolvedValue({
        data: mockOpenSearchRules,
        meta: {
          total: 2,
          current_page: 1,
          page_size: 100,
          total_pages: 1,
          size: 2,
          has_next_page: false,
          has_prev_page: false,
        },
        status: 'success',
      });

      // Act
      const result = await controller.getMyRules(mockRequest);

      // Assert
      expect(mockSearchService.pagedSearch).toHaveBeenCalledWith(
        undefined,
        {
          owner_ids: ['group-id-1', 'user-id-1'],
          created_by: ['user-id-1'],
        },
        {
          size: 100,
          page: 1,
          sort_by: SortField.PUBLISHED_AT,
          sort_order: SortOrder.DESC,
        },
      );
      expect(mockEnrichmentHelper.enrichRulesWithDetails).toHaveBeenCalledWith(
        mockUnenrichedRules,
        'test',
        mockUser.id,
      );
      expect(result.data.length).toBe(2);
      expect(result.meta.total).toBe(2);
    });

    it('should filter by status and sort my rules when parameters are provided', async () => {
      // Arrange
      mockSearchService.pagedSearch.mockResolvedValue({
        data: [mockOpenSearchRules[0]],
        meta: {
          total: 1,
          current_page: 1,
          page_size: 20,
          total_pages: 1,
          size: 1,
          has_next_page: false,
          has_prev_page: false,
        },
        status: 'success',
      });

      // Act
      const result = await controller.getMyRules(
        mockRequest,
        { page: 1, size: 20 },
        RuleOrderBy.LIKES,
        SortOrder.DESC,
        'process execution',
      );

      // Assert
      expect(mockSearchService.pagedSearch).toHaveBeenCalledWith(
        'process execution',
        {
          owner_ids: ['group-id-1', 'user-id-1'],
          created_by: ['user-id-1'],
        },
        {
          size: 20,
          page: 1,
          sort_by: SortField.LIKES,
          sort_order: SortOrder.DESC,
        },
      );
      expect(result.data.length).toBe(1);
    });

    it('should handle custom pagination for my rules', async () => {
      // Arrange
      mockSearchService.pagedSearch.mockResolvedValue({
        data: mockOpenSearchRules,
        meta: {
          total: 10,
          current_page: 2,
          page_size: 5,
          total_pages: 2,
          size: 5,
          has_next_page: false,
          has_prev_page: true,
        },
        status: 'success',
      });

      // Act
      const result = await controller.getMyRules(mockRequest, {
        page: 2,
        size: 5,
      });

      // Assert
      expect(mockSearchService.pagedSearch).toHaveBeenCalledWith(
        undefined,
        {
          owner_ids: ['group-id-1', 'user-id-1'],
          created_by: ['user-id-1'],
        },
        {
          size: 5,
          page: 2,
          sort_by: SortField.PUBLISHED_AT,
          sort_order: SortOrder.DESC,
        },
      );
      expect(result.meta.total).toBe(10);
      expect(result.meta.current_page).toBe(2);
      expect(result.meta.page_size).toBe(5);
    });
  });

  describe('getCountsByUser', () => {
    beforeEach(() => {
      // Add getCountsByUser to the mock service
      service.getCountsByUser = jest.fn();
      // Reset FGA mock for each test if needed, or ensure it's set appropriately
      mockFgaService.checkUserAuthorization.mockClear();
    });

    it('should return rule counts for specified users (no group_id)', async () => {
      // Arrange
      const mockUserCounts = {
        'user-id-1': {
          id: 'user-id-1',
          detections_count: 10,
          likes_count: 25,
          downloads_count: 50,
          dislikes_count: 8,
          followers_count: 0,
          following_count: 0,
          bookmarks_count: 0,
        },
      };

      const mockReq = {
        headers: {
          authorization: 'Bearer TEST_TOKEN',
        },
        user: {
          sub: 'mock-user-id',
        },
      };

      const mockVisibleUserIds = ['user-id-1'];
      jest
        .spyOn(
          require('../auth/decorators/with-visible-resources.decorator'),
          'getVisibleResources',
        )
        .mockReturnValue(mockVisibleUserIds);

      service.getCountsByUser.mockResolvedValue({
        'user-id-1': mockUserCounts['user-id-1'],
      });
      mockFgaService.checkUserAuthorization.mockResolvedValue(true); // Default to true for other checks if any

      // Act
      const result = await controller.getCountsByUser(mockReq, {
        user_ids: ['user-id-1', 'user-id-2'], // Requesting two, but only one is visible
      });

      // Assert
      // Check that FGA was NOT called for group permission since no group_id is passed
      expect(mockFgaService.checkUserAuthorization).not.toHaveBeenCalledWith(
        expect.objectContaining({
          relation: FGARelation.CAN_VIEW_GROUP_DETAILS,
          object_type: FGAType.GROUP,
        }),
      );
      expect(service.getCountsByUser).toHaveBeenCalledWith(
        mockVisibleUserIds,
        undefined, // group_id is undefined
      );
      expect(result.data).toEqual({
        users: {
          'user-id-1': mockUserCounts['user-id-1'],
        },
      });
    });

    it('should return rule counts for specified users when group_id is provided', async () => {
      const groupId = 'test-group-id';
      const mockUserCounts = {
        'user-id-1': {
          id: 'user-id-1',
          detections_count: 5,
          likes_count: 2,
          downloads_count: 10,
          dislikes_count: 1,
          followers_count: 0,
          following_count: 0,
          bookmarks_count: 0,
        },
      };

      const mockReq = {
        headers: { authorization: 'Bearer TEST_TOKEN' },
        user: { sub: 'mock-user-id' },
      };

      const mockVisibleUserIds = ['user-id-1'];
      jest
        .spyOn(
          require('../auth/decorators/with-visible-resources.decorator'),
          'getVisibleResources',
        )
        .mockReturnValue(mockVisibleUserIds);

      service.getCountsByUser.mockResolvedValue({
        'user-id-1': mockUserCounts['user-id-1'],
      });
      // Mock FGA check for group to be true
      mockFgaService.checkUserAuthorization.mockImplementation(
        async (params) => {
          if (
            params.object_type === FGAType.GROUP &&
            params.object_id === groupId
          ) {
            return true;
          }
          return true; // Default for other potential checks
        },
      );

      const result = await controller.getCountsByUser(mockReq, {
        user_ids: ['user-id-1', 'user-id-unauthorized'],
        group_id: groupId,
      });

      expect(mockFgaService.checkUserAuthorization).toHaveBeenCalledWith({
        user_type: FGAType.USER,
        user_id: 'mock-user-id',
        relation: FGARelation.CAN_VIEW_GROUP_DETAILS,
        object_type: FGAType.GROUP,
        object_id: groupId,
      });
      expect(service.getCountsByUser).toHaveBeenCalledWith(
        mockVisibleUserIds,
        groupId,
      );
      expect(result.data).toEqual({
        users: {
          'user-id-1': mockUserCounts['user-id-1'],
        },
      });
    });

    it('should throw ForbiddenException if user cannot view group when group_id is provided', async () => {
      const groupId = 'forbidden-group-id';
      const mockReq = {
        headers: { authorization: 'Bearer TEST_TOKEN' },
        user: { sub: 'mock-user-id' },
      };

      const mockVisibleUserIds = ['user-id-1'];
      jest
        .spyOn(
          require('../auth/decorators/with-visible-resources.decorator'),
          'getVisibleResources',
        )
        .mockReturnValue(mockVisibleUserIds);

      // Mock FGA check for group to be false
      mockFgaService.checkUserAuthorization.mockImplementation(
        async (params) => {
          if (
            params.object_type === FGAType.GROUP &&
            params.object_id === groupId
          ) {
            return false;
          }
          return true; // Default for other potential checks
        },
      );

      await expect(
        controller.getCountsByUser(mockReq, {
          user_ids: ['user-id-1'],
          group_id: groupId,
        }),
      ).rejects.toThrow(ForbiddenException);

      expect(mockFgaService.checkUserAuthorization).toHaveBeenCalledWith({
        user_type: FGAType.USER,
        user_id: 'mock-user-id',
        relation: FGARelation.CAN_VIEW_GROUP_DETAILS,
        object_type: FGAType.GROUP,
        object_id: groupId,
      });
      expect(service.getCountsByUser).not.toHaveBeenCalled();
    });

    it('should return empty object when no users are specified', async () => {
      // Arrange
      const mockCounts = {};
      // Mock request object
      const mockReq = {
        headers: {
          authorization: 'Bearer TEST_TOKEN',
        },
        user: {
          sub: 'mock-user-id',
        },
      };
      service.getCountsByUser.mockResolvedValue(mockCounts);

      // Act
      const result = await controller.getCountsByUser(mockReq, {
        user_ids: [],
      });

      // Assert
      expect(service.getCountsByUser).toHaveBeenCalledWith([], undefined);
      expect(result.data).toEqual({ users: {} });
    });

    it('should handle errors from the service', async () => {
      // Arrange
      const error = new InternalServerErrorException(
        'Failed to get rule counts by user',
      );

      // Mock request object
      const mockReq = {
        headers: {
          authorization: 'Bearer TEST_TOKEN',
        },
        user: {
          sub: 'mock-user-id',
        },
      };

      // Mock getVisibleResources to return only user-id-1
      const mockVisibleResources = ['user-id-1'];
      jest
        .spyOn(
          require('../auth/decorators/with-visible-resources.decorator'),
          'getVisibleResources',
        )
        .mockReturnValue(mockVisibleResources);

      // Mock service to throw error
      service.getCountsByUser.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.getCountsByUser(mockReq, {
          user_ids: ['user-id-1', 'user-id-2'],
        }),
      ).rejects.toThrow(error);
      expect(service.getCountsByUser).toHaveBeenCalledWith(
        ['user-id-1'],
        undefined, // Ensure group_id is undefined
      );
    });
  });

  describe('getCountsByGroup', () => {
    beforeEach(() => {
      // Add getCountsByGroup to the mock service
      service.getCountsByGroup = jest.fn();
    });

    it('should return rule counts for specified groups', async () => {
      // Arrange
      const mockCounts = {
        'group-id-1': {
          id: 'group-id-1',
          detections_count: 10,
          likes_count: 25,
          downloads_count: 50,
          dislikes_count: 8,
          followers_count: 0,
          following_count: 0,
          bookmarks_count: 0,
        },
        'group-id-2': {
          id: 'group-id-2',
          detections_count: 5,
          likes_count: 15,
          downloads_count: 30,
          dislikes_count: 5,
          followers_count: 0,
          following_count: 0,
          bookmarks_count: 0,
        },
      };

      // Mock request object
      const mockReq = {
        headers: {
          authorization: 'Bearer TEST_TOKEN',
        },
        user: {
          sub: 'mock-user-id',
        },
      };

      // Mock getVisibleResources to return only group-id-1
      const mockVisibleResources = ['group-id-1'];
      jest
        .spyOn(
          require('../auth/decorators/with-visible-resources.decorator'),
          'getVisibleResources',
        )
        .mockReturnValue(mockVisibleResources);

      // Mock service to return counts for the authorized group
      service.getCountsByGroup.mockResolvedValue({
        'group-id-1': mockCounts['group-id-1'],
      });

      // Act
      const result = await controller.getCountsByGroup(mockReq, {
        group_ids: ['group-id-1', 'group-id-2'],
      });

      // Assert
      expect(service.getCountsByGroup).toHaveBeenCalledWith(['group-id-1']);
      expect(result.data).toEqual({
        groups: {
          'group-id-1': mockCounts['group-id-1'],
        },
      });
    });

    it('should return empty object when no groups are specified', async () => {
      // Arrange
      const mockCounts = {};
      // Mock request object
      const mockReq = {
        headers: {
          authorization: 'Bearer TEST_TOKEN',
        },
        user: {
          sub: 'mock-user-id',
        },
      };
      service.getCountsByGroup.mockResolvedValue(mockCounts);

      // Act
      const result = await controller.getCountsByGroup(mockReq, {
        group_ids: [],
      });

      // Assert
      expect(service.getCountsByGroup).toHaveBeenCalledWith([]);
      expect(result.data).toEqual({ groups: {} });
    });

    it('should handle errors from the service', async () => {
      // Arrange
      const error = new InternalServerErrorException(
        'Failed to get rule counts by group',
      );

      // Mock request object
      const mockReq = {
        headers: {
          authorization: 'Bearer TEST_TOKEN',
        },
        user: {
          sub: 'mock-user-id',
        },
      };

      // Mock getVisibleResources to return only group-id-1
      const mockVisibleResources = ['group-id-1'];
      jest
        .spyOn(
          require('../auth/decorators/with-visible-resources.decorator'),
          'getVisibleResources',
        )
        .mockReturnValue(mockVisibleResources);

      // Mock service to throw error
      service.getCountsByGroup.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.getCountsByGroup(mockReq, {
          group_ids: ['group-id-1', 'group-id-2'],
        }),
      ).rejects.toThrow(error);
      expect(service.getCountsByGroup).toHaveBeenCalledWith(['group-id-1']);
    });
  });
});
