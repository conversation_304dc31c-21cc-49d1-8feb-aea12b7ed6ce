import { Modu<PERSON> } from '@nestjs/common';
import { Rules<PERSON>ontroller } from './rules.controller';
import { RulesService } from './rules.service';
import { RuleRepository } from './repositories/rule.repository';
import { DynamoDBModule } from '../dynamodb/dynamodb.module';
import { S3Module } from '../s3/s3.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { UserGroupsService } from '../auth/services/user-groups.service';
import { HttpModule } from '@nestjs/axios';
import { FgaModule } from '../auth/fga/fga.module';
import { BookmarksService } from '../bookmarks/bookmarks.service';
import { BookmarkRepository } from '../bookmarks/repositories/bookmark.repository';
import { EnrichmentHelper } from '../common/enrichment-helper';
import { RuleInteractionsService } from '../rule-interactions/rule-interactions.service';
import { RuleInteractionRepository } from '../rule-interactions/repositories/rule-interaction.repository';
import { SearchModule } from '../search/search.module';
import { CacheModule } from '@nestjs/cache-manager';
import { RuleMetadataController } from './controllers/rule-metadata.controller';
import { RuleMetadataService } from './services/rule-metadata.service';
import { ParsingModule } from '../parsing/parsing.module';
import { OpenSearchService } from '../opensearch/opensearch.service';
import { OpenSearchConfigService } from '../opensearch/config/opensearch.config';

/**
 * Module for rule management
 */
@Module({
  imports: [
    ParsingModule,
    DynamoDBModule,
    S3Module,
    ConfigModule,
    HttpModule,
    FgaModule,
    SearchModule,
    CacheModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        ttl: configService.get<number>('RULES_COUNTS_CACHE_TTL', 900) * 1000, // Convert to milliseconds
        max: configService.get<number>('RULES_COUNTS_CACHE_MAX_ITEMS', 1000),
        isGlobal: false,
      }),
    }),
  ],
  controllers: [RulesController, RuleMetadataController],
  providers: [
    RulesService,
    UserGroupsService,
    RuleRepository,
    ConfigService,
    BookmarksService,
    BookmarkRepository,
    EnrichmentHelper,
    RuleInteractionsService,
    RuleInteractionRepository,
    RuleMetadataService,
    OpenSearchService,
    OpenSearchConfigService,
  ],
  exports: [RulesService, RuleInteractionsService],
})
export class RulesModule {}
