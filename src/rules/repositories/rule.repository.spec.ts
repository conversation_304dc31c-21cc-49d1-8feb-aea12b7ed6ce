import { Test, TestingModule } from '@nestjs/testing';
import { RuleRepository } from './rule.repository';
import { DynamoDBService } from '../../dynamodb/dynamodb.service';
import { RuleStatus, RuleType, RuleOrderBy } from '../models/rule.model';
import { QueryCommand, ScanCommand } from '@aws-sdk/lib-dynamodb';
import { StoredRule } from '../schemas/rule.schema';

describe('RuleRepository', () => {
  let repository: RuleRepository;
  let dynamoDBService: DynamoDBService;
  let mockDocumentClient: any;

  const mockRules: StoredRule[] = [
    {
      id: 'rule1',
      title: 'Test Rule 1',
      description: 'Test Description 1',
      ai_generated: {
        description: false,
        title: false,
        content: false,
        tags: false,
      },
      rule_type: RuleType.SIGMA,
      status: RuleStatus.PUBLISHED,
      created_at: **********,
      updated_at: **********,
      published_at: **********,
      created_by: 'test-user',
      owner_id: 'group1',
      content: 'Test Content 1',
      tags: [],
      metadata: {},
      test_cases: [],
      version: 1,
      contributor: 'test-contributor-1',
      group_name: 'test-group-name-1',
      likes: 0,
      downloads: 0,
      dislikes: 0,
    },
    {
      id: 'rule2',
      title: 'Test Rule 2',
      description: 'Test Description 2',
      ai_generated: {
        description: false,
        title: false,
        content: false,
        tags: false,
      },
      rule_type: RuleType.SIGMA,
      status: RuleStatus.DRAFT,
      created_at: **********,
      updated_at: **********,
      published_at: **********,
      created_by: 'test-user',
      owner_id: 'group2',
      content: 'Test Content 2',
      tags: [],
      metadata: {},
      test_cases: [],
      version: 1,
      contributor: 'test-contributor-2',
      group_name: 'test-group-name-2',
      likes: 0,
      downloads: 0,
      dislikes: 0,
    },
    {
      id: 'rule3',
      title: 'Test Rule 3',
      description: 'Test Description 3',
      ai_generated: {
        description: false,
        title: false,
        content: false,
        tags: false,
      },
      rule_type: RuleType.KQL,
      status: RuleStatus.PUBLISHED,
      created_at: **********,
      updated_at: **********,
      published_at: **********,
      created_by: 'user2',
      owner_id: 'group1',
      content: 'Test Content 3',
      tags: [],
      metadata: {},
      test_cases: [],
      version: 1,
      contributor: 'test-contributor-3',
      group_name: 'test-group-name-3',
      likes: 0,
      downloads: 0,
      dislikes: 0,
    },
  ];

  beforeEach(async () => {
    // Create mock document client
    mockDocumentClient = {
      send: jest.fn(),
    };

    // Create mock DynamoDBService
    const mockDynamoDBService = {
      getDocumentClient: jest.fn().mockReturnValue(mockDocumentClient),
      getTableName: jest.fn().mockReturnValue('test-table'),
      incrementCounter: jest.fn(),
      decrementCounter: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RuleRepository,
        {
          provide: DynamoDBService,
          useValue: mockDynamoDBService,
        },
      ],
    }).compile();

    repository = module.get<RuleRepository>(RuleRepository);
    dynamoDBService = module.get<DynamoDBService>(DynamoDBService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('findByUserId', () => {
    it('should query rules by user ID with owner IDs filter', async () => {
      // Mock the findMinimalRulesByOwnerId method
      jest
        .spyOn(repository as any, 'findMinimalRulesByOwnerId')
        .mockResolvedValueOnce([
          {
            id: 'rule1',
            owner_id: 'group1',
            published_at: **********,
            created_at: **********,
            likes: 0,
            downloads: 0,
          },
        ]);

      // Mock the fetchFullRuleDetails method
      jest
        .spyOn(repository as any, 'fetchFullRuleDetails')
        .mockResolvedValueOnce([mockRules[0]]);

      const result = await repository.findByUserId(
        'test-user',
        ['group1'],
        RuleOrderBy.PUBLISHED_AT,
        1,
        10,
      );

      // Verify the correct methods were called
      expect(
        (repository as any).findMinimalRulesByOwnerId,
      ).toHaveBeenCalledWith(
        'group1',
        'OwnerPublishedIndex',
        ['test-user'],
        10,
      );
      expect((repository as any).fetchFullRuleDetails).toHaveBeenCalledWith([
        'rule1',
      ]);

      // Verify the result
      expect(result).toEqual({
        items: [mockRules[0]],
        total: 1,
      });
    });

    it('should sort results by created_at when specified', async () => {
      // Mock the findMinimalRulesByOwnerId method
      jest
        .spyOn(repository as any, 'findMinimalRulesByOwnerId')
        .mockResolvedValueOnce([
          {
            id: 'rule1',
            owner_id: 'group1',
            published_at: **********,
            created_at: **********,
            likes: 0,
            downloads: 0,
          },
        ]);

      // Mock the fetchFullRuleDetails method
      jest
        .spyOn(repository as any, 'fetchFullRuleDetails')
        .mockResolvedValueOnce([mockRules[0]]);

      const result = await repository.findByUserId(
        'test-user',
        ['group1'],
        RuleOrderBy.CREATED_AT,
        1,
        10,
      );

      // Verify the correct methods were called with the right index
      expect(
        (repository as any).findMinimalRulesByOwnerId,
      ).toHaveBeenCalledWith('group1', 'OwnerIdIndex', ['test-user'], 10);

      // Verify the result
      expect(result).toEqual({
        items: [mockRules[0]],
        total: 1,
      });
    });

    it('should handle pagination correctly', async () => {
      // Create mock minimal rules
      const mockMinimalRules = [
        {
          id: 'rule1',
          owner_id: 'group1',
          published_at: **********,
          created_at: **********,
          likes: 0,
          downloads: 0,
        },
        {
          id: 'rule2',
          owner_id: 'group1',
          published_at: 1234567880,
          created_at: 1234567880,
          likes: 0,
          downloads: 0,
        },
        {
          id: 'rule3',
          owner_id: 'group1',
          published_at: 1234567870,
          created_at: 1234567870,
          likes: 0,
          downloads: 0,
        },
      ];

      // Mock the findMinimalRulesByOwnerId method
      jest
        .spyOn(repository as any, 'findMinimalRulesByOwnerId')
        .mockResolvedValueOnce(mockMinimalRules);

      // Mock the fetchFullRuleDetails method
      jest
        .spyOn(repository as any, 'fetchFullRuleDetails')
        .mockResolvedValueOnce([mockRules[0], mockRules[1]]);

      // Request page 2 with 2 items per page
      const result = await repository.findByUserId(
        'test-user',
        ['group1'],
        RuleOrderBy.PUBLISHED_AT,
        2,
        1,
      );

      // Verify the correct methods were called
      expect(
        (repository as any).findMinimalRulesByOwnerId,
      ).toHaveBeenCalledWith(
        'group1',
        'OwnerPublishedIndex',
        ['test-user'],
        2, // page * limit
      );
      expect((repository as any).fetchFullRuleDetails).toHaveBeenCalledWith([
        'rule2',
      ]);

      // Verify the result contains the second item
      expect(result.total).toBe(3);
      expect(result.items.length).toBe(1);
    });

    it('should handle multiple owner IDs', async () => {
      // Mock the findMinimalRulesByOwnerId method
      jest
        .spyOn(repository as any, 'findMinimalRulesByOwnerId')
        .mockResolvedValueOnce([
          {
            id: 'rule1',
            owner_id: 'group1',
            published_at: **********,
            created_at: **********,
            likes: 0,
            downloads: 0,
          },
        ])
        .mockResolvedValueOnce([
          {
            id: 'rule2',
            owner_id: 'group2',
            published_at: 1234567880,
            created_at: 1234567880,
            likes: 0,
            downloads: 0,
          },
        ]);

      // Mock the fetchFullRuleDetails method
      jest
        .spyOn(repository as any, 'fetchFullRuleDetails')
        .mockResolvedValueOnce([mockRules[0], mockRules[1]]);

      const result = await repository.findByUserId(
        'test-user',
        ['group1', 'group2'],
        RuleOrderBy.PUBLISHED_AT,
        1,
        10,
      );

      // Verify the correct methods were called for each group
      expect(
        (repository as any).findMinimalRulesByOwnerId,
      ).toHaveBeenCalledWith(
        'group1',
        'OwnerPublishedIndex',
        ['test-user'],
        10,
      );
      expect(
        (repository as any).findMinimalRulesByOwnerId,
      ).toHaveBeenCalledWith(
        'group2',
        'OwnerPublishedIndex',
        ['test-user'],
        10,
      );
      expect((repository as any).fetchFullRuleDetails).toHaveBeenCalledWith([
        'rule1',
        'rule2',
      ]);

      // Verify the result
      expect(result).toEqual({
        items: [mockRules[0], mockRules[1]],
        total: 2,
      });
    });
  });

  describe('findByIds', () => {
    it('should find rules by IDs', async () => {
      // Mock the batchGet method
      repository.batchGet = jest.fn().mockResolvedValue({
        successful: [mockRules[0]],
        failed: [],
      });

      const result = await repository.findByIds(['rule1']);

      // Verify batchGet was called with correct parameters
      expect(repository.batchGet).toHaveBeenCalledWith([{ id: 'rule1' }]);

      // Verify the result
      expect(result).toEqual([mockRules[0]]);
    });

    it('should handle missing rules', async () => {
      // Mock the batchGet method
      repository.batchGet = jest.fn().mockResolvedValue({
        successful: [mockRules[0]],
        failed: [],
      });

      // Request two rules but only one exists
      const result = await repository.findByIds(['rule1', 'nonexistent']);

      // Verify the result only includes the found rule
      expect(result.length).toBe(1);
      expect(result[0].id).toBe('rule1');
    });

    it('should handle empty input array', async () => {
      // Create a mock for batchGet
      const mockBatchGet = jest.fn();
      repository.batchGet = mockBatchGet;

      const result = await repository.findByIds([]);

      // Verify batchGet was not called
      expect(mockBatchGet).not.toHaveBeenCalled();

      // Verify empty array is returned
      expect(result).toEqual([]);
    });

    it('should handle large arrays by chunking', async () => {
      // Create a large array of IDs
      const largeIdArray = Array.from({ length: 150 }, (_, i) => `rule${i}`);

      // Create a proper Jest mock function
      const mockBatchGet = jest.fn();

      // Set up the mock responses
      mockBatchGet
        .mockResolvedValueOnce({
          successful: Array.from({ length: 100 }, (_, i) => ({
            ...mockRules[0],
            id: `rule${i}`,
          })),
          failed: [],
        })
        .mockResolvedValueOnce({
          successful: Array.from({ length: 50 }, (_, i) => ({
            ...mockRules[0],
            id: `rule${i + 100}`,
          })),
          failed: [],
        });

      // Replace the repository's batchGet with our mock
      repository.batchGet = mockBatchGet;

      // Request all rules
      const result = await repository.findByIds(largeIdArray);

      // Verify batchGet was called twice (for two chunks)
      expect(mockBatchGet).toHaveBeenCalledTimes(2);

      // First chunk should have 100 items
      expect(mockBatchGet.mock.calls[0][0].length).toBe(100);

      // Second chunk should have 50 items
      expect(mockBatchGet.mock.calls[1][0].length).toBe(50);

      // Verify all rules were returned
      expect(result.length).toBe(150);

      // Verify order is maintained
      for (let i = 0; i < 150; i++) {
        expect(result[i].id).toBe(`rule${i}`);
      }
    });

    it('should handle errors from batchGet', async () => {
      // Mock batchGet to throw an error
      const error = new Error('DynamoDB error');
      repository.batchGet = jest.fn().mockRejectedValue(error);

      // Verify the error is propagated
      await expect(repository.findByIds(['rule1'])).rejects.toThrow(error);
    });
  });

  describe('findFollowedRules', () => {
    it('should find rules from followed groups and users', async () => {
      // Mock the findMinimalRulesByOwnerId method
      jest
        .spyOn(repository as any, 'findMinimalRulesByOwnerId')
        .mockResolvedValueOnce([
          {
            id: 'rule1',
            owner_id: 'group1',
            published_at: **********,
            created_at: **********,
            likes: 0,
            downloads: 0,
          },
        ])
        .mockResolvedValueOnce([
          {
            id: 'rule3',
            owner_id: 'group1',
            published_at: 1234567870,
            created_at: 1234567870,
            likes: 0,
            downloads: 0,
          },
        ]);

      // Mock the fetchFullRuleDetails method
      jest
        .spyOn(repository as any, 'fetchFullRuleDetails')
        .mockResolvedValueOnce([mockRules[0], mockRules[2]]);

      const result = await repository.findFollowedRules(
        ['group1'], // followedGroupIds
        ['user2'], // followedUserIds
        ['group1', 'group2'], // ownerIds
        1, // page
        10, // limit
      );

      // Verify the correct methods were called
      expect(
        (repository as any).findMinimalRulesByOwnerId,
      ).toHaveBeenCalledWith('group2', 'OwnerPublishedIndex', ['user2'], 10);
      expect(
        (repository as any).findMinimalRulesByOwnerId,
      ).toHaveBeenCalledWith('group1', 'OwnerPublishedIndex', undefined, 10);
      expect((repository as any).fetchFullRuleDetails).toHaveBeenCalledWith([
        'rule1',
        'rule3',
      ]);

      // Verify the result
      expect(result).toEqual({
        items: [mockRules[0], mockRules[2]],
        total: 2,
      });
    });

    it('should deduplicate rules from different queries', async () => {
      // Mock the findMinimalRulesByOwnerId method to return the same rule from different queries
      jest
        .spyOn(repository as any, 'findMinimalRulesByOwnerId')
        .mockResolvedValueOnce([
          {
            id: 'rule1',
            owner_id: 'group1',
            published_at: **********,
            created_at: **********,
            likes: 0,
            downloads: 0,
          },
        ])
        .mockResolvedValueOnce([
          {
            id: 'rule1',
            owner_id: 'group1',
            published_at: **********,
            created_at: **********,
            likes: 0,
            downloads: 0,
          },
        ]);

      // Mock the fetchFullRuleDetails method
      jest
        .spyOn(repository as any, 'fetchFullRuleDetails')
        .mockResolvedValueOnce([mockRules[0]]);

      const result = await repository.findFollowedRules(
        ['group1'], // followedGroupIds
        ['user2'], // followedUserIds
        ['group1', 'group2'], // ownerIds
        1, // page
        10, // limit
      );

      // Verify the result contains only one instance of the rule
      expect(result.total).toBe(1);
      expect(result.items.length).toBe(1);
      expect(result.items[0].id).toBe('rule1');
    });
  });

  describe('incrementCounter and decrementCounter', () => {
    it('should call incrementCounter on DynamoDBService', async () => {
      await repository.incrementCounter('rule1', 'likes');

      expect(dynamoDBService.incrementCounter).toHaveBeenCalledWith(
        'test-table',
        'rule1',
        'likes',
      );
    });

    it('should call decrementCounter on DynamoDBService', async () => {
      await repository.decrementCounter('rule1', 'likes');

      expect(dynamoDBService.decrementCounter).toHaveBeenCalledWith(
        'test-table',
        'rule1',
        'likes',
      );
    });
  });

  describe('findRulesByGroupId', () => {
    it('should find rules by group ID', async () => {
      // Arrange
      const groupId = 'group1';

      // Create a mock QueryCommand
      const queryCommand = new QueryCommand({
        TableName: 'test-table',
        IndexName: 'OwnerIdIndex',
        KeyConditionExpression: 'owner_id = :groupId',
        ExpressionAttributeValues: {
          ':groupId': groupId,
        },
      });

      // Mock the document client's send method to return the expected rules
      mockDocumentClient.send.mockImplementation((command) => {
        if (command instanceof QueryCommand) {
          return Promise.resolve({
            Items: [mockRules[0], mockRules[2]], // Rules with group1 owner_id
          });
        }
        return Promise.resolve({});
      });

      // Act
      const result = await repository.findRulesByGroupId(groupId);

      // Assert
      expect(mockDocumentClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            TableName: 'test-table',
            IndexName: 'OwnerIdIndex',
            KeyConditionExpression: 'owner_id = :groupId',
            ExpressionAttributeValues: {
              ':groupId': groupId,
            },
          }),
        }),
      );

      expect(result).toEqual([mockRules[0], mockRules[2]]);
    });

    it('should return an empty array if no rules found', async () => {
      // Arrange
      const groupId = 'non-existent-group';
      mockDocumentClient.send.mockImplementation((command) => {
        if (command instanceof QueryCommand) {
          return Promise.resolve({ Items: [] });
        }
        return Promise.resolve({});
      });

      // Act
      const result = await repository.findRulesByGroupId(groupId);

      // Assert
      expect(result).toEqual([]);
    });

    it('should handle errors', async () => {
      // Arrange
      const groupId = 'group1';
      const error = new Error('DynamoDB error');
      mockDocumentClient.send.mockImplementation((command) => {
        if (command instanceof QueryCommand) {
          return Promise.reject(error);
        }
        return Promise.resolve({});
      });

      // Act & Assert
      await expect(repository.findRulesByGroupId(groupId)).rejects.toThrow(
        error,
      );
    });
  });

  describe('resetGroupRulesToDraft', () => {
    beforeEach(() => {
      // Reset the update spy for each test
      jest
        .spyOn(repository, 'update')
        .mockImplementation((key, updatedRule) =>
          Promise.resolve(updatedRule as StoredRule),
        );
    });

    it('should reset all published rules for a group to draft status', async () => {
      // Arrange
      const groupId = 'group1';

      // Mock findPublishedRulesByGroupId to return rules
      jest
        .spyOn(repository as any, 'findPublishedRulesByGroupId')
        .mockResolvedValueOnce([
          mockRules[0], // Published rule in group1
          mockRules[2], // Published rule in group1
        ]);

      // Act
      const result = await repository.resetGroupRulesToDraft(groupId);

      // Assert
      expect(
        (repository as any).findPublishedRulesByGroupId,
      ).toHaveBeenCalledWith(groupId);
      expect(repository.update).toHaveBeenCalledTimes(2);

      // Check updates for each rule
      expect(repository.update).toHaveBeenCalledWith(
        { id: 'rule1' },
        expect.objectContaining({
          status: RuleStatus.DRAFT,
          owner_id: 'test-user', // creator becomes the owner
          group_name: null,
        }),
      );

      expect(repository.update).toHaveBeenCalledWith(
        { id: 'rule3' },
        expect.objectContaining({
          status: RuleStatus.DRAFT,
          owner_id: 'user2', // creator becomes the owner
          group_name: null,
        }),
      );

      expect(result).toBe(2); // Two rules successfully updated
    });

    it('should return 0 if no rules found for the group', async () => {
      // Arrange
      const groupId = 'empty-group';
      jest
        .spyOn(repository as any, 'findPublishedRulesByGroupId')
        .mockResolvedValueOnce([]);

      // Act
      const result = await repository.resetGroupRulesToDraft(groupId);

      // Assert
      expect(result).toBe(0);
      expect(repository.update).not.toHaveBeenCalled();
    });

    it('should count only successful updates', async () => {
      // Arrange
      const groupId = 'group1';

      jest
        .spyOn(repository as any, 'findPublishedRulesByGroupId')
        .mockResolvedValueOnce([
          mockRules[0], // Will succeed
          mockRules[2], // Will fail
        ]);

      // First update succeeds, second fails
      (repository.update as jest.Mock)
        .mockResolvedValueOnce(mockRules[0]) // First update succeeds
        .mockRejectedValueOnce(new Error('Update failed')); // Second update fails

      // Act
      const result = await repository.resetGroupRulesToDraft(groupId);

      // Assert
      expect(result).toBe(1); // Only one successful update
      expect(repository.update).toHaveBeenCalledTimes(2);
    });

    it('should handle errors from finding rules', async () => {
      // Arrange
      const groupId = 'error-group';
      const error = new Error('Find error');
      jest
        .spyOn(repository as any, 'findPublishedRulesByGroupId')
        .mockRejectedValueOnce(error);

      // Act & Assert
      await expect(repository.resetGroupRulesToDraft(groupId)).rejects.toThrow(
        error,
      );
      expect(repository.update).not.toHaveBeenCalled();
    });
  });

  describe('findAllRulesCreatedByUser', () => {
    it('should find all rules created by a user', async () => {
      // Arrange
      const userId = 'test-user';

      // Create mock rules created by the user
      const mockUserRules = [
        {
          ...mockRules[0],
          created_by: 'test-user',
          owner_id: 'group1', // Owned by a group
          status: RuleStatus.PUBLISHED,
        },
        {
          ...mockRules[2],
          created_by: 'test-user',
          owner_id: 'test-user', // Owned by the user
          status: RuleStatus.DRAFT,
        },
      ];

      // Mock the document client's send method to return the expected rules
      mockDocumentClient.send.mockImplementation((command) => {
        if (command instanceof ScanCommand) {
          return Promise.resolve({
            Items: mockUserRules,
          });
        }
        return Promise.resolve({});
      });

      // Act
      const result = await repository.findAllRulesCreatedByUser(userId);

      // Assert
      expect(mockDocumentClient.send).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            TableName: 'test-table',
            FilterExpression: '#created_by = :userId',
            ExpressionAttributeNames: {
              '#created_by': 'created_by',
            },
            ExpressionAttributeValues: {
              ':userId': userId,
            },
          }),
        }),
      );

      expect(result).toEqual(mockUserRules);
    });

    it('should return an empty array if no rules found', async () => {
      // Arrange
      const userId = 'user-with-no-rules';
      mockDocumentClient.send.mockImplementation((command) => {
        if (command instanceof ScanCommand) {
          return Promise.resolve({ Items: [] });
        }
        return Promise.resolve({});
      });

      // Act
      const result = await repository.findAllRulesCreatedByUser(userId);

      // Assert
      expect(result).toEqual([]);
    });

    it('should handle errors', async () => {
      // Arrange
      const userId = 'error-user';
      const error = new Error('DynamoDB error');
      mockDocumentClient.send.mockImplementation((command) => {
        if (command instanceof ScanCommand) {
          return Promise.reject(error);
        }
        return Promise.resolve({});
      });

      // Act & Assert
      await expect(
        repository.findAllRulesCreatedByUser(userId),
      ).rejects.toThrow(error);
    });
  });

  describe('updateMyRulesUsername', () => {
    it('should update contributor field for rules created by the user', async () => {
      // Arrange
      const userId = 'test-user';
      const newUsername = 'updated-username';
      const ruleIds = ['rule1', 'rule2', 'rule3'];

      // Mock the findRulesByCreatorAndIds method
      const matchingRules = [
        {
          ...mockRules[0],
          id: 'rule1',
          created_by: userId,
          contributor: 'old-username',
        },
        {
          ...mockRules[1],
          id: 'rule2',
          created_by: userId,
          contributor: 'old-username',
        },
      ];
      jest
        .spyOn(repository as any, 'findRulesByCreatorAndIds')
        .mockResolvedValueOnce(matchingRules);

      // Mock the batchUpdate method
      jest.spyOn(repository, 'batchUpdate').mockResolvedValueOnce({
        successful: matchingRules.map((rule) => ({
          ...rule,
          contributor: newUsername,
        })),
        failed: [],
      });

      // Act
      await repository.updateMyRulesUsername(userId, newUsername, ruleIds);

      // Assert
      expect(repository['findRulesByCreatorAndIds']).toHaveBeenCalledWith(
        userId,
        ruleIds,
      );
      expect(repository.batchUpdate).toHaveBeenCalledWith([
        {
          key: { id: 'rule1' },
          item: { contributor: newUsername },
        },
        {
          key: { id: 'rule2' },
          item: { contributor: newUsername },
        },
      ]);
    });

    it('should handle case when no rules match the criteria', async () => {
      // Arrange
      const userId = 'test-user';
      const newUsername = 'updated-username';
      const ruleIds = ['rule1', 'rule2'];

      // Mock the findRulesByCreatorAndIds method to return empty array
      jest
        .spyOn(repository as any, 'findRulesByCreatorAndIds')
        .mockResolvedValueOnce([]);

      // Mock the batchUpdate method
      jest
        .spyOn(repository, 'batchUpdate')
        .mockImplementation(() =>
          Promise.resolve({ successful: [], failed: [] }),
        );

      // Spy on the logger
      jest.spyOn(repository['logger'], 'log');

      // Act
      await repository.updateMyRulesUsername(userId, newUsername, ruleIds);

      // Assert
      expect(repository['findRulesByCreatorAndIds']).toHaveBeenCalledWith(
        userId,
        ruleIds,
      );
      expect(repository.batchUpdate).not.toHaveBeenCalled();
      expect(repository['logger'].log).toHaveBeenCalledWith(
        expect.stringContaining('No rules found for user'),
      );
    });

    it('should handle empty ruleIds array', async () => {
      // Arrange
      const userId = 'test-user';
      const newUsername = 'updated-username';
      const ruleIds: string[] = [];

      // Mock the findRulesByCreatorAndIds method
      jest
        .spyOn(repository as any, 'findRulesByCreatorAndIds')
        .mockImplementation(() => Promise.resolve([]));

      // Mock the batchUpdate method
      jest
        .spyOn(repository, 'batchUpdate')
        .mockImplementation(() =>
          Promise.resolve({ successful: [], failed: [] }),
        );

      // Spy on the logger
      jest.spyOn(repository['logger'], 'log');

      // Act
      await repository.updateMyRulesUsername(userId, newUsername, ruleIds);

      // Assert
      expect(repository['findRulesByCreatorAndIds']).not.toHaveBeenCalled();
      expect(repository.batchUpdate).not.toHaveBeenCalled();
      expect(repository['logger'].log).toHaveBeenCalledWith(
        expect.stringContaining('No rule IDs provided'),
      );
    });

    it('should process multiple batches for large number of rules', async () => {
      // Arrange
      const userId = 'test-user';
      const newUsername = 'updated-username';
      const ruleIds = Array.from({ length: 55 }, (_, i) => `rule${i}`);

      // Create 55 matching rules
      const matchingRules = Array.from({ length: 55 }, (_, i) => ({
        ...mockRules[0],
        id: `rule${i}`,
        created_by: userId,
        contributor: 'old-username',
      }));

      jest
        .spyOn(repository as any, 'findRulesByCreatorAndIds')
        .mockResolvedValueOnce(matchingRules);

      // Mock the batchUpdate method for first batch (25 items)
      jest
        .spyOn(repository, 'batchUpdate')
        .mockResolvedValueOnce({
          successful: matchingRules
            .slice(0, 25)
            .map((rule) => ({ ...rule, contributor: newUsername })),
          failed: [],
        })
        // Second batch (25 items)
        .mockResolvedValueOnce({
          successful: matchingRules
            .slice(25, 50)
            .map((rule) => ({ ...rule, contributor: newUsername })),
          failed: [],
        })
        // Third batch (5 items)
        .mockResolvedValueOnce({
          successful: matchingRules
            .slice(50)
            .map((rule) => ({ ...rule, contributor: newUsername })),
          failed: [],
        });

      // Act
      await repository.updateMyRulesUsername(userId, newUsername, ruleIds);

      // Assert
      expect(repository.batchUpdate).toHaveBeenCalledTimes(3);

      // Assert first batch call (items 0-24)
      expect(repository.batchUpdate).toHaveBeenNthCalledWith(
        1,
        matchingRules.slice(0, 25).map((rule) => ({
          key: { id: rule.id },
          item: { contributor: newUsername },
        })),
      );

      // Assert second batch call (items 25-49)
      expect(repository.batchUpdate).toHaveBeenNthCalledWith(
        2,
        matchingRules.slice(25, 50).map((rule) => ({
          key: { id: rule.id },
          item: { contributor: newUsername },
        })),
      );

      // Assert third batch call (items 50-54)
      expect(repository.batchUpdate).toHaveBeenNthCalledWith(
        3,
        matchingRules.slice(50).map((rule) => ({
          key: { id: rule.id },
          item: { contributor: newUsername },
        })),
      );
    });

    it('should handle partial failures in batch updates', async () => {
      // Arrange
      const userId = 'test-user';
      const newUsername = 'updated-username';
      const ruleIds = ['rule1', 'rule2', 'rule3'];

      const matchingRules = [
        {
          ...mockRules[0],
          id: 'rule1',
          created_by: userId,
          contributor: 'old-username',
        },
        {
          ...mockRules[1],
          id: 'rule2',
          created_by: userId,
          contributor: 'old-username',
        },
        {
          ...mockRules[2],
          id: 'rule3',
          created_by: userId,
          contributor: 'old-username',
        },
      ];

      jest
        .spyOn(repository as any, 'findRulesByCreatorAndIds')
        .mockResolvedValueOnce(matchingRules);

      // Mock the batchUpdate method to include a failure
      jest.spyOn(repository, 'batchUpdate').mockResolvedValueOnce({
        successful: [
          { ...matchingRules[0], contributor: newUsername },
          { ...matchingRules[2], contributor: newUsername },
        ],
        failed: [
          {
            item: {
              key: { id: 'rule2' },
              update: { contributor: newUsername },
            },
            reason: 'Failed to update item',
          },
        ],
      });

      // Spy on the logger
      jest.spyOn(repository['logger'], 'warn');
      jest.spyOn(repository['logger'], 'log');

      // Act
      await repository.updateMyRulesUsername(userId, newUsername, ruleIds);

      // Assert
      expect(repository.batchUpdate).toHaveBeenCalledTimes(1);
      expect(repository['logger'].warn).toHaveBeenCalledWith(
        expect.stringContaining('Failed to update some rules: rule2'),
      );
      expect(repository['logger'].log).toHaveBeenCalledWith(
        expect.stringContaining('Updated username for 2 rules (failed: 1)'),
      );
    });
  });

  describe('findRulesByCreatorAndIds', () => {
    it('should find rules by creator ID and rule IDs', async () => {
      // Arrange
      const userId = 'test-user';
      const ruleIds = ['rule1', 'rule2', 'rule3'];

      const allRules = [
        { ...mockRules[0], id: 'rule1', created_by: userId },
        { ...mockRules[1], id: 'rule2', created_by: 'other-user' },
        { ...mockRules[2], id: 'rule3', created_by: userId },
      ];

      jest.spyOn(repository, 'findByIds').mockResolvedValueOnce(allRules);

      // Act
      const result = await repository['findRulesByCreatorAndIds'](
        userId,
        ruleIds,
      );

      // Assert
      expect(repository.findByIds).toHaveBeenCalledWith(ruleIds);
      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('rule1');
      expect(result[1].id).toBe('rule3');
      expect(result.every((rule) => rule.created_by === userId)).toBe(true);
    });
  });
});
