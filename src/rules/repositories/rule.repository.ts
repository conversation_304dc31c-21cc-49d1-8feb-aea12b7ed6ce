import { Injectable } from '@nestjs/common';
import {
  QueryCommand as Document<PERSON>uery<PERSON>ommand,
  <PERSON>an<PERSON>ommand,
  BatchGetCommand,
} from '@aws-sdk/lib-dynamodb';
import { BaseRepository } from '../../dynamodb/repositories/repository.base';
import { RuleStatus, RuleOrderBy } from '../models/rule.model';
import { DynamoDBService } from '../../dynamodb/dynamodb.service';
import { RuleTableSchema, StoredRule } from '../schemas/rule.schema';

/**
 * Repository for Rule entity
 */
@Injectable()
export class RuleRepository extends BaseRepository<StoredRule> {
  constructor(dynamoDBService: DynamoDBService) {
    super(dynamoDBService, new RuleTableSchema());
  }

  /**
   * Find rules by user ID with optional status filtering
   * @param userId User ID to filter by
   * @param status Optional rule status filter
   * @param filterByRuleIds Optional list of rule IDs to filter by
   * @param ruleType Optional rule type filter
   * @param tag Optional tag filter
   * @param page Page number (default: 1)
   * @param size Items per page (default: 100)
   * @returns List of rules and the total count
   */
  async findByUserId(
    userId: string,
    filterByOwnerIds: string[],
    orderBy: RuleOrderBy.PUBLISHED_AT | RuleOrderBy.CREATED_AT,
    page: number = 1,
    limit: number = 100,
  ): Promise<{ items: StoredRule[]; total: number }> {
    let indexName = 'OwnerIdIndex';
    if (orderBy === RuleOrderBy.PUBLISHED_AT) {
      indexName = 'OwnerPublishedIndex';
    }

    // Query for all detections in groups the user follows
    const minimalRulesPromisesForUserId = filterByOwnerIds.map((groupId) => {
      return this.findMinimalRulesByOwnerId(
        groupId,
        indexName,
        [userId],
        limit * page,
      ); // Fetch enough to cover all pages up to the requested one
    });

    const minimalRulesArrays = await Promise.all(minimalRulesPromisesForUserId);

    // Combine all results
    const allMinimalItems = minimalRulesArrays.flat();
    const total = allMinimalItems.length;

    // Sort combined results based on orderBy
    allMinimalItems.sort((a, b) => {
      if (orderBy === RuleOrderBy.PUBLISHED_AT) {
        return (b.published_at || 0) - (a.published_at || 0);
      } else {
        return b.created_at - a.created_at;
      }
    });

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const paginatedMinimalItems = allMinimalItems.slice(
      startIndex,
      startIndex + limit,
    );

    // Fetch full details for the paginated items
    const paginatedIds = paginatedMinimalItems.map((item) => item.id);
    const fullItems = await this.fetchFullRuleDetails(paginatedIds);

    // Sort the full items in the same order as the paginated minimal items
    const sortedFullItems = paginatedIds
      .map((id) => fullItems.find((item) => item.id === id))
      .filter(Boolean) as StoredRule[];

    return {
      items: sortedFullItems,
      total,
    };
  }

  /**
   * Find rules by IDs preserving the order of IDs provided
   * @param ruleIds Array of rule IDs to find
   * @returns List of rules in the same order as the input IDs
   */
  async findByIds(ruleIds: string[]): Promise<StoredRule[]> {
    if (!ruleIds.length) {
      return [];
    }

    try {
      // Create keys for batch get
      const keys = ruleIds.map((id) => ({ id }));

      // Split into chunks of 100 (DynamoDB batch get limit)
      const chunkSize = 100;
      const chunks: Array<(typeof keys)[0][]> = [];
      for (let i = 0; i < keys.length; i += chunkSize) {
        chunks.push(keys.slice(i, i + chunkSize));
      }

      // Process each chunk
      let allItems: StoredRule[] = [];
      for (const chunk of chunks) {
        const batchResult = await this.batchGet(chunk);
        allItems = [...allItems, ...batchResult.successful];
      }

      // Create a map for quick lookup
      const ruleMap = new Map<string, StoredRule>();
      allItems.forEach((rule) => {
        ruleMap.set(rule.id, rule);
      });

      // Return rules in the same order as the input IDs
      return ruleIds
        .map((id) => ruleMap.get(id))
        .filter((rule) => rule !== undefined);
    } catch (error) {
      this.logger.error(
        `Failed to find rules by IDs. Error: ${error.message}`,
        error,
      );
      throw error;
    }
  }

  async incrementCounter(
    ruleId: string,
    counterName: string,
  ): Promise<StoredRule> {
    const updatedRule = await this.dynamoDBService.incrementCounter(
      this.tableName,
      ruleId,
      counterName,
    );
    return updatedRule as StoredRule;
  }

  async decrementCounter(
    ruleId: string,
    counterName: string,
  ): Promise<StoredRule> {
    const updatedRule = await this.dynamoDBService.decrementCounter(
      this.tableName,
      ruleId,
      counterName,
    );
    return updatedRule as StoredRule;
  }

  async findFollowedRules(
    followedGroupIds: string[],
    followedUserIds: string[],
    ownerIds: string[],
    page: number = 1,
    limit: number = 1000,
  ): Promise<{ items: StoredRule[]; total: number }> {
    const indexName = 'OwnerPublishedIndex';

    // Get set of group IDs that exist in followedGroupIds and in ownerIds
    const groupIdsToQuery = followedGroupIds.filter((groupId) =>
      ownerIds.includes(groupId),
    );

    // Get set of owner IDs that do not exist in followedGroupIds but exist in ownerIds
    const ownerIdsToQueryByFollowedUsers = ownerIds.filter(
      (ownerId) => !groupIdsToQuery.includes(ownerId),
    );

    // Query all groups the user can view rules for by the users they follow - exclude groups they follow as we will find these in the next query
    let minimalRulesPromisesForFollowedUsers: Promise<
      Pick<
        StoredRule,
        | 'id'
        | 'owner_id'
        | 'published_at'
        | 'created_at'
        | 'likes'
        | 'downloads'
      >[]
    >[] = [];
    if (followedUserIds.length > 0) {
      minimalRulesPromisesForFollowedUsers = ownerIdsToQueryByFollowedUsers.map(
        (ownerId) => {
          return this.findMinimalRulesByOwnerId(
            ownerId,
            indexName,
            followedUserIds,
            limit * page,
          ); // Fetch enough to cover all pages up to the requested one
        },
      );
    }

    // Query for all detections in groups the user follows
    const minimalRulesPromisesForFollowedGroups = groupIdsToQuery.map(
      (groupId) => {
        return this.findMinimalRulesByOwnerId(
          groupId,
          indexName,
          undefined,
          limit * page,
        ); // Fetch enough to cover all pages up to the requested one
      },
    );

    const allPromises = [
      ...minimalRulesPromisesForFollowedGroups,
      ...minimalRulesPromisesForFollowedUsers,
    ];
    const minimalRulesArrays = await Promise.all(allPromises);

    // Combine all results and deduplicate by id
    const allMinimalItems = minimalRulesArrays.flat().reduce(
      (acc, item) => {
        if (!acc.some((i) => i.id === item.id)) {
          acc.push(item);
        }
        return acc;
      },
      [] as Pick<
        StoredRule,
        | 'id'
        | 'owner_id'
        | 'published_at'
        | 'created_at'
        | 'likes'
        | 'downloads'
      >[],
    );

    const total = allMinimalItems.length;

    // Sort combined results based on orderBy
    allMinimalItems.sort(
      (a, b) => (b.published_at || 0) - (a.published_at || 0),
    );

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const paginatedMinimalItems = allMinimalItems.slice(
      startIndex,
      startIndex + limit,
    );

    // Fetch full details for the paginated items
    const paginatedIds = paginatedMinimalItems.map((item) => item.id);
    const fullItems = await this.fetchFullRuleDetails(paginatedIds);

    // Sort the full items in the same order as the paginated minimal items
    const sortedFullItems = paginatedIds
      .map((id) => fullItems.find((item) => item.id === id))
      .filter(Boolean) as StoredRule[];

    return {
      items: sortedFullItems,
      total,
    };
  }

  // Private helper method to query minimal rule data for a single owner ID
  private async findMinimalRulesByOwnerId(
    ownerId: string,
    indexName: string,
    createdByUserIds?: string[],
    limit: number = 1000,
  ): Promise<
    Pick<
      StoredRule,
      'id' | 'owner_id' | 'published_at' | 'created_at' | 'likes' | 'downloads'
    >[]
  > {
    const queryParams: any = {
      TableName: this.tableName,
      IndexName: indexName,
      KeyConditionExpression: 'owner_id = :ownerId',
      ExpressionAttributeValues: {
        ':ownerId': ownerId,
      },
      ScanIndexForward: false, // Sort in descending order
      Limit: limit,
      ProjectionExpression:
        'id, owner_id, published_at, created_at, likes, downloads', // Only fetch minimal fields
    };

    if (createdByUserIds) {
      // construct a string of OR conditions for the created_by field
      const createdByConditions = createdByUserIds
        .map((_, index) => `created_by = :createdBy${index}`)
        .join(' OR ');
      if (queryParams.FilterExpression) {
        queryParams.FilterExpression += ` AND (${createdByConditions})`;
      } else {
        queryParams.FilterExpression = createdByConditions;
      }
      createdByUserIds.forEach((userId, index) => {
        queryParams.ExpressionAttributeValues[`:createdBy${index}`] = userId;
      });
    }

    const command = new DocumentQueryCommand(queryParams);
    const result = await this.getDocumentClient().send(command);

    return (result.Items || []) as Pick<
      StoredRule,
      'id' | 'owner_id' | 'published_at' | 'created_at' | 'likes' | 'downloads'
    >[];
  }

  // Method to fetch full details for a list of rule IDs
  private async fetchFullRuleDetails(ruleIds: string[]): Promise<StoredRule[]> {
    if (ruleIds.length === 0) {
      return [];
    }

    // Use BatchGetItem for efficiency
    const batchGetParams = {
      RequestItems: {
        [this.tableName]: {
          Keys: ruleIds.map((id) => ({ id })),
        },
      },
    };

    const result = await this.getDocumentClient().send(
      new BatchGetCommand(batchGetParams),
    );

    return (result.Responses?.[this.tableName] || []) as StoredRule[];
  }

  /**
   * Find published rules by group ID and reset them to draft status
   * @param groupId Group ID to find rules for
   * @returns Number of rules reset to draft status
   */
  async resetGroupRulesToDraft(groupId: string): Promise<number> {
    try {
      // Find all published rules for the group
      const publishedRules = await this.findPublishedRulesByGroupId(groupId);

      if (publishedRules.length === 0) {
        return 0;
      }

      // Process all updates in parallel
      const updatePromises = publishedRules.map((rule) =>
        this.update(
          { id: rule.id },
          {
            ...rule,
            status: RuleStatus.DRAFT,
            owner_id: rule.created_by, // Set owner_id to created_by
            group_name: null, // Remove group_name
            updated_at: Date.now(),
          },
        ),
      );

      // Wait for all updates to complete
      const results = await Promise.allSettled(updatePromises);

      // Count successful updates
      const successCount = results.filter(
        (r) => r.status === 'fulfilled',
      ).length;

      return successCount;
    } catch (error) {
      this.logger.error(
        `Failed to reset group rules to draft: ${error.message}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Find published rules by group ID
   * @param groupId Group ID to find rules for
   * @returns Array of published rules for the group
   */
  private async findPublishedRulesByGroupId(
    groupId: string,
  ): Promise<StoredRule[]> {
    try {
      const queryParams = {
        TableName: this.tableName,
        IndexName: 'OwnerIdIndex',
        KeyConditionExpression: 'owner_id = :groupId',
        ExpressionAttributeValues: {
          ':groupId': groupId,
        },
      };

      const command = new DocumentQueryCommand(queryParams);
      const result = await this.getDocumentClient().send(command);
      return (result.Items || []) as StoredRule[];
    } catch (error) {
      this.logger.error(
        `Failed to find published rules by group ID ${groupId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Find rules by group ID
   * @param groupId Group ID to find rules for
   * @returns Array of rules for the group
   */
  async findRulesByGroupId(groupId: string): Promise<StoredRule[]> {
    try {
      const queryParams = {
        TableName: this.tableName,
        IndexName: 'OwnerIdIndex',
        KeyConditionExpression: 'owner_id = :groupId',
        ExpressionAttributeValues: {
          ':groupId': groupId,
        },
      };

      const command = new DocumentQueryCommand(queryParams);
      const result = await this.getDocumentClient().send(command);
      return (result.Items || []) as StoredRule[];
    } catch (error) {
      this.logger.error(`Failed to find rules by group ID ${groupId}`, error);
      throw error;
    }
  }

  /**
   * Find published rules created by a user but owned by groups
   * @param userId User ID to find rules for
   * @returns Array of published rules created by the user but owned by groups
   */
  async findAllRulesCreatedByUser(userId: string): Promise<StoredRule[]> {
    try {
      const scanParams = {
        TableName: this.tableName,
        FilterExpression: '#created_by = :userId',
        ExpressionAttributeNames: {
          '#created_by': 'created_by',
        },
        ExpressionAttributeValues: {
          ':userId': userId,
        },
      };

      const command = new ScanCommand(scanParams);
      const result = await this.getDocumentClient().send(command);
      return (result.Items || []) as StoredRule[];
    } catch (error) {
      this.logger.error(
        `Failed to find published rules by user ID ${userId} in groups`,
        error,
      );
      throw error;
    }
  }

  async updateMyRulesUsername(
    userId: string,
    username: string,
    allowedRuleIds: string[],
  ): Promise<void> {
    try {
      if (!allowedRuleIds.length) {
        this.logger.log(`No rule IDs provided for user ${userId}`);
        return;
      }

      // Find all rules that match both criteria:
      // 1. Rule ID is in the provided allowedRuleIds
      // 2. created_by equals the provided userId
      const matchingRules = await this.findRulesByCreatorAndIds(
        userId,
        allowedRuleIds,
      );

      if (matchingRules.length === 0) {
        this.logger.log(
          `No rules found for user ${userId} within the provided rule IDs`,
        );
        return;
      }

      // Prepare update items - only update the contributor field
      const updateItems = matchingRules.map((rule) => ({
        key: { id: rule.id },
        item: { contributor: username },
      }));

      // Process updates in batches (DynamoDB has a limit of 25 items per batch operation)
      const batchSize = 25;
      let updatedCount = 0;
      let failedCount = 0;

      for (let i = 0; i < updateItems.length; i += batchSize) {
        const batch = updateItems.slice(i, i + batchSize);
        const result = await this.batchUpdate(batch);

        updatedCount += result.successful.length;
        failedCount += result.failed.length;

        if (result.failed.length > 0) {
          const failedIds = result.failed.map((failure) =>
            typeof failure.item === 'object' && failure.item.key
              ? failure.item.key.id
              : 'unknown',
          );
          this.logger.warn(
            `Failed to update some rules: ${failedIds.join(', ')}`,
          );
        }
      }

      this.logger.log(
        `Updated username for ${updatedCount} rules (failed: ${failedCount}) for user ${userId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to update rules username for user ${userId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Find rules that match both a creator ID and are within a list of rule IDs
   * @param creatorId The user ID who created the rules
   * @param ruleIds List of rule IDs to filter by
   * @returns List of matching rules
   */
  private async findRulesByCreatorAndIds(
    creatorId: string,
    ruleIds: string[],
  ): Promise<StoredRule[]> {
    try {
      // First get all the rules by IDs
      const rules = await this.findByIds(ruleIds);

      // Then filter to only those created by the user
      return rules.filter((rule) => rule.created_by === creatorId);
    } catch (error) {
      this.logger.error(
        `Failed to find rules by creator ID ${creatorId} and rule IDs`,
        error,
      );
      throw error;
    }
  }
}
