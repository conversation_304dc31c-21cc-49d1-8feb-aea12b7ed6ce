import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  OpenFgaClient,
  CredentialsMethod,
  ClientBatchCheckResponse,
  FgaApiValidationError,
  FgaApiAuthenticationError,
} from '@openfga/sdk';
import { FGAType, FGARelation } from './fga.enums';
import { RedisCacheService } from '../../redis-cache/redis-cache.service';
import * as retryModule from 'async-retry';
const retry = retryModule.default || retryModule;

export interface FGATuple {
  user_type: FGAType;
  user_id: string;
  relation: FGARelation;
  object_type: FGAType;
  object_id: string;
}

export interface TupleBatchOperationResult {
  success: boolean;
  failedTuples: FGATuple[];
  shouldRetrySingleWrites: boolean;
}

enum FGAOperationType {
  WRITE = 'writes',
  DELETE = 'deletes',
}

const NON_RETRYABLE_ERRORS = [FgaApiAuthenticationError, FgaApiValidationError];

@Injectable()
export class FgaService {
  private fgaClient: OpenFgaClient;
  private workspaceId: string;
  private logger: Logger;
  private cacheTtl: number;
  private cacheEnabled: boolean;
  private cacheListTtl: number;
  private retryOptions: retryModule.Options;
  private writeBatchSize: number;
  private checkBatchSize: number;
  private retryTimes: number;
  private retryWaitTimeMs: number;
  private readonly fgaDatabase: number;

  constructor(
    private configService: ConfigService,
    private redisCacheService: RedisCacheService,
  ) {
    this.logger = new Logger(FgaService.name);
    this.workspaceId =
      this.configService.getOrThrow<string>('FGA_WORKSPACE_ID');
    this.fgaDatabase = this.configService.get<number>('REDIS_FGA_DB', 0);
    this.fgaClient = new OpenFgaClient({
      apiUrl: this.configService.getOrThrow<string>('FGA_API_URL'),
      storeId: this.configService.getOrThrow<string>('FGA_STORE_ID'),
      credentials: {
        method: CredentialsMethod.ClientCredentials,
        config: {
          apiTokenIssuer: this.configService.getOrThrow<string>(
            'FGA_API_TOKEN_ISSUER',
          ),
          apiAudience:
            this.configService.getOrThrow<string>('FGA_API_AUDIENCE'),
          clientId: this.configService.getOrThrow<string>('FGA_CLIENT_ID'),
          clientSecret:
            this.configService.getOrThrow<string>('FGA_CLIENT_SECRET'),
        },
      },
    });

    // Get cache configuration with defaults
    this.cacheTtl = this.configService.get<number>('FGA_CACHE_TTL', 900); // 900 seconds = 15 minutes
    this.cacheListTtl = this.configService.get<number>(
      'FGA_CACHE_LIST_TTL',
      900,
    ); // 900 seconds = 15 minutes
    this.cacheEnabled = this.configService.get<boolean>(
      'FGA_CACHE_ENABLED',
      true,
    );
    this.retryTimes = this.configService.get<number>('FGA_MAX_RETRIES', 3);

    // Configure retry options
    this.retryOptions = {
      retries: this.retryTimes,
      minTimeout: this.configService.get<number>(
        'FGA_INITIAL_BACKOFF_MS',
        3000,
      ),
      factor: this.configService.get<number>('FGA_BACKOFF_MULTIPLIER', 1),
      randomize: false,
      onRetry: (error, attempt) => {
        this.logger.warn(
          `Retry attempt ${attempt} for FGA operation due to error: ${error.message}`,
        );
      },
    };

    this.writeBatchSize = this.configService.get<number>(
      'FGA_WRITE_BATCH_SIZE',
      100,
    );

    this.checkBatchSize = this.configService.get<number>(
      'FGA_CHECK_BATCH_SIZE',
      100,
    );

    this.retryWaitTimeMs = 1000;

    this.logger.log(
      `FGA cache initialized with TTL: ${this.cacheTtl}s, enabled: ${this.cacheEnabled}`,
    );
    this.logger.log(
      `FGA retry configuration: retries=${this.retryOptions.retries}, minTimeout=${this.retryOptions.minTimeout}ms, factor=${this.retryOptions.factor}`,
    );
  }

  clearCache() {
    if (this.cacheEnabled) {
      this.logger.log('Clearing FGA cache');
      this.redisCacheService.clear({ database: this.fgaDatabase });
      this.logger.log('FGA cache cleared');
    } else {
      this.logger.log('FGA cache is not enabled. Ignoring clear cache request');
    }
  }

  async checkUserAuthorization(
    tuple: FGATuple,
    ignoreCache: boolean = false,
  ): Promise<boolean> {
    try {
      this.logger.log(
        `Checking authorization for ${tuple.user_type}:${tuple.user_id} ${tuple.relation} ${tuple.object_type}:${tuple.object_id}`,
      );

      if (this.cacheEnabled && !ignoreCache) {
        const cachedResult = await this.checkCacheForTuple(tuple);
        if (cachedResult) {
          return cachedResult;
        }
      }

      const result = await this.checkTuple(tuple);
      this.logger.log(`Authorization check result: ${result}`);

      if (this.cacheEnabled) {
        this.addTupleToCache(tuple, result);
      }

      return result;
    } catch (error) {
      this.logger.error(
        `FGA check failed for tuple: ${tuple.user_type}:${tuple.user_id} ${tuple.relation} ${tuple.object_type}:${tuple.object_id}:`,
        error,
      );
      throw new InternalServerErrorException('Failed to Authorize');
    }
  }

  async checkUserAuthorizationOnWorkspace(
    user_type: FGAType,
    user_id: string,
    workspace_relation: FGARelation,
  ): Promise<boolean> {
    const tuple = {
      user_type,
      user_id,
      relation: workspace_relation,
      object_type: FGAType.WORKSPACE,
      object_id: this.workspaceId,
    };
    return this.checkUserAuthorization(tuple);
  }

  async deleteTuple(tuple: FGATuple): Promise<void> {
    await this.deleteTuples([tuple]);
  }

  async writeTuple(tuple: FGATuple): Promise<void> {
    await this.writeTuples([tuple]);
  }

  async writeTuples(tuples: FGATuple[]): Promise<void> {
    await this.writeTuplesInBatches(tuples, FGAOperationType.WRITE);
  }

  async deleteTuples(tuples: FGATuple[]): Promise<void> {
    await this.writeTuplesInBatches(tuples, FGAOperationType.DELETE);
  }

  private async writeTuplesInBatches(
    tuples: FGATuple[],
    operationType: FGAOperationType,
    isRollback: boolean = false,
  ): Promise<void> {
    const writtenTuples: FGATuple[] = [];

    const batchSize = this.writeBatchSize;
    for (let i = 0; i < tuples.length; i += batchSize) {
      const batch = tuples.slice(i, i + batchSize);
      try {
        await this.writeTuplesInBatchWithRetry(batch, operationType);
        writtenTuples.push(...batch);
        this.logger.log(
          `FGA ${operationType} tuples succeeded for tuples: ${batch.map((tuple) => `${tuple.user_type}:${tuple.user_id} ${tuple.relation} ${tuple.object_type}:${tuple.object_id}`).join(', ')}`,
        );
      } catch (error) {
        this.logger.error(
          `FGA ${isRollback ? 'rollback' : ''} ${operationType} tuples failed for tuples: ${batch.map((tuple) => `${tuple.user_type}:${tuple.user_id} ${tuple.relation} ${tuple.object_type}:${tuple.object_id}`).join(', ')}:`,
          error,
        );
        if (isRollback) {
          // Already trying to rollback so we throw error instead of retrying
          throw error;
        }

        // Attempt to rollback the written/deleted tuples
        const rollbackOperationType =
          operationType === FGAOperationType.WRITE
            ? FGAOperationType.DELETE
            : FGAOperationType.WRITE;
        await this.writeTuplesInBatches(
          writtenTuples,
          rollbackOperationType,
          true,
        );

        throw error;
      }

      // Sleep for 1 second to avoid rate limiting if there are more tuples to write
      if (i < tuples.length - 1) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    }

    if (this.cacheEnabled) {
      this.redisCacheService.clear({ database: this.fgaDatabase });
    }
  }

  private async writeTuplesInBatchWithRetry(
    tuples: FGATuple[],
    operationType: FGAOperationType,
    retryCount: number = 0,
  ): Promise<void> {
    try {
      await this.fgaClient.write({
        [operationType]: tuples.map((tuple) => ({
          user: `${tuple.user_type}:${tuple.user_id}`,
          relation: tuple.relation,
          object: `${tuple.object_type}:${tuple.object_id}`,
        })),
      });
    } catch (error) {
      const tupleStateAlreadyExistsErrorStartsWith =
        operationType === FGAOperationType.WRITE
          ? 'cannot write a tuple which already exists:'
          : 'cannot delete a tuple which does not exist:';
      if (
        error instanceof FgaApiValidationError &&
        error.apiErrorMessage?.startsWith(
          tupleStateAlreadyExistsErrorStartsWith,
        )
      ) {
        for (const tuple of tuples) {
          this.logger.log(
            `Due to tuple state already exists in batch, retrying ${operationType} tuple: ${tuple.user_type}:${tuple.user_id} ${tuple.relation} ${tuple.object_type}:${tuple.object_id}`,
          );
          await this.writeSingleTupleWithRetry(tuple, operationType);
        }
        return;
      }

      if (retryCount == this.retryTimes) {
        this.logger.error(
          `FGA ${operationType} tuples failed (retries exhausted) for tuples: ${tuples.map((tuple) => `${tuple.user_type}:${tuple.user_id} ${tuple.relation} ${tuple.object_type}:${tuple.object_id}`).join(', ')}:`,
          error,
        );
        throw new InternalServerErrorException('Failed to Complete Operation');
      }

      retryCount++;
      this.logger.warn(
        `FGA ${operationType} tuples (retry ${retryCount} of ${this.retryTimes}) failed for tuples: ${tuples.map((tuple) => `${tuple.user_type}:${tuple.user_id} ${tuple.relation} ${tuple.object_type}:${tuple.object_id}`).join(', ')}:`,
        error,
      );
      // Wait for 1 second to avoid rate limiting
      await new Promise((resolve) => setTimeout(resolve, this.retryWaitTimeMs));
      await this.writeTuplesInBatchWithRetry(tuples, operationType, retryCount);
    }
  }

  private async writeSingleTupleWithRetry(
    tuple: FGATuple,
    operationType: FGAOperationType,
    retryCount: number = 0,
  ): Promise<void> {
    try {
      if (operationType === FGAOperationType.WRITE) {
        await this.fgaClient.writeTuples([
          {
            user: `${tuple.user_type}:${tuple.user_id}`,
            relation: tuple.relation,
            object: `${tuple.object_type}:${tuple.object_id}`,
          },
        ]);
      } else if (operationType === FGAOperationType.DELETE) {
        await this.fgaClient.deleteTuples([
          {
            user: `${tuple.user_type}:${tuple.user_id}`,
            relation: tuple.relation,
            object: `${tuple.object_type}:${tuple.object_id}`,
          },
        ]);
      }

      this.logger.log(
        `FGA ${operationType} tuple succeeded: ${tuple.user_type}:${tuple.user_id} ${tuple.relation} ${tuple.object_type}:${tuple.object_id}`,
      );
    } catch (error) {
      const tupleStateAlreadyExistsErrorStartsWith =
        operationType === FGAOperationType.WRITE
          ? 'cannot write a tuple which already exists:'
          : 'cannot delete a tuple which does not exist:';
      if (
        error instanceof FgaApiValidationError &&
        error.apiErrorMessage?.startsWith(
          tupleStateAlreadyExistsErrorStartsWith,
        )
      ) {
        this.logger.log(
          `FGA ${operationType} tuple already exists: ${tuple.user_type}:${tuple.user_id} ${tuple.relation} ${tuple.object_type}:${tuple.object_id}`,
        );
        return;
      }
      if (retryCount == this.retryTimes) {
        this.logger.error(
          `FGA ${operationType} tuple failed (retries exhausted) for tuple: ${tuple.user_type}:${tuple.user_id} ${tuple.relation} ${tuple.object_type}:${tuple.object_id}:`,
          error,
        );
        throw new InternalServerErrorException('Failed to Complete Operation');
      }

      retryCount++;
      this.logger.warn(
        `FGA ${operationType} tuple failed (retry ${retryCount} of ${this.retryTimes}) for tuple: ${tuple.user_type}:${tuple.user_id} ${tuple.relation} ${tuple.object_type}:${tuple.object_id}:`,
        error,
      );
      await new Promise((resolve) => setTimeout(resolve, this.retryWaitTimeMs));
      await this.writeSingleTupleWithRetry(tuple, operationType, retryCount);
    }
  }

  async batchCheckTuples(tuples: FGATuple[]): Promise<boolean> {
    return this.batchCheckTuplesInBatches(tuples);
  }

  private async batchCheckTuplesInBatches(
    tuples: FGATuple[],
  ): Promise<boolean> {
    const batchSize = this.checkBatchSize;
    for (let i = 0; i < tuples.length; i += batchSize) {
      const batch = tuples.slice(i, i + batchSize);
      const result = await this.batchCheckTuplesBatch(batch);
      if (!result) {
        return false;
      }
      // Sleep for 1 second to avoid rate limiting
      if (i < tuples.length - 1) {
        await new Promise((resolve) =>
          setTimeout(resolve, this.retryWaitTimeMs),
        );
      }
    }
    return true;
  }

  private async batchCheckTuplesBatch(tuples: FGATuple[]): Promise<boolean> {
    this.logger.log(
      `Checking tuples: ${tuples.map((tuple) => `${tuple.user_type}:${tuple.user_id} ${tuple.relation} ${tuple.object_type}:${tuple.object_id}`).join(', ')}`,
    );

    try {
      // Perform batch check with retry
      const response: ClientBatchCheckResponse = await retry(async (bail) => {
        try {
          return await this.fgaClient.batchCheck({
            checks: tuples.map((tuple) => ({
              user: `${tuple.user_type}:${tuple.user_id}`,
              relation: tuple.relation,
              object: `${tuple.object_type}:${tuple.object_id}`,
            })),
          });
        } catch (error) {
          // If it's a non-retryable error, bail
          if (
            NON_RETRYABLE_ERRORS.some((errorType) => error instanceof errorType)
          ) {
            bail(error);
            return null; // This is never reached but needed for TypeScript
          }
          throw error; // Retryable error
        }
      }, this.retryOptions);

      // Return false if any tuple is not allowed
      const result = response.result.map((result) => result.allowed ?? false);
      this.logger.log(`Tuple check results: ${result.join(', ')}`);
      return result.every(Boolean);
    } catch (error) {
      this.logger.error(
        `FGA check tuples failed for tuples: ${tuples.map((tuple) => `${tuple.user_type}:${tuple.user_id} ${tuple.relation} ${tuple.object_type}:${tuple.object_id}`).join(', ')}:`,
        error,
      );
      throw new InternalServerErrorException('Failed to Authorize');
    }
  }

  async getAccessibleResourceIds(
    userType: FGAType,
    userId: string,
    relation: FGARelation,
    objectType: FGAType,
  ): Promise<string[]> {
    this.logger.log(
      `Getting accessible resource ids for ${userType}:${userId} ${relation} ${objectType}`,
    );

    const cacheKey = this.generateAccessibleResourceIdsCacheKey(
      userType,
      userId,
      relation,
      objectType,
    );
    if (this.cacheEnabled) {
      const cachedResult = await this.redisCacheService.get<string[]>(
        cacheKey,
        { database: this.fgaDatabase },
      );
      if (cachedResult) {
        this.logger.debug(
          `Cache hit for accessible resource ids: ${cacheKey}, count: ${cachedResult.length}`,
        );
        return cachedResult;
      }
      this.logger.debug(`Cache miss for accessible resource ids: ${cacheKey}`);
    }

    let result: string[] = [];
    try {
      // Perform listObjects with retry
      const response = await retry(async (bail) => {
        try {
          return await this.fgaClient.listObjects({
            user: `${userType}:${userId}`,
            relation: relation,
            type: objectType,
          });
        } catch (error) {
          // If it's a non-retryable error, bail
          if (
            NON_RETRYABLE_ERRORS.some((errorType) => error instanceof errorType)
          ) {
            bail(error);
            return null; // This is never reached but needed for TypeScript
          }
          throw error; // Retryable error
        }
      }, this.retryOptions);

      // Response format ["document:123", "document:456", "document:789"]
      result = response.objects.map((object) => object.split(':')[1]);
    } catch (error) {
      this.logger.error(
        `FGA get accessible resource ids failed for ${userType}:${userId} ${relation} ${objectType}:`,
        error,
      );
      throw new InternalServerErrorException('Failed to Authorize');
    }

    // Cache the result if enabled
    if (this.cacheEnabled) {
      await this.redisCacheService.set(cacheKey, result, {
        database: this.fgaDatabase,
        ttlSeconds: this.cacheListTtl,
      });
      this.logger.debug(
        `Cached accessible resource ids: ${cacheKey}, count: ${result.length}, TTL: ${this.cacheListTtl}s`,
      );
    }

    return result;
  }

  private async checkTuple(tuple: FGATuple): Promise<boolean> {
    try {
      // Perform check with retry
      const { allowed } = await retry(async (bail) => {
        try {
          return await this.fgaClient.check({
            user: `${tuple.user_type}:${tuple.user_id}`,
            relation: tuple.relation,
            object: `${tuple.object_type}:${tuple.object_id}`,
          });
        } catch (error) {
          // If it's a non-retryable error, bail
          if (
            NON_RETRYABLE_ERRORS.some((errorType) => error instanceof errorType)
          ) {
            this.logger.warn(
              `FGA check tuples failed, won't retry for tuple: ${tuple.user_type}:${tuple.user_id} ${tuple.relation} ${tuple.object_type}:${tuple.object_id}:`,
              error,
            );
            bail(error);
            return null; // This is never reached but needed for TypeScript
          }
          this.logger.warn(
            `FGA check tuples failed, will retry for tuple: ${tuple.user_type}:${tuple.user_id} ${tuple.relation} ${tuple.object_type}:${tuple.object_id}:`,
            error,
          );
          throw error; // Retryable error
        }
      }, this.retryOptions);

      return allowed ?? false;
    } catch (error) {
      this.logger.error(
        `FGA check tuples failed for tuple: ${tuple.user_type}:${tuple.user_id} ${tuple.relation} ${tuple.object_type}:${tuple.object_id}:`,
        error,
      );
      throw new InternalServerErrorException('Failed to Authorize');
    }
  }

  private generateCacheKey(tuple: FGATuple): string {
    return `fga:check:${tuple.user_type}:${tuple.user_id}:${tuple.relation}:${tuple.object_type}:${tuple.object_id}`;
  }

  private generateAccessibleResourceIdsCacheKey(
    userType: FGAType,
    userId: string,
    relation: FGARelation,
    objectType: FGAType,
  ): string {
    return `fga:list:${userType}:${userId}:${relation}:${objectType}`;
  }

  private async checkCacheForTuple(tuple: FGATuple): Promise<boolean | null> {
    const cacheKey = this.generateCacheKey(tuple);
    const cachedResult = await this.redisCacheService.get<boolean>(cacheKey, {
      database: this.fgaDatabase,
    });
    if (cachedResult) {
      this.logger.debug(
        `Cache hit for tuple check: ${cacheKey}, result: ${cachedResult}`,
      );
      return cachedResult;
    }
    this.logger.debug(`Cache miss for tuple check: ${cacheKey}`);
    return null;
  }

  private async addTupleToCache(
    tuple: FGATuple,
    result: boolean,
  ): Promise<void> {
    const cacheKey = this.generateCacheKey(tuple);
    await this.redisCacheService.set(cacheKey, result, {
      database: this.fgaDatabase,
      ttlSeconds: this.cacheTtl,
    });
    this.logger.debug(
      `Cached tuple check result: ${cacheKey}, result: ${result}, TTL: ${this.cacheTtl}s`,
    );
  }
}
