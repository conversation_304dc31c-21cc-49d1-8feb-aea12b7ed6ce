import { Test, TestingModule } from '@nestjs/testing';
import { FgaController } from './fga.controller';
import { FgaService } from './fga.service';
import { ConfigService } from '@nestjs/config';
import * as configHelpers from '../../common/config-helpers';

// Mock dependencies
const mockFgaService = {
  clearCache: jest.fn(),
};

const mockConfigService = {
  get: jest.fn(),
  getOrThrow: jest.fn(),
};

// Mock config helpers
jest.mock('../../common/config-helpers', () => ({
  setConfigService: jest.fn(),
  getWorkspaceId: jest.fn().mockReturnValue('test-workspace-id'),
}));

describe('FgaController', () => {
  let controller: FgaController;
  let fgaService: FgaService;

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      controllers: [FgaController],
      providers: [
        {
          provide: FgaService,
          useValue: mockFgaService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    controller = module.get<FgaController>(FgaController);
    fgaService = module.get<FgaService>(FgaService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('onModuleInit', () => {
    it('should set the config service', () => {
      controller.onModuleInit();
      expect(configHelpers.setConfigService).toHaveBeenCalledWith(
        mockConfigService,
      );
    });
  });

  describe('clearCache', () => {
    it('should call fgaService.clearCache', () => {
      // Mock request object with user info
      const mockRequest = {
        user: {
          sub: 'test-user-id',
        },
      };

      controller.clearCache(mockRequest);

      // Verify service was called
      expect(mockFgaService.clearCache).toHaveBeenCalledTimes(1);
    });

    it('should handle undefined user in request', () => {
      // Mock request object without user info
      const mockRequest = {};

      controller.clearCache(mockRequest);

      // Verify service was called
      expect(mockFgaService.clearCache).toHaveBeenCalledTimes(1);
    });

    it('should handle errors from fgaService.clearCache', () => {
      // Mock the service to throw an error
      mockFgaService.clearCache.mockImplementation(() => {
        throw new Error('Test error');
      });

      // Mock request object
      const mockRequest = {
        user: {
          sub: 'test-user-id',
        },
      };

      // The controller should propagate the error
      expect(() => controller.clearCache(mockRequest)).toThrow('Test error');
      expect(mockFgaService.clearCache).toHaveBeenCalledTimes(1);
    });
  });
});
