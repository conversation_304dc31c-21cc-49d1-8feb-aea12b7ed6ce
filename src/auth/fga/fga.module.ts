import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { FgaService } from './fga.service';
import { RedisCacheModule } from '../../redis-cache/redis-cache.module';
import { FgaController } from './fga.controller';

@Module({
  imports: [ConfigModule, RedisCacheModule],
  controllers: [FgaController],
  providers: [FgaService],
  exports: [FgaService],
})
export class FgaModule {}
