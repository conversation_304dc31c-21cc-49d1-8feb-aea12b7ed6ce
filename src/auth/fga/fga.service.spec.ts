import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { OpenFgaClient, CredentialsMethod } from '@openfga/sdk';
import { FgaService } from './fga.service';
import { FGAType, FGARelation, UserRole } from './fga.enums';
import { RedisCacheService } from '../../redis-cache/redis-cache.service';
import * as retryModule from 'async-retry';
import { AxiosError, AxiosHeaders } from 'axios';

// Mock OpenFgaClient
const mockFgaClient = {
  check: jest.fn(),
  write: jest.fn(),
  listObjects: jest.fn(),
  batchCheck: jest.fn(),
  writeTuples: jest.fn(),
  deleteTuples: jest.fn(),
};

// Mock ConfigService
const mockConfigService = {
  getOrThrow: jest.fn((key: string) => {
    const config = {
      FGA_WORKSPACE_ID: 'test-workspace',
      FGA_API_URL: 'http://fga.test',
      FGA_STORE_ID: 'test-store',
      FGA_API_TOKEN_ISSUER: 'test-issuer',
      FGA_API_AUDIENCE: 'test-audience',
      FGA_CLIENT_ID: 'test-client',
      FGA_CLIENT_SECRET: 'test-secret',
    };
    return config[key];
  }),
  get: jest.fn((key: string, defaultValue: any) => {
    const config = {
      FGA_CACHE_TTL: 900,
      FGA_CACHE_ENABLED: true,
      FGA_MAX_RETRIES: 3,
      FGA_INITIAL_BACKOFF_MS: 3000,
      FGA_BACKOFF_MULTIPLIER: 1,
      REDIS_FGA_DB: 0,
    };
    return config[key] !== undefined ? config[key] : defaultValue;
  }),
};

// Mock Redis Cache Service
const mockRedisCacheService = {
  get: jest.fn(),
  set: jest.fn(),
  del: jest.fn().mockImplementation(() => Promise.resolve()),
  clear: jest.fn(),
  ping: jest.fn(),
  getConnectionStatus: jest.fn().mockReturnValue(true),
};

// Mock retry function
jest.mock('async-retry', () => {
  return {
    __esModule: true,
    default: jest.fn((fn, options) => fn()),
  };
});

// Mock setTimeout to execute immediately in tests
jest.mock('timers', () => ({
  setTimeout: (callback) => callback(),
}));

jest.useFakeTimers();

// Mock AxiosError for FgaApiValidationError
const mockAxiosError: AxiosError = {
  config: {
    headers: new AxiosHeaders({
      'Content-Type': 'application/json',
    }),
  },
  isAxiosError: true,
  name: 'AxiosError',
  message: 'Mock Axios Error',
  toJSON: () => ({}),
  response: {
    status: 400,
    statusText: 'Bad Request',
    headers: new AxiosHeaders(),
    config: {
      headers: new AxiosHeaders(),
    },
    data: {},
  },
};

// Update FgaApiValidationError mock to use AxiosError
jest.mock('@openfga/sdk', () => ({
  OpenFgaClient: jest.fn().mockImplementation(() => mockFgaClient),
  CredentialsMethod: {
    ClientCredentials: 'ClientCredentials',
  },
  FgaApiValidationError: class FgaApiValidationError extends Error {
    constructor(
      message,
      public apiErrorMessage?: string,
    ) {
      super(message);
      this.name = 'FgaApiValidationError';
      this.apiErrorMessage = mockAxiosError.message;
    }
  },
  FgaApiAuthenticationError: class FgaApiAuthenticationError extends Error {
    constructor(message) {
      super(message);
      this.name = 'FgaApiAuthenticationError';
    }
  },
}));

describe('FgaService', () => {
  let service: FgaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FgaService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: RedisCacheService,
          useValue: mockRedisCacheService,
        },
      ],
    }).compile();

    service = module.get<FgaService>(FgaService);

    // Override the setTimeout function in the writeTuplesInBatches method
    jest.spyOn(global, 'setTimeout').mockImplementation((fn) => {
      return fn() as any;
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('initialization', () => {
    it('should create instance with correct config', () => {
      expect(service).toBeDefined();
      expect(OpenFgaClient).toHaveBeenCalledWith({
        apiUrl: 'http://fga.test',
        storeId: 'test-store',
        credentials: {
          method: CredentialsMethod.ClientCredentials,
          config: {
            apiTokenIssuer: 'test-issuer',
            apiAudience: 'test-audience',
            clientId: 'test-client',
            clientSecret: 'test-secret',
          },
        },
      });
    });
  });

  describe('checkUserAuthorization', () => {
    const testTuple = {
      user_type: FGAType.USER,
      user_id: 'user123',
      relation: FGARelation.MEMBER,
      object_type: FGAType.WORKSPACE,
      object_id: 'workspace123',
    };

    it('should return true when access is allowed', async () => {
      mockFgaClient.check.mockResolvedValueOnce({ allowed: true });
      mockRedisCacheService.get.mockResolvedValueOnce(null); // Cache miss

      const result = await service.checkUserAuthorization(testTuple);

      expect(result).toBe(true);
      expect(mockFgaClient.check).toHaveBeenCalledWith({
        user: 'user:user123',
        relation: FGARelation.MEMBER,
        object: 'workspace:workspace123',
      });
      expect(mockRedisCacheService.get).toHaveBeenCalledWith(
        'fga:check:user:user123:member:workspace:workspace123',
        { database: 0 },
      );
      expect(mockRedisCacheService.set).toHaveBeenCalledWith(
        'fga:check:user:user123:member:workspace:workspace123',
        true,
        { database: 0, ttlSeconds: 900 },
      );
      expect(retryModule.default).toHaveBeenCalled();
    });

    it('should return cached result when available', async () => {
      mockRedisCacheService.get.mockResolvedValueOnce(true); // Cache hit

      const result = await service.checkUserAuthorization(testTuple);

      expect(result).toBe(true);
      expect(mockFgaClient.check).not.toHaveBeenCalled();
      expect(mockRedisCacheService.get).toHaveBeenCalledWith(
        'fga:check:user:user123:member:workspace:workspace123',
        { database: 0 },
      );
    });

    it('should return false when access is denied', async () => {
      mockFgaClient.check.mockResolvedValueOnce({ allowed: false });
      mockRedisCacheService.get.mockResolvedValueOnce(null); // Cache miss

      const result = await service.checkUserAuthorization(testTuple);

      expect(result).toBe(false);
      expect(mockRedisCacheService.set).toHaveBeenCalledWith(
        'fga:check:user:user123:member:workspace:workspace123',
        false,
        { database: 0, ttlSeconds: 900 },
      );
    });

    it('should handle errors and throw exception', async () => {
      mockRedisCacheService.get.mockResolvedValueOnce(null); // Cache miss
      mockFgaClient.check.mockRejectedValueOnce(new Error('FGA Error'));

      await expect(service.checkUserAuthorization(testTuple)).rejects.toThrow(
        'Failed to Authorize',
      );
    });
  });

  describe('checkUserAuthorizationOnWorkspace', () => {
    it('should check authorization against configured workspace', async () => {
      mockFgaClient.check.mockResolvedValueOnce({ allowed: true });
      mockRedisCacheService.get.mockResolvedValueOnce(null); // Cache miss

      const result = await service.checkUserAuthorizationOnWorkspace(
        FGAType.USER,
        'user123',
        FGARelation.MEMBER,
      );

      expect(result).toBe(true);
      expect(mockFgaClient.check).toHaveBeenCalledWith({
        user: 'user:user123',
        relation: FGARelation.MEMBER,
        object: 'workspace:test-workspace',
      });
      expect(mockRedisCacheService.get).toHaveBeenCalledWith(
        'fga:check:user:user123:member:workspace:test-workspace',
        { database: 0 },
      );
      expect(mockRedisCacheService.set).toHaveBeenCalledWith(
        'fga:check:user:user123:member:workspace:test-workspace',
        true,
        { database: 0, ttlSeconds: 900 },
      );
    });

    it('should return cached workspace authorization result when available', async () => {
      mockRedisCacheService.get.mockResolvedValueOnce(true); // Cache hit

      const result = await service.checkUserAuthorizationOnWorkspace(
        FGAType.USER,
        'user123',
        FGARelation.MEMBER,
      );

      expect(result).toBe(true);
      expect(mockFgaClient.check).not.toHaveBeenCalled();
    });
  });

  describe('writeTuples', () => {
    it('should write tuples correctly and invalidate cache', async () => {
      const tuples = [
        {
          user_type: FGAType.USER,
          user_id: 'user123',
          relation: FGARelation.MEMBER,
          object_type: FGAType.WORKSPACE,
          object_id: 'workspace123',
        },
      ];

      await service.writeTuples(tuples);

      expect(mockFgaClient.write).toHaveBeenCalledWith({
        writes: [
          {
            user: 'user:user123',
            relation: FGARelation.MEMBER,
            object: 'workspace:workspace123',
          },
        ],
      });
      expect(mockRedisCacheService.clear).toHaveBeenCalledWith({ database: 0 });
    });

    describe('writeTuples retry logic', () => {
      const testTuples = [
        {
          user_type: FGAType.USER,
          user_id: 'user123',
          relation: FGARelation.MEMBER,
          object_type: FGAType.WORKSPACE,
          object_id: 'workspace123',
        },
        {
          user_type: FGAType.USER,
          user_id: 'user456',
          relation: FGARelation.ADMIN,
          object_type: FGAType.WORKSPACE,
          object_id: 'workspace456',
        },
      ];

      it('should retry and succeed on second attempt', async () => {
        // Reset mocks to ensure call counts are correct
        mockFgaClient.write.mockReset();
        mockRedisCacheService.clear.mockReset();

        // First call fails, second succeeds
        mockFgaClient.write
          .mockRejectedValueOnce(new Error('Network error'))
          .mockResolvedValueOnce(undefined);

        await service.writeTuples(testTuples);

        expect(mockFgaClient.write).toHaveBeenCalledTimes(2);
        expect(mockFgaClient.write).toHaveBeenCalledWith({
          writes: expect.arrayContaining([
            {
              user: 'user:user123',
              relation: FGARelation.MEMBER,
              object: 'workspace:workspace123',
            },
            {
              user: 'user:user456',
              relation: FGARelation.ADMIN,
              object: 'workspace:workspace456',
            },
          ]),
        });
        expect(mockRedisCacheService.clear).toHaveBeenCalledWith({
          database: 0,
        });
      });

      it('should fail after maximum retry attempts', async () => {
        // Reset mocks to ensure clean state
        mockFgaClient.write.mockReset();
        mockRedisCacheService.clear.mockReset();

        // Mock retryTimes to have a known value for testing
        (service as any).retryTimes = 3;

        // All attempts fail - setting up exactly retryTimes+1 total calls
        // (1 initial + 3 retries)
        mockFgaClient.write
          .mockRejectedValueOnce(new Error('Network error 1'))
          .mockRejectedValueOnce(new Error('Network error 2'))
          .mockRejectedValueOnce(new Error('Network error 3'))
          .mockRejectedValueOnce(new Error('Network error 4'));

        await expect(service.writeTuples(testTuples)).rejects.toThrow(
          'Failed to Complete Operation',
        );

        // Verify we tried exactly retryTimes+1 times (initial try + retries)
        expect(mockFgaClient.write).toHaveBeenCalledTimes(4);

        // Verify each call used the same arguments
        const expectedWriteArgs = {
          writes: expect.arrayContaining([
            {
              user: 'user:user123',
              relation: FGARelation.MEMBER,
              object: 'workspace:workspace123',
            },
            {
              user: 'user:user456',
              relation: FGARelation.ADMIN,
              object: 'workspace:workspace456',
            },
          ]),
        };

        // Verify all calls used the same arguments
        expect(mockFgaClient.write).toHaveBeenNthCalledWith(
          1,
          expectedWriteArgs,
        );
        expect(mockFgaClient.write).toHaveBeenNthCalledWith(
          2,
          expectedWriteArgs,
        );
        expect(mockFgaClient.write).toHaveBeenNthCalledWith(
          3,
          expectedWriteArgs,
        );
        expect(mockFgaClient.write).toHaveBeenNthCalledWith(
          4,
          expectedWriteArgs,
        );
      });

      it('should fall back to individual tuple writes when tuple already exists', async () => {
        const { FgaApiValidationError } = require('@openfga/sdk');
        const validationError = new FgaApiValidationError('Validation error');
        validationError.apiErrorMessage =
          'cannot write a tuple which already exists: user:user123 member workspace:workspace123';

        // Reset mocks to ensure call counts are correct
        mockFgaClient.write.mockReset();
        mockFgaClient.writeTuples.mockReset();
        mockRedisCacheService.clear.mockReset();

        // Batch write fails, individual writes succeed
        mockFgaClient.write.mockRejectedValueOnce(validationError);

        // Mock for individual tuple writes
        mockFgaClient.writeTuples.mockResolvedValue(undefined);

        await service.writeTuples(testTuples);

        // Verify the batch write attempt with correct arguments
        expect(mockFgaClient.write).toHaveBeenCalledWith({
          writes: expect.arrayContaining([
            {
              user: 'user:user123',
              relation: FGARelation.MEMBER,
              object: 'workspace:workspace123',
            },
            {
              user: 'user:user456',
              relation: FGARelation.ADMIN,
              object: 'workspace:workspace456',
            },
          ]),
        });

        // Verify individual writes were attempted with correct arguments
        expect(mockFgaClient.writeTuples).toHaveBeenNthCalledWith(1, [
          {
            user: 'user:user123',
            relation: FGARelation.MEMBER,
            object: 'workspace:workspace123',
          },
        ]);
        expect(mockFgaClient.writeTuples).toHaveBeenNthCalledWith(2, [
          {
            user: 'user:user456',
            relation: FGARelation.ADMIN,
            object: 'workspace:workspace456',
          },
        ]);

        // Verify cache was cleared
        expect(mockRedisCacheService.clear).toHaveBeenCalledWith({
          database: 0,
        });
      });

      it('should handle FgaApiValidationError for existing tuples in single write', async () => {
        const { FgaApiValidationError } = require('@openfga/sdk');

        // Create validation error with correct format
        const validationError = new FgaApiValidationError('Validation error');
        validationError.apiErrorMessage =
          'cannot write a tuple which already exists: user:user123 member workspace:workspace123';

        // Reset mocks to ensure call counts are correct
        mockFgaClient.write.mockReset();
        mockFgaClient.writeTuples.mockReset();
        mockRedisCacheService.clear.mockReset();

        // Batch write fails
        mockFgaClient.write.mockRejectedValueOnce(validationError);

        // First individual write fails with validation error, second succeeds
        mockFgaClient.writeTuples
          .mockRejectedValueOnce(validationError)
          .mockResolvedValueOnce(undefined);

        await service.writeTuples([testTuples[0]]);

        // Verify the batch write attempt with correct arguments
        expect(mockFgaClient.write).toHaveBeenCalledWith({
          writes: [
            {
              user: 'user:user123',
              relation: FGARelation.MEMBER,
              object: 'workspace:workspace123',
            },
          ],
        });

        // Verify individual write was attempted with correct arguments
        expect(mockFgaClient.writeTuples).toHaveBeenCalledWith([
          {
            user: 'user:user123',
            relation: FGARelation.MEMBER,
            object: 'workspace:workspace123',
          },
        ]);

        // Verify cache was cleared
        expect(mockRedisCacheService.clear).toHaveBeenCalledWith({
          database: 0,
        });
      });

      it('should attempt rollback when a batch fails after successful batch', async () => {
        // Create a larger set of tuples that will be processed in multiple batches
        const largeTupleSet = Array(3)
          .fill(0)
          .map((_, i) => ({
            user_type: FGAType.USER,
            user_id: `user${i}`,
            relation: FGARelation.MEMBER,
            object_type: FGAType.WORKSPACE,
            object_id: `workspace${i}`,
          }));

        // Reset mocks to ensure call counts are correct
        mockFgaClient.write.mockReset();
        mockRedisCacheService.clear.mockReset();

        // Override the batch size to 1 to ensure multiple batches
        (service as any).writeBatchSize = 1;

        // Disable retries for this test to have predictable call counts
        const originalRetryTimes = (service as any).retryTimes;
        (service as any).retryTimes = 0;

        // First batch succeeds, second batch fails with a non-retryable error that won't be retried
        mockFgaClient.write
          .mockResolvedValueOnce(undefined) // First write succeeds
          .mockImplementationOnce(() => {
            // Throw a non-retryable error that will cause immediate failure with no retries
            const error = new Error('Network error');
            // Make it look like a FgaApiValidationError which is non-retryable
            error.name = 'FgaApiValidationError';
            return Promise.reject(error);
          })
          .mockResolvedValueOnce(undefined); // Rollback succeeds

        try {
          await service.writeTuples(largeTupleSet);
          fail('Should have thrown an error');
        } catch (error) {
          // Error expected
        }

        // Restore retry times
        (service as any).retryTimes = originalRetryTimes;

        // Should have exactly 3 calls (first tuple write, second tuple write failing, rollback)
        expect(mockFgaClient.write).toHaveBeenCalledTimes(3);

        // Verify that the first call was a write operation with the first tuple
        expect(mockFgaClient.write.mock.calls[0][0]).toEqual({
          writes: [
            {
              user: 'user:user0',
              relation: FGARelation.MEMBER,
              object: 'workspace:workspace0',
            },
          ],
        });

        // Verify that the second call was a write operation with the second tuple
        expect(mockFgaClient.write.mock.calls[1][0]).toEqual({
          writes: [
            {
              user: 'user:user1',
              relation: FGARelation.MEMBER,
              object: 'workspace:workspace1',
            },
          ],
        });

        // Verify that the third call was a rollback operation (delete) with the first tuple
        expect(mockFgaClient.write.mock.calls[2][0]).toEqual({
          deletes: [
            {
              user: 'user:user0',
              relation: FGARelation.MEMBER,
              object: 'workspace:workspace0',
            },
          ],
        });

        // Restore the batch size
        (service as any).writeBatchSize = 100;
      });

      it('should handle combination of validation errors and network retries', async () => {
        // Create 3 tuples with different behaviors
        const complexTuples = [
          {
            user_type: FGAType.USER,
            user_id: 'already-exists',
            relation: FGARelation.MEMBER,
            object_type: FGAType.WORKSPACE,
            object_id: 'workspace1',
          },
          {
            user_type: FGAType.USER,
            user_id: 'works-fine',
            relation: FGARelation.MEMBER,
            object_type: FGAType.WORKSPACE,
            object_id: 'workspace2',
          },
          {
            user_type: FGAType.USER,
            user_id: 'needs-retry',
            relation: FGARelation.MEMBER,
            object_type: FGAType.WORKSPACE,
            object_id: 'workspace3',
          },
        ];

        // Reset all mocks
        mockFgaClient.write.mockReset();
        mockFgaClient.writeTuples.mockReset();
        mockRedisCacheService.clear.mockReset();

        // Create validation error
        const { FgaApiValidationError } = require('@openfga/sdk');
        const batchValidationError = new FgaApiValidationError(
          'Validation error',
        );
        batchValidationError.apiErrorMessage =
          'cannot write a tuple which already exists: user:already-exists member workspace:workspace1';

        const singleTupleValidationError = new FgaApiValidationError(
          'Validation error',
        );
        singleTupleValidationError.apiErrorMessage =
          'cannot write a tuple which already exists: user:already-exists member workspace:workspace1';

        // Batch write fails with validation error
        mockFgaClient.write.mockRejectedValueOnce(batchValidationError);

        // Mock individual tuple writes:
        // - First tuple: validation error (already exists)
        // - Second tuple: success
        // - Third tuple: network error, then success
        mockFgaClient.writeTuples
          // First tuple - validation error (already exists)
          .mockRejectedValueOnce(singleTupleValidationError)
          // Second tuple - success
          .mockResolvedValueOnce(undefined)
          // Third tuple - network error first time
          .mockRejectedValueOnce(new Error('Network error'))
          // Third tuple - success on retry
          .mockResolvedValueOnce(undefined);

        await service.writeTuples(complexTuples);

        // Verify batch write was called with all tuples
        expect(mockFgaClient.write).toHaveBeenCalledWith({
          writes: [
            {
              user: 'user:already-exists',
              relation: FGARelation.MEMBER,
              object: 'workspace:workspace1',
            },
            {
              user: 'user:works-fine',
              relation: FGARelation.MEMBER,
              object: 'workspace:workspace2',
            },
            {
              user: 'user:needs-retry',
              relation: FGARelation.MEMBER,
              object: 'workspace:workspace3',
            },
          ],
        });

        // Verify individual writes were attempted for all three tuples
        // First tuple - should get validation error
        expect(mockFgaClient.writeTuples).toHaveBeenNthCalledWith(1, [
          {
            user: 'user:already-exists',
            relation: FGARelation.MEMBER,
            object: 'workspace:workspace1',
          },
        ]);

        // Second tuple - should succeed
        expect(mockFgaClient.writeTuples).toHaveBeenNthCalledWith(2, [
          {
            user: 'user:works-fine',
            relation: FGARelation.MEMBER,
            object: 'workspace:workspace2',
          },
        ]);

        // Third tuple - should fail then retry
        expect(mockFgaClient.writeTuples).toHaveBeenNthCalledWith(3, [
          {
            user: 'user:needs-retry',
            relation: FGARelation.MEMBER,
            object: 'workspace:workspace3',
          },
        ]);

        // Same args for retry
        expect(mockFgaClient.writeTuples).toHaveBeenNthCalledWith(4, [
          {
            user: 'user:needs-retry',
            relation: FGARelation.MEMBER,
            object: 'workspace:workspace3',
          },
        ]);

        // Verify total call counts
        expect(mockFgaClient.write).toHaveBeenCalledTimes(1);
        expect(mockFgaClient.writeTuples).toHaveBeenCalledTimes(4);
        expect(mockRedisCacheService.clear).toHaveBeenCalledWith({
          database: 0,
        });
      });
    });
  });

  describe('deleteTuples', () => {
    it('should delete tuples correctly and invalidate cache', async () => {
      const tuples = [
        {
          user_type: FGAType.USER,
          user_id: 'user123',
          relation: FGARelation.MEMBER,
          object_type: FGAType.WORKSPACE,
          object_id: 'workspace123',
        },
      ];

      // Reset mocks to ensure clean state
      mockFgaClient.write.mockReset();
      mockRedisCacheService.clear.mockReset();

      await service.deleteTuples(tuples);

      expect(mockFgaClient.write).toHaveBeenCalledWith({
        deletes: [
          {
            user: 'user:user123',
            relation: FGARelation.MEMBER,
            object: 'workspace:workspace123',
          },
        ],
      });
      expect(mockRedisCacheService.clear).toHaveBeenCalledWith({ database: 0 });
    });
  });

  describe('checkTuple', () => {
    const testTuple = {
      user_type: FGAType.USER,
      user_id: 'user123',
      relation: FGARelation.MEMBER,
      object_type: FGAType.WORKSPACE,
      object_id: 'workspace123',
    };

    it('should check tuple correctly when allowed is true', async () => {
      mockFgaClient.check.mockResolvedValueOnce({ allowed: true });
      mockRedisCacheService.get.mockResolvedValueOnce(null); // Cache miss

      const result = await service.checkUserAuthorization(testTuple);

      expect(result).toBe(true);
      expect(mockFgaClient.check).toHaveBeenCalledWith({
        user: 'user:user123',
        relation: FGARelation.MEMBER,
        object: 'workspace:workspace123',
      });
      expect(mockRedisCacheService.set).toHaveBeenCalledWith(
        'fga:check:user:user123:member:workspace:workspace123',
        true,
        { database: 0, ttlSeconds: 900 },
      );
    });

    it('should return false when allowed is undefined or null', async () => {
      // Test undefined
      mockFgaClient.check.mockResolvedValueOnce({ allowed: undefined });
      mockRedisCacheService.get.mockResolvedValueOnce(null); // Cache miss

      let result = await service.checkUserAuthorization(testTuple);
      expect(result).toBe(false);
      expect(mockRedisCacheService.set).toHaveBeenCalledWith(
        'fga:check:user:user123:member:workspace:workspace123',
        false,
        { database: 0, ttlSeconds: 900 },
      );

      // Test null
      mockFgaClient.check.mockResolvedValueOnce({ allowed: null });
      mockRedisCacheService.get.mockResolvedValueOnce(null); // Cache miss

      result = await service.checkUserAuthorization(testTuple);
      expect(result).toBe(false);
      expect(mockRedisCacheService.set).toHaveBeenCalledWith(
        'fga:check:user:user123:member:workspace:workspace123',
        false,
        { database: 0, ttlSeconds: 900 },
      );

      expect(mockFgaClient.check).toHaveBeenCalledTimes(2);
    });

    it('should throw error when check fails', async () => {
      mockRedisCacheService.get.mockResolvedValueOnce(null); // Cache miss
      mockFgaClient.check.mockRejectedValueOnce(
        new Error('Failed to Authorize'),
      );

      await expect(service.checkUserAuthorization(testTuple)).rejects.toThrow(
        'Failed to Authorize',
      );
    });
  });

  describe('batchCheckTuples', () => {
    const testTuples = [
      {
        user_type: FGAType.USER,
        user_id: 'user123',
        relation: FGARelation.MEMBER,
        object_type: FGAType.WORKSPACE,
        object_id: 'workspace123',
      },
      {
        user_type: FGAType.USER,
        user_id: 'user123',
        relation: FGARelation.ADMIN,
        object_type: FGAType.GROUP,
        object_id: 'group456',
      },
    ];

    it('should return false when any tuple is not allowed', async () => {
      mockFgaClient.batchCheck.mockResolvedValueOnce({
        result: [{ allowed: true }, { allowed: false }],
      });

      const result = await service.batchCheckTuples(testTuples);

      expect(result).toBe(false);
    });

    it('should handle undefined allowed values', async () => {
      mockFgaClient.batchCheck.mockResolvedValueOnce({
        result: [{ allowed: true }, { allowed: undefined }],
      });

      const result = await service.batchCheckTuples(testTuples);

      expect(result).toBe(false);
      expect(mockFgaClient.batchCheck).toHaveBeenCalledTimes(1);
    });

    it('should throw error when batchCheck fails', async () => {
      mockFgaClient.batchCheck.mockRejectedValueOnce(new Error('FGA Error'));

      await expect(service.batchCheckTuples(testTuples)).rejects.toThrow(
        'Failed to Authorize',
      );
    });
  });

  describe('getAccessibleResourceIds', () => {
    it('should return correct resource ids', async () => {
      mockFgaClient.listObjects.mockResolvedValueOnce({
        objects: ['group:123', 'group:456', 'group:789'],
      });

      const result = await service.getAccessibleResourceIds(
        FGAType.USER,
        'user123',
        FGARelation.CAN_VIEW_GROUP_DETAILS,
        FGAType.GROUP,
      );

      expect(result).toEqual(['123', '456', '789']);
      expect(mockFgaClient.listObjects).toHaveBeenCalledWith({
        user: 'user:user123',
        relation: FGARelation.CAN_VIEW_GROUP_DETAILS,
        type: FGAType.GROUP,
      });
    });

    it('should handle errors and throw exception', async () => {
      mockFgaClient.listObjects.mockRejectedValueOnce(new Error('FGA Error'));

      await expect(
        service.getAccessibleResourceIds(
          FGAType.USER,
          'user123',
          FGARelation.CAN_VIEW_GROUP_DETAILS,
          FGAType.GROUP,
        ),
      ).rejects.toThrow('Failed to Authorize');
    });
  });
});
