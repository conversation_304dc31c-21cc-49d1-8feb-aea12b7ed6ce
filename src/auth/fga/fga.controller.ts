import { <PERSON>, Post, HttpStatus, Req, Logger } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ApiBearerAuth } from '@nestjs/swagger';
import { FgaService } from './fga.service';
import { RequireFgaPermission } from '../decorators/require-fga-permission.decorator';
import { FGAType, FGARelation } from './fga.enums';
import { ConfigService } from '@nestjs/config';
import { setConfigService } from '../../common/config-helpers';
import { getWorkspaceId } from '../../common/config-helpers';

/**
 * Controller for search operations
 */
@ApiTags('FGA')
@ApiBearerAuth()
@Controller('_internal/fga')
export class FgaController {
  private readonly logger: Logger;

  constructor(
    private readonly fgaService: FgaService,
    private readonly configService: ConfigService,
  ) {
    this.logger = new Logger(FgaController.name);
  }

  onModuleInit() {
    setConfigService(this.configService);
  }

  @ApiOperation({
    summary: 'Clear Cache',
    description: 'Clears the current FGA cache',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Search results returned successfully.',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Not permitted to clear the cache',
  })
  @Post('clear-cache')
  @RequireFgaPermission(
    FGAType.APP_CLIENT,
    FGARelation.CAN_CLEAR_FGA_CACHE,
    FGAType.WORKSPACE,
    (req) => getWorkspaceId(),
  )
  clearCache(@Req() req: any): void {
    this.logger.log('Request to clear FGA cache from ', req.user?.sub);
    this.fgaService.clearCache();
  }
}
