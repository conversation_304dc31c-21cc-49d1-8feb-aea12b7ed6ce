import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { RedisCacheService } from './redis-cache.service';

// Mock Redis client
const mockRedisClient = {
  connect: jest.fn().mockResolvedValue(undefined),
  disconnect: jest.fn().mockResolvedValue(undefined),
  select: jest.fn().mockResolvedValue(undefined),
  get: jest.fn(),
  set: jest.fn(),
  setEx: jest.fn(),
  del: jest.fn(),
  flushDb: jest.fn(),
  ping: jest.fn(),
  on: jest.fn(),
};

// Mock createClient function
jest.mock('redis', () => ({
  createClient: jest.fn(() => mockRedisClient),
}));

const mockConfigService = {
  get: jest.fn().mockImplementation((key: string, defaultValue?: any) => {
    const config = {
      REDIS_ENABLED: true,
      REDIS_HOST: 'localhost',
      REDIS_PORT: 6379,
    };
    return config[key] !== undefined ? config[key] : defaultValue;
  }),
};

describe('RedisCacheService', () => {
  let service: RedisCacheService;

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RedisCacheService,
        { provide: ConfigService, useValue: mockConfigService },
      ],
    }).compile();

    service = module.get<RedisCacheService>(RedisCacheService);

    // Manually set the client and connection status for testing
    (service as any).client = mockRedisClient;
    (service as any).isConnected = true;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should get value from cache', async () => {
    const testValue = { data: 'test' };
    mockRedisClient.get.mockResolvedValue(JSON.stringify(testValue));

    const result = await service.get('test-key');

    expect(mockRedisClient.get).toHaveBeenCalledWith('test-key');
    expect(result).toEqual(testValue);
  });

  it('should set value in cache with TTL', async () => {
    const testValue = { data: 'test' };
    const ttlSeconds = 300;

    await service.set('test-key', testValue, { ttlSeconds });

    expect(mockRedisClient.setEx).toHaveBeenCalledWith(
      'test-key',
      ttlSeconds,
      JSON.stringify(testValue),
    );
  });

  it('should set value in cache without TTL', async () => {
    const testValue = { data: 'test' };

    await service.set('test-key', testValue);

    expect(mockRedisClient.set).toHaveBeenCalledWith(
      'test-key',
      JSON.stringify(testValue),
    );
  });

  it('should delete key from cache', async () => {
    mockRedisClient.del.mockResolvedValue(1);

    await service.del('test-key');

    expect(mockRedisClient.del).toHaveBeenCalledWith('test-key');
  });

  it('should clear database', async () => {
    await service.clearAsync();

    expect(mockRedisClient.flushDb).toHaveBeenCalled();
  });

  it('should ping Redis successfully', async () => {
    mockRedisClient.ping.mockResolvedValue('PONG');

    const result = await service.ping();

    expect(result).toBe(true);
    expect(mockRedisClient.ping).toHaveBeenCalled();
  });

  it('should handle database selection', async () => {
    const testValue = { data: 'test' };
    mockRedisClient.get.mockResolvedValue(JSON.stringify(testValue));

    await service.get('test-key', { database: 1 });

    expect(mockRedisClient.select).toHaveBeenCalledWith(1);
    expect(mockRedisClient.get).toHaveBeenCalledWith('test-key');
  });

  it('should return null when Redis is not connected', async () => {
    (service as any).isConnected = false;

    const result = await service.get('test-key');

    expect(result).toBeNull();
    expect(mockRedisClient.get).not.toHaveBeenCalled();
  });

  it('should return null when Redis is disabled', async () => {
    const disabledConfigService = {
      get: jest.fn().mockImplementation((key: string, defaultValue?: any) => {
        if (key === 'REDIS_ENABLED') return false;
        return defaultValue;
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RedisCacheService,
        { provide: ConfigService, useValue: disabledConfigService },
      ],
    }).compile();

    const disabledService = module.get<RedisCacheService>(RedisCacheService);
    const result = await disabledService.get('test-key');

    expect(result).toBeNull();
  });

  it('should handle sync clear method', () => {
    const clearAsyncSpy = jest.spyOn(service, 'clearAsync').mockResolvedValue();

    service.clear();

    expect(clearAsyncSpy).toHaveBeenCalled();
  });
});
