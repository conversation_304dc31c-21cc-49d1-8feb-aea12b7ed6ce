import {
  Injectable,
  Logger,
  OnM<PERSON><PERSON><PERSON><PERSON><PERSON>,
  OnModuleInit,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createClient, RedisClientType } from 'redis';
import * as Sentry from '@sentry/nestjs';

export interface RedisCacheOptions {
  database?: number;
  ttlSeconds?: number;
}

@Injectable()
export class RedisCacheService implements OnModuleInit, OnModuleDestroy {
  private client: RedisClientType;
  private readonly logger = new Logger(RedisCacheService.name);
  private isConnected = false;
  private readonly host: string;
  private readonly port: number;
  private readonly redisEnabled: boolean;

  constructor(private readonly configService: ConfigService) {
    this.redisEnabled = this.configService.get<boolean>('REDIS_ENABLED', true);
    const host = this.configService.get<string>('REDIS_HOST');
    if (!host) {
      if (this.redisEnabled) {
        throw new Error('REDIS_HOST is not set');
      }
    } else {
      this.host = host;
    }
    this.port = this.configService.get<number>('REDIS_PORT', 6379);
  }

  async onModuleInit(): Promise<void> {
    if (!this.redisEnabled) {
      this.logger.log('Redis caching is disabled via REDIS_ENABLED=false');
      return;
    }

    await this.initializeClient();
  }

  async get<T>(key: string, options?: RedisCacheOptions): Promise<T | null> {
    if (!this.isRedisAvailable()) {
      return null;
    }

    try {
      const database = options?.database ?? 0;
      await this.ensureDatabase(database);

      const value = await this.client.get(key);

      if (value === null) {
        this.logger.debug(`Cache miss for key: ${key} (db: ${database})`);
        return null;
      }

      this.logger.debug(`Cache hit for key: ${key} (db: ${database})`);
      return JSON.parse(value) as T;
    } catch (error) {
      this.reportError(error, `Failed to get key ${key}`, {
        key,
        database: options?.database,
      });
      return null;
    }
  }

  async set<T>(
    key: string,
    value: T,
    options?: RedisCacheOptions,
  ): Promise<void> {
    if (!this.isRedisAvailable()) {
      return;
    }

    try {
      const database = options?.database ?? 0;
      const ttlSeconds = options?.ttlSeconds;

      await this.ensureDatabase(database);

      const serializedValue = JSON.stringify(value);

      if (ttlSeconds && ttlSeconds > 0) {
        await this.client.setEx(key, ttlSeconds, serializedValue);
        this.logger.debug(
          `Cached key: ${key} with TTL: ${ttlSeconds}s (db: ${database})`,
        );
      } else {
        await this.client.set(key, serializedValue);
        this.logger.debug(`Cached key: ${key} without TTL (db: ${database})`);
      }
    } catch (error) {
      this.reportError(error, `Failed to set key ${key}`, {
        key,
        database: options?.database,
        ttlSeconds: options?.ttlSeconds,
      });
    }
  }

  async del(key: string, options?: RedisCacheOptions): Promise<void> {
    if (!this.isRedisAvailable()) {
      return;
    }

    try {
      const database = options?.database ?? 0;
      await this.ensureDatabase(database);

      const deletedCount = await this.client.del(key);
      this.logger.debug(
        `Deleted ${deletedCount} key(s): ${key} (db: ${database})`,
      );
    } catch (error) {
      this.reportError(error, `Failed to delete key ${key}`, {
        key,
        database: options?.database,
      });
    }
  }

  async clearAsync(options?: RedisCacheOptions): Promise<void> {
    if (!this.isRedisAvailable()) {
      return;
    }

    try {
      const database = options?.database ?? 0;
      await this.ensureDatabase(database);

      await this.client.flushDb();
      this.logger.log(`Cleared cache for database: ${database}`);
    } catch (error) {
      this.logger.error('Failed to clear cache:', error);
      throw error;
    }
  }

  clear(options?: RedisCacheOptions): void {
    this.clearAsync(options).catch((error) => {
      this.reportError(error, 'Failed to clear Redis cache', {
        database: options?.database ?? 0,
      });
    });
  }

  async ping(): Promise<boolean> {
    if (!this.isConnected) {
      return false;
    }

    try {
      const response = await this.client.ping();
      return response === 'PONG';
    } catch (error) {
      this.reportError(error, 'Redis ping failed');
      return false;
    }
  }

  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  async onModuleDestroy(): Promise<void> {
    if (this.client) {
      try {
        await this.client.disconnect();
        this.logger.log('Redis client disconnected successfully');
      } catch (error) {
        this.logger.error('Error disconnecting Redis client:', error);
      }
    }
  }

  private async initializeClient(): Promise<void> {
    try {
      this.client = createClient({
        socket: {
          host: this.host,
          port: this.port,
        },
      });

      this.client.on('error', (error) => {
        this.logger.error('Redis client error:', error);
        this.isConnected = false;
      });

      this.client.on('connect', () => {
        this.logger.log(`Redis client connected to ${this.host}:${this.port}`);
        this.isConnected = true;
      });

      this.client.on('disconnect', () => {
        this.logger.warn('Redis client disconnected');
        this.isConnected = false;
      });

      await this.client.connect();
    } catch (error) {
      this.logger.error('Failed to initialize Redis client:', error);
      this.isConnected = false;
    }
  }

  private isRedisAvailable(): boolean {
    if (!this.redisEnabled) {
      this.logger.debug('Redis disabled');
      return false;
    }
    if (!this.isConnected) {
      this.logger.warn('Redis not connected');
      return false;
    }
    return true;
  }

  private async ensureDatabase(database: number): Promise<void> {
    // Only call SELECT if not using default database (0)
    if (database !== 0) {
      await this.client.select(database);
    }
  }

  private reportError(
    error: unknown,
    context: string,
    extra?: Record<string, any>,
  ): void {
    this.logger.error(`${context}:`, error);
    if (process.env.SENTRY_DSN_URL) {
      Sentry.captureException(error, {
        extra: {
          context,
          ...extra,
        },
      });
    }
  }
}
