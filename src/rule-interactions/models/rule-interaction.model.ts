import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsUUID } from 'class-validator';
import { Rule } from '../../rules/models/rule.model';
import { UserResponse } from '../../auth/interfaces/user-response.interface';

export enum RuleInteractionType {
  LIKE = 'LIKE',
  DISLIKE = 'DISLIKE',
  DOWNLOAD = 'DOWNLOAD',
  BOOKMARK = 'BOOKMARK',
  ADD_AS_INSPIRATION = 'ADD_AS_INSPIRATION',
}

export class RuleInteraction {
  @ApiProperty({
    description: 'ID of the rule being interacted with',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID()
  rule_id: string;

  @ApiProperty({
    description: 'ID of the user who interacted with the rule',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID()
  user_id: string;

  @ApiProperty({
    description: 'Type of interaction',
    enum: RuleInteractionType,
    example: RuleInteractionType.LIKE,
  })
  @IsEnum(RuleInteractionType)
  type: RuleInteractionType;

  @ApiProperty({
    description: 'When the interaction occurred',
    example: '2024-03-19T12:00:00Z',
  })
  created_at: string;
}

export class RuleInteractionWithRuleDetails extends RuleInteraction {
  @ApiProperty({
    description: 'Details of the rule being interacted with',
    type: Rule,
  })
  rule: Rule;
}

export class RuleInteractionWithUserDetails extends RuleInteraction {
  @ApiProperty({
    description: 'Details of the user who interacted with the rule',
    type: UserResponse,
  })
  user: UserResponse;
}

export class RuleInteractionResponse {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Current like count for the rule',
    example: 42,
  })
  likes?: number;

  @ApiProperty({
    description: 'Current dislike count for the rule',
    example: 5,
  })
  dislikes?: number;

  @ApiProperty({
    description: 'Current download count for the rule',
    example: 100,
  })
  downloads?: number;

  @ApiProperty({
    description: 'Message describing the result',
    example: 'Rule liked successfully',
  })
  message: string;
}
