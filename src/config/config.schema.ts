import * as Jo<PERSON> from 'joi';

export const configValidationSchema = Joi.object({
  // API Configuration
  PORT: Joi.number().default(3003),
  NODE_ENV: Joi.string()
    .valid('development', 'production', 'test')
    .default('development'),

  // Logging Configuration
  APP_LOG_COLOR: Joi.string().valid('true', 'false').default('false'),
  APP_LOG_LEVEL: Joi.string()
    .valid('debug', 'info', 'warn', 'error')
    .default('info'),

  // Auth0 Configuration
  AUTH0_DOMAIN: Joi.string().required(),
  AUTH0_AUDIENCE: Joi.string().required(),
  AUTH0_ISSUER_URL: Joi.string().uri().required(),
  AUTH0_WEB_CLIENT_ID: Joi.string().required(),
  AUTH0_API_CLIENT_ID: Joi.string().required(),
  AUTHORIZED_CLIENT_IDS: Joi.string().default(''),

  // JWT Security Settings
  AUTH0_JWT_CACHE_ENABLED: Joi.boolean().default(true),
  AUTH0_JWT_RATE_LIMIT_ENABLED: Joi.boolean().default(true),
  AUTH0_JWT_REQUESTS_PER_MINUTE: Joi.number().min(1).max(20).default(5),

  // AI Generation Configuration
  OPENAI_API_KEY: Joi.string().required(),

  // OpenFGA Configuration
  FGA_WORKSPACE_ID: Joi.string().required(),
  FGA_API_URL: Joi.string().uri().required(),
  FGA_STORE_ID: Joi.string().required(),
  FGA_API_TOKEN_ISSUER: Joi.string().required(),
  FGA_API_AUDIENCE: Joi.string().required(),
  FGA_CLIENT_ID: Joi.string().required(),
  FGA_CLIENT_SECRET: Joi.string().required(),

  // FGA Cache and Retry Configuration
  FGA_CACHE_TTL: Joi.number().default(900),
  FGA_CACHE_LIST_TTL: Joi.number().default(900),
  FGA_CACHE_ENABLED: Joi.string().valid('true', 'false').default('true'),
  FGA_MAX_RETRIES: Joi.number().default(3),
  FGA_INITIAL_BACKOFF_MS: Joi.number().default(3000),
  FGA_BACKOFF_MULTIPLIER: Joi.number().default(1),

  // AWS Configuration
  AWS_REGION: Joi.string().default('us-west-2'),

  // In a local dev environment with DYNAMODB_LOCAL=false, we need AWS credentials
  // In deployed environments, credentials are provided through instance profiles
  AWS_ACCESS_KEY_ID: Joi.alternatives().conditional('NODE_ENV', {
    is: 'development',
    then: Joi.alternatives().conditional('DYNAMODB_LOCAL', {
      is: 'false',
      then: Joi.string().required(),
      otherwise: Joi.string().optional(),
    }),
    otherwise: Joi.string().optional(),
  }),

  AWS_SECRET_ACCESS_KEY: Joi.alternatives().conditional('NODE_ENV', {
    is: 'development',
    then: Joi.alternatives().conditional('DYNAMODB_LOCAL', {
      is: 'false',
      then: Joi.string().required(),
      otherwise: Joi.string().optional(),
    }),
    otherwise: Joi.string().optional(),
  }),

  AWS_SESSION_TOKEN: Joi.string().optional(),

  // DynamoDB Configuration
  DYNAMODB_LOCAL: Joi.string().valid('true', 'false').default('false'),
  DYNAMODB_LOCAL_ENDPOINT: Joi.string().default('http://localhost:8000'),
  DYNAMODB_TABLE_PREFIX: Joi.string().default('dev'),
  DYNAMODB_AUTO_MIGRATIONS: Joi.string()
    .valid('true', 'false')
    .default('false'),
  DYNAMODB_MIGRATION_MODE: Joi.string()
    .valid('DRY_RUN', 'SAFE', 'FULL')
    .default('SAFE'),
  DYNAMODB_ALLOW_ATTRIBUTE_RENAMES: Joi.string()
    .valid('true', 'false')
    .default('false'),
  DYNAMODB_ALLOW_INDEX_CHANGES: Joi.string()
    .valid('true', 'false')
    .default('true'),

  // S3 Configuration
  S3_LOCAL: Joi.string().valid('true', 'false').default('false').optional(),
  S3_LOCAL_ENDPOINT: Joi.string().when('S3_LOCAL', {
    is: 'true',
    then: Joi.string().required(),
    otherwise: Joi.string().optional(),
  }),
  RULES_UPLOAD_S3_BUCKET_NAME: Joi.string().required(),
  RULES_DOWNLOAD_S3_BUCKET_NAME: Joi.string().required(),
  RULES_DOWNLOAD_URL_EXPIRY: Joi.number().default(3600),
  MAX_RULES_PER_ZIP: Joi.number().default(1000),
  INSPIRATION_BUCKET_NAME: Joi.string().default(
    's2s_community_dev_inspirations',
  ),
  INSPIRATION_MAX_FILE_UPLOAD_SIZE_MB: Joi.number().default(10),
  INSPIRATIONS_DOWNLOAD_EXPIRY_SECONDS: Joi.number().required(),

  // OpenSearch Configuration
  OPENSEARCH_NODE: Joi.string().default('https://localhost:9200'),
  OPENSEARCH_USERNAME: Joi.string().optional(),
  OPENSEARCH_PASSWORD: Joi.string().optional(),
  OPENSEARCH_INDEX_PREFIX: Joi.string().default('dev'),
  OPENSEARCH_SSL_VERIFY: Joi.string().valid('true', 'false').default('false'),
  OPENSEARCH_AUTO_MIGRATIONS: Joi.string()
    .valid('true', 'false')
    .default('true'),

  // External Services
  USER_SERVICE_URL: Joi.string().uri().required(),
  TRANSLATION_SERVICE_URL: Joi.string().uri().required(),
  LANGUAGE_DETECTION_CONFIDENCE_THRESHOLD: Joi.number().min(0).max(1),
  LANGUAGE_DETECTION_TIMEOUT_MS: Joi.number().default(10000),

  // CORS Configuration
  CORS_ALLOWED_ORIGINS: Joi.string().default(''),
  CORS_ALLOWED_METHODS: Joi.string().default('GET,POST,PUT,DELETE,OPTIONS'),
  CORS_ALLOWED_HEADERS: Joi.string().default('*'),
  CORS_CREDENTIALS: Joi.boolean().default(true),
  CORS_MAX_AGE: Joi.number().default(3600),

  // Sentry Configuration
  SENTRY_DSN_URL: Joi.string().uri().optional(),
  SENTRY_ENV: Joi.string().valid('development', 'qa', 'production').optional(),

  // Rules Caching Configuration
  RULES_COUNTS_CACHE_ENABLED: Joi.boolean().default(true),
  RULES_COUNTS_CACHE_TTL: Joi.number().default(900),
  RULES_COUNTS_CACHE_MAX_ITEMS: Joi.number().default(1000),

  S3_PUBLIC_BUCKET_NAME: Joi.string().required(),

  // Redis Configuration
  REDIS_ENABLED: Joi.boolean().default(true),
  REDIS_HOST: Joi.string(),
  REDIS_PORT: Joi.number(),
  REDIS_FGA_DB: Joi.number(),
});
