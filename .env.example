# DynamoDB Configuration
DYNAMODB_LOCAL=true
DYNAMODB_LOCAL_ENDPOINT=http://localhost:8000
AWS_REGION=us-west-2
AWS_ACCESS_KEY_ID=local
AWS_SECRET_ACCESS_KEY=local
DYNAMODB_TABLE_PREFIX=dev
DYNAMODB_AUTO_MIGRATIONS=true

# DynamoDB Migration Configuration
DYNAMODB_MIGRATION_MODE=SAFE
DYNAMODB_ALLOW_ATTRIBUTE_RENAMES=false
DYNAMODB_ALLOW_INDEX_CHANGES=true

# S3 Configuration
RULES_UPLOAD_S3_BUCKET_NAME=s2s_community_dev_rules_upload
RULES_DOWNLOAD_S3_BUCKET_NAME=s2s_community_dev_rules_download
RULES_DOWNLOAD_URL_EXPIRY=3600
MAX_RULES_PER_ZIP=1000
INSPIRATION_BUCKET_NAME=inspirations-bucket
INSPIRATION_MAX_FILE_UPLOAD_SIZE_MB=10
# Required - no default provided
INSPIRATIONS_DOWNLOAD_EXPIRY_SECONDS=3600

# Application Configuration
PORT=3003

# AI Generation Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Translation Service Configuration (required)
TRANSLATION_SERVICE_URL=https://rule-translation-backend.dev.s2s.ai
LANGUAGE_DETECTION_CONFIDENCE_THRESHOLD=0.7
LANGUAGE_DETECTION_TIMEOUT_MS=10000

# OpenFGA Configuration
FGA_WORKSPACE_ID='detections-ai-dev'
FGA_API_AUDIENCE='https://api.us1.fga.dev/'
FGA_API_URL='https://api.us1.fga.dev'
FGA_CLIENT_ID=
FGA_CLIENT_SECRET=
FGA_STORE_ID='01JMMNN7XX3B3WT991KG60PCTK'
FGA_API_TOKEN_ISSUER="auth.fga.dev"

# FGA Cache and Retry Configuration
FGA_CACHE_TTL=900
FGA_CACHE_LIST_TTL=300
FGA_CACHE_ENABLED=true
FGA_MAX_RETRIES=3
FGA_INITIAL_BACKOFF_MS=3000
FGA_BACKOFF_MULTIPLIER=1

# Auth0 Configuration
AUTH0_DOMAIN=https://systemtwosecurity.us.auth0.com
AUTH0_ISSUER_URL=https://systemtwosecurity.us.auth0.com/
AUTH0_AUDIENCE=https://api.detections.ai
AUTH0_WEB_CLIENT_ID=j3Hkwy368kyi6Jh1NyRXkERsVZyyBOQZ
AUTH0_API_CLIENT_ID=cB2IhawhpiMXqp74d0R4xoh6cK1J3GaU
AUTHORIZED_CLIENT_IDS=2xUJcG9yeIee4QihjOb2U4c2Sa7gzSG4
AUTH0_JWT_CACHE_ENABLED=true
AUTH0_JWT_RATE_LIMIT_ENABLED=true
AUTH0_JWT_REQUESTS_PER_MINUTE=5

# Logging
APP_LOG_COLOR=false
APP_LOG_LEVEL=info

# OpenSearch Configuration
OPENSEARCH_NODE=https://localhost:9200
OPENSEARCH_USERNAME=admin  # For local user only
OPENSEARCH_PASSWORD=admin  # For local user only
OPENSEARCH_INDEX_PREFIX=dev
OPENSEARCH_SSL_VERIFY=false
OPENSEARCH_AUTO_MIGRATIONS=true

USER_SERVICE_URL=http://localhost:3000

# CORS Configuration
CORS_ALLOWED_ORIGINS=https://detections.dev.s2s.ai
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=*
CORS_CREDENTIALS=true
CORS_MAX_AGE=3600

# Rules Caching Configuration
RULES_COUNTS_CACHE_ENABLED=true
RULES_COUNTS_CACHE_TTL=900
RULES_COUNTS_CACHE_MAX_ITEMS=1000

# MITRE ATTACK OPENSEARCH DATA
MITRE_DATA_URL=https://raw.githubusercontent.com/mitre/cti/dc2ea9270f1a91398c750a6dceb3591b86082ce8/enterprise-attack/enterprise-attack.json
MITRE_ATTACK_VERSION_TO_STORE=17.1

S3_PUBLIC_BUCKET_NAME=data.us-west-2.systemtwosecurity.com

# VERTEX AI SPECIFIC ENVVARS
VERTEX_MODEL_NAME=gemini-2.5-flash-preview-04-17
VERTEX_LOCATION=us-central1
VERTEX_PROJECT_ID=s2s-hunter
GOOGLE_APPLICATION_CREDENTIALS=/tmp/google.json
GOOGLE_AUTH_DATA='{"type": "service_account", "project_id": "<project_id>", "private_key_id": "1234123....}'

# FGA REDIS
REDIS_ENABLED=true
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_FGA_DB=0
