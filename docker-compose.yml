services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - GITHUB_TOKEN
    container_name: detection-api-app
    ports:
      - "3003:3003"
    env_file:
      - .env
    # Override specific environment variables that need different values in container
    environment:
      - DYNAMODB_LOCAL_ENDPOINT=http://dynamodb-local:8000
      - OPENSEARCH_NODE=http://opensearch:9200
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_FGA_DB=0
    volumes:
      - ./src:/usr/src/app/src
      - ./node_modules:/usr/src/app/node_modules
    depends_on:
      - dynamodb-local
      - opensearch
      - redis
    restart: unless-stopped
    networks:
      - detection-network

  dynamodb-local:
    image: amazon/dynamodb-local:latest
    container_name: dynamodb-local
    ports:
      - "8000:8000"
    command: "-jar DynamoDBLocal.jar -sharedDb -inMemory"
    working_dir: /home/<USER>
    user: root
    volumes:
      - dynamodb-data:/home/<USER>/data
    restart: unless-stopped
    networks:
      - detection-network

  dynamodb-admin:
    image: aaronshaf/dynamodb-admin
    container_name: dynamodb-admin
    ports:
      - "8001:8001"
    environment:
      - DYNAMO_ENDPOINT=http://dynamodb-local:8000
    depends_on:
      - dynamodb-local
    restart: unless-stopped
    networks:
      - detection-network

  opensearch:
    image: opensearchproject/opensearch:2.13.0
    container_name: opensearch
    environment:
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx512m"
      - "DISABLE_SECURITY_PLUGIN=true"
      - "_JAVA_OPTIONS=-XX:UseSVE=0"
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    ports:
      - "9200:9200"
      - "9600:9600"
    volumes:
      - opensearch-data:/usr/share/opensearch/data
    networks:
      - detection-network
    restart: unless-stopped

  redis:
    image: redis:7.4.4-alpine
    container_name: redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    networks:
      - detection-network
    restart: unless-stopped

networks:
  detection-network:
    driver: bridge
    name: detection-network

volumes:
  dynamodb-data:
  opensearch-data:
  redis-data: 