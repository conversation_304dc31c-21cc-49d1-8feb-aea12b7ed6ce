# Detections.AI Rule Management 

A NestJS application for managing security detection rules with advanced search capabilities using OpenSearch.

## Overview

This application provides a comprehensive API for managing security detection rules, including:

- CRUD operations for detection rules
- Advanced search capabilities using OpenSearch
- Rule versioning and validation
- Rule export and import
- Authentication and authorization

## Features

- **Rule Management**: Create, read, update, and delete security detection rules
- **Advanced Search**: Search rules by content, metadata, and other attributes
- **Similar Rule Finding**: Find rules similar to a given rule
- **Autocomplete**: Get suggestions for rule titles
- **Rule Export**: Export rules as a ZIP archive
- **Rule Import**: Import rules from various formats
- **Authentication**: Secure API access with Auth0
- **Authorization**: Fine-grained access control with OpenFGA

## Technology Stack

- **Framework**: NestJS
- **Database**: DynamoDB
- **Search Engine**: OpenSearch
- **Storage**: AWS S3
- **Authentication**: Auth0
- **Authorization**: OpenFGA

## Architecture

The application follows a modular architecture with the following components:

- **Rules Module**: Manages rule CRUD operations
- **OpenSearch Module**: Provides search capabilities
- **DynamoDB Module**: Handles data persistence
- **S3 Module**: Manages file storage
- **Auth Module**: Handles authentication and authorization

## Getting Started

### Prerequisites

- Node.js 18 or higher
- Docker and Docker Compose (for local development)
- AWS account (for production deployment)

### Installation

1. Clone the repository:

```bash
git clone https://github.com/yourusername/detections.ai-rule-management.git
cd detections.ai-rule-management
```

2. Install dependencies:

```bash
npm install
```

3. Set up environment variables:

```bash
cp .env.example .env
```

Edit the `.env` file to configure your environment.

4. Start the local development environment:

```bash
docker-compose up -d
```

5. Run the application:

```bash
npm run start:dev
```

### Environment Variables

The application uses the following environment variables:

#### API Configuration
- `PORT`: The port on which the API will run (default: 3003)

# Sentry Configuration
- `SENTRY_DSN_URL`: Used to tell sentry integration where to send captured metrics, errors
- `SENTRY_ENV`: Used to determine which environment the service is running in: `dev`, `qa`, `production`

#### Authentication
- `AUTH0_DOMAIN`: Auth0 domain
- `AUTH0_ISSUER_URL`: Auth0 issuer URL
- `AUTH0_AUDIENCE`: Auth0 audience
- `AUTH0_JWT_CACHE_ENABLED`: Enable JWT cache (default: true)
- `AUTH0_JWT_RATE_LIMIT_ENABLED`: Enable JWT rate limiting (default: true)
- `AUTH0_JWT_REQUESTS_PER_MINUTE`: JWT requests per minute (default: 5)

#### AI Generation
- `OPENAI_API_KEY`: OpenAI API key for AI-powered rule generation and analysis

#### Authorization
- `FGA_API_URL`: OpenFGA API URL
- `FGA_STORE_ID`: OpenFGA store ID
- `FGA_API_TOKEN_ISSUER`: OpenFGA token issuer
- `FGA_API_AUDIENCE`: OpenFGA audience
- `FGA_CLIENT_ID`: OpenFGA client ID
- `FGA_CLIENT_SECRET`: OpenFGA client secret
- `FGA_WORKSPACE_ID`: OpenFGA workspace ID

#### AWS Configuration
- `AWS_REGION`: AWS region (default: us-west-2)
- `AWS_ACCESS_KEY_ID`: AWS access key ID
- `AWS_SECRET_ACCESS_KEY`: AWS secret access key

#### DynamoDB Configuration
- `DYNAMODB_TABLE_PREFIX`: Prefix for DynamoDB tables (default: dev)
- `DYNAMODB_LOCAL`: Use local DynamoDB (default: true)
- `DYNAMODB_LOCAL_ENDPOINT`: Local DynamoDB endpoint (default: http://localhost:8000)
- `DYNAMODB_AUTO_MIGRATIONS`: Enable automatic migrations (default: true)
- `DYNAMODB_MIGRATION_MODE`: Mode for schema migrations (DRY_RUN, SAFE, FULL)
- `DYNAMODB_ALLOW_ATTRIBUTE_RENAMES`: Allow renaming of attributes during migrations
- `DYNAMODB_ALLOW_INDEX_CHANGES`: Allow changes to indexes during migrations

#### S3 Configuration
- `S3_LOCAL`: Use local S3 (default: false)
- `S3_LOCAL_ENDPOINT`: Local S3 endpoint (default: http://localhost:4566)
- `S3_PUBLIC_BUCKET_NAME`: Public accessible S3 bucket used for public assets
- `RULES_S3_BUCKET_NAME`: S3 bucket name for rules
- `RULES_DOWNLOAD_URL_EXPIRY`: Expiry time for download URLs in seconds (default: 3600)
- `MAX_RULES_PER_ZIP`: Maximum number of rules allowed in a ZIP archive (default: 1000)
- `INSPIRATION_BUCKET_NAME`: S3 bucket name for inspirations
- `INSPIRATION_MAX_FILE_UPLOAD_SIZE_MB`: Maximum file size for inspiration uploads in MB (default: 10)
- `INSPIRATIONS_DOWNLOAD_EXPIRY_SECONDS`: Expiry time for inspiration download URLs in seconds (required)

#### OpenSearch Configuration
- `OPENSEARCH_NODE`: OpenSearch node URL (default: https://localhost:9200)
- `OPENSEARCH_USERNAME`: OpenSearch username for local user only
- `OPENSEARCH_PASSWORD`: OpenSearch password for local user only
- `OPENSEARCH_INDEX_PREFIX`: Prefix for OpenSearch indices (default: dev)
- `OPENSEARCH_SSL_VERIFY`: Verify SSL certificates (default: false)
- `OPENSEARCH_AUTO_MIGRATIONS`: Enable automatic migrations (default: true)
- `AWS_REGION`: AWS region for SigV4 authentication (default: us-west-2)

#### Redis Configuration
- `REDIS_ENABLED`: Enable Redis caching (default: true)
- `REDIS_HOST`: Redis host endpoint (e.g., redis for Docker, localhost for local Redis)
- `REDIS_PORT`: Redis port (default: 6379)
- `REDIS_FGA_DB`: Redis database index for FGA cache (default: 0)

### Local Development with Redis

The application uses Redis for caching authorization data and rule metadata. You have several options for local development:

#### Option 1: Using Docker (Recommended)
The easiest approach is to use the provided Docker Compose setup:

```bash
docker-compose up -d
npm run start:dev
```

This automatically starts a Redis container and configures the application to use it.

#### Option 2: Using Local Redis Installation
If you prefer to run Redis locally:

1. Install Redis on your system:
   ```bash
   # macOS
   brew install redis
   
   # Ubuntu/Debian
   sudo apt-get install redis-server
   
   # Windows
   # Download from https://redis.io/download
   ```

2. Start Redis:
   ```bash
   redis-server
   ```

3. Update your `.env` file:
   ```bash
   REDIS_ENABLED=true
   REDIS_HOST=localhost
   REDIS_PORT=6379
   REDIS_FGA_DB=0
   ```

4. Start the application and make sure it connects:
   ```bash
   npm run start:dev
   ```

#### Option 3: Disable Redis for Development
If you want to develop without Redis (caching will be disabled):

1. Update your `.env` file:
   ```bash
   REDIS_ENABLED=false
   ```

2. Start the application:
   ```bash
   npm run start:dev
   ```

**Note:** When Redis is disabled, authorization checks and metadata requests will not be cached, which may impact performance but allows for development without Redis infrastructure.

> **Production Note:** For AWS ElastiCache with IAM authentication, no password is required in the application configuration. Ensure your application's IAM role has permissions to access the Redis cluster.

## API Documentation

The API documentation is available at `/api/docs` when the application is running.

### Search API

The Search API provides powerful capabilities for finding and filtering detection rules. All search endpoints are available under the `/api/v1/search` base path.

#### Basic Search

```http
GET /api/v1/search
```

Simple search with query parameters for basic filtering and pagination.

**Query Parameters:**
- `q` (string): Search query text
- `created_after` (string): Filter rules created after this date (ISO format or YYYY-MM-DD)
- `created_before` (string): Filter rules created before this date (ISO format or YYYY-MM-DD)
- `published_after` (string): Filter rules published after this date (ISO format or YYYY-MM-DD)
- `published_before` (string): Filter rules published before this date (ISO format or YYYY-MM-DD)
- `updated_after` (string): Filter rules updated after this date (ISO format or YYYY-MM-DD)
- `updated_before` (string): Filter rules updated before this date (ISO format or YYYY-MM-DD)
- `owner_ids` (string[]): Filter by owner IDs (groups that own the rules)
- `exclude[owner_ids]` (string[]): Exclude rules from these owner IDs
- `rule_types` (string[]): Filter by rule types (SIGMA, SPL, KQL)
- `statuses` (string[]): Filter by rule statuses
- `stages` (string[]): Filter by rule stages
- `severities` (string[]): Filter by rule severities
- `mitre_tactics` (string[]): Filter by MITRE ATT&CK tactics
- `mitre_techniques` (string[]): Filter by MITRE ATT&CK techniques
- `cursor` (string, optional): Pagination cursor for next/previous page
- `size` (number, default: 20): Number of results to return
- `direction` (string, optional): Direction of pagination ('forward' or 'backward')
- `sort_by` (string): Field to sort by
- `sort_order` (string): Sort order ('asc' or 'desc')

**Example Request:**
```bash
# Search for process execution rules created in January 2025
curl "http://localhost:3000/api/v1/search?q=process+execution&created_after=2025-01-01&created_before=2025-02-01"

# Search for rules published after a specific date
curl "http://localhost:3000/api/v1/search?published_after=2025-01-01"

# Search for recently updated rules
curl "http://localhost:3000/api/v1/search?updated_after=2024-03-01"

# Combine multiple date filters
curl "http://localhost:3000/api/v1/search?created_after=2024-01-01&published_before=2025-12-31&updated_after=2025-06-01"

# Get next page using cursor
curl "http://localhost:3000/api/v1/search?q=process+execution&cursor=eyJjcmVhdGVkX2F0IjoiMjAyNC0wMy0xNFQwOTowMTowMFoiLCJpZCI6InJ1bGUxMjMifQ=="

# Search for high severity SIGMA rules with backward pagination
curl "http://localhost:3000/api/v1/search?rule_types[]=SIGMA&severities[]=HIGH&cursor=eyJjcmVhdGVkX2F0IjoiMjAyNC0wMy0xNFQwOTowMTowMFoiLCJpZCI6InJ1bGUxMjMifQ==&direction=backward"

# Search for rules from specific owners, excluding test rules
curl "http://localhost:3000/api/v1/search?owner_ids[]=prod_group&owner_ids[]=dev_group&exclude[owner_ids][]=test_rules"
```

#### Advanced Search

```http
POST /api/v1/search/advanced
```

Advanced search with more complex query options and filters.

**Request Body:**
```json
{
  "query_text": "process execution",
  "search_fields": ["title", "description", "content", "metadata.tags"],
  "boost": {
    "title": 4,
    "description": 3,
    "content": 2,
    "metadata.tags": 1.5
  },
  "filters": {
    "owner_ids": ["group1", "group2"],
    "exclude": {
      "owner_ids": ["test_rules", "noisy_rules"]
    },
    "rule_types": ["SIGMA"],
    "created_after": "2025-01-01",
    "created_before": "2025-02-01",
    "stages": ["production"],
    "statuses": ["active"],
    "severities": ["HIGH"],
    "mitre_tactics": ["execution"],
    "mitre_techniques": ["T1059"]
  },
  "options": {
    "size": 20,
    "sort_by": "created_at",
    "sort_order": "desc",
    "cursor": "eyJjcmVhdGVkX2F0IjoiMjAyNC0wMy0xNFQwOTowMTowMFoiLCJpZCI6InJ1bGUxMjMifQ==",
    "direction": "forward"
  }
}
```

**Example Queries:**

1. Get all SIGMA rules from specific owners with date range and pagination:
```json
{
  "query_text": "",
  "filters": {
    "rule_types": ["SIGMA"],
    "owner_ids": ["group1", "group2", "group3"],
    "exclude": {
      "owner_ids": ["test_rules"]
    },
    "created_after": "2025-01-01T00:00:00Z",
    "created_before": "2025-12-31T23:59:59Z"
  },
  "options": {
    "size": 50,
    "sort_by": "created_at",
    "sort_order": "desc"
  }
}
```

2. Search for process-related rules with specific MITRE tactics:
```json
{
  "query_text": "process",
  "search_fields": ["title", "content"],
  "boost": {
    "title": 3,
    "content": 1
  },
  "filters": {
    "owner_ids": ["windows_group", "linux_group"],
    "exclude": {
      "owner_ids": ["deprecated_rules"]
    },
    "mitre_tactics": ["execution", "privilege-escalation"],
    "severities": ["HIGH", "CRITICAL"]
  }
}
```

3. Find high-severity rules with specific techniques:
```json
{
  "query_text": "",
  "filters": {
    "severities": ["HIGH", "CRITICAL"],
    "mitre_techniques": ["T1059", "T1055", "T1027"],
    "stages": ["production"],
    "statuses": ["active"]
  },
  "options": {
    "sort_by": "severity",
    "sort_order": "desc"
  }
}
```

4. Search across multiple rule types with metadata filtering:
```json
{
  "query_text": "lateral movement",
  "search_fields": ["title", "description", "content", "metadata.tags"],
  "filters": {
    "rule_types": ["SIGMA", "KQL"],
    "metadata": {
      "platforms": ["windows", "linux"],
      "data_sources": ["process_creation", "network_connection"]
    }
  }
}
```

5. Complex query with field-specific boosting and multiple filters:
```json
{
  "query_text": "powershell execution",
  "search_fields": ["title", "description", "content", "metadata.tags"],
  "boost": {
    "title": 4,
    "description": 2,
    "content": 1,
    "metadata.tags": 3
  },
  "filters": {
    "rule_types": ["SIGMA"],
    "severities": ["HIGH", "CRITICAL"],
    "mitre_tactics": ["execution", "defense-evasion"],
    "mitre_techniques": ["T1059.001", "T1059.003"],
    "metadata": {
      "platforms": ["windows"],
      "data_sources": ["process_creation", "powershell_log"]
    },
    "created_after": "2025-01-01",
    "stages": ["production"],
    "statuses": ["active"]
  },
  "options": {
    "size": 20,
    "sort_by": "created_at",
    "sort_order": "desc"
  }
}
```

6. Search with multiple query terms and field-specific matching:
```json
{
  "query_text": "powershell bypass amsi defender",
  "search_fields": ["title^5", "content^3", "metadata.tags^2"],
  "operator": "AND",
  "minimum_should_match": "75%",
  "filters": {
    "rule_types": ["SIGMA", "KQL"],
    "metadata": {
      "data_sources": ["powershell/classic", "powershell/operational", "microsoft-windows-windows defender/operational"]
    }
  },
  "options": {
    "sort_by": "relevance"
  }
}
```

7. Search with exact phrase matching:
```json
{
  "query_text": "\"bypass security\"",
  "search_fields": ["title", "content"],
  "match_type": "phrase",
  "slop": 2,
  "filters": {
    "metadata": {
      "tags": ["defense_evasion", "security_control"]
    }
  }
}
```

8. Metadata-only search with complex filtering:
```json
{
  "filters": {
    "rule_types": ["SIGMA"],
    "metadata": {
      "platforms": ["windows"],
      "data_sources": ["process_creation", "file_event"],
      "tags": {
        "include": ["attack.defense_evasion", "attack.execution"],
        "exclude": ["experimental", "deprecated"]
      },
      "required_fields": ["CommandLine", "ParentCommandLine"],
      "minimum_log_level": "medium"
    },
    "validation_status": "validated",
    "false_positives_rate": "low"
  },
  "options": {
    "size": 100,
    "sort_by": ["severity", "false_positives_rate"],
    "sort_order": ["desc", "asc"]
  }
}
```

9. Search with complex date ranges and multiple criteria:
```json
{
  "filters": {
    "created_after": "2025-01-01T00:00:00Z",
    "created_before": "2025-12-31T23:59:59Z",
    "published_after": "2025-03-01T00:00:00Z",
    "published_before": "2025-12-31T23:59:59Z",
    "updated_after": "2025-06-01T00:00:00Z",
    "updated_before": "2025-12-31T23:59:59Z",
    "last_triggered": {
      "from": "now-30d",
      "to": "now"
    },
    "detection_rate": {
      "min": 0.7,
      "max": 1.0
    }
  },
  "options": {
    "sort_by": ["detection_rate", "last_triggered"],
    "sort_order": ["desc", "desc"],
    "size": 50
  }
}
```

10. Search with negation filters:
```json
{
  "query_text": "network connection",
  "filters": {
    "rule_types": ["SIGMA"],
    "exclude": {
      "group_ids": ["noisy_rules", "test_rules"],
      "severities": ["LOW"],
      "stages": ["development", "testing"],
      "mitre_techniques": ["T1095", "T1043"],
      "metadata": {
        "platforms": ["macos", "linux"],
        "tags": ["experimental", "needs_review"]
      }
    },
    "minimum_confidence": 0.8
  },
  "options": {
    "sort_by": "confidence",
    "sort_order": "desc"
  }
}
```

These examples demonstrate various advanced search capabilities:

- **Field-specific matching**: Target specific fields with different weights
- **Exact phrase matching**: Search for exact phrases with configurable slop
- **Complex metadata filtering**: Include/exclude specific metadata values
- **Multiple sort criteria**: Sort by multiple fields with different orders
- **Date range filtering**: Filter by various date fields with relative dates
- **Negation filtering**: Exclude rules based on various criteria
- **Confidence and detection metrics**: Filter based on rule effectiveness
- **Custom operators**: Use AND/OR operators with minimum match requirements

Note: Some of these examples include hypothetical features that might be planned for future releases. Please refer to the API documentation at `/api/docs` for the currently supported features.

**Response Format:**
```json
{
  "status": "success",
  "meta": {
    "total": 42,
    "size": 20,
    "next_cursor": "eyJjcmVhdGVkX2F0IjoiMjAyNC0wMy0xNFQwOTowMTowMFoiLCJpZCI6InJ1bGUxMjMifQ==",
    "prev_cursor": null
  },
  "data": [{
    "id": "rule123",
    "title": "Suspicious Process Execution",
    "description": "Detects suspicious process execution patterns",
    "content": "rule content...",
    "rule_type": "SIGMA",
    "severity": "HIGH",
    "stage": "production",
    "status": "active",
    "created_at": "2025-01-06T12:00:00Z",
    "updated_at": "2025-01-06T12:00:00Z",
    "metadata": {
      "tags": ["process_creation", "attack.execution"],
      "mitre_tactics": ["execution"],
      "mitre_techniques": ["T1059"],
      "platforms": ["windows"],
      "data_sources": ["process_creation"]
    },
    "_score": 6.5,
    "highlight": {
      "title": ["Suspicious <strong>Process</strong> <strong>Execution</strong>"],
      "content": ["detects suspicious <strong>process</strong> <strong>execution</strong> patterns"]
    }
  }]
}
```

### Cursor-based Pagination

The API uses cursor-based pagination for efficient and consistent results when navigating through large datasets. This approach offers several advantages:
- Consistent results even when data changes
- Better performance for deep pagination
- No skipped or duplicated items

#### Using Cursors

1. Initial Request (First Page):
```json
{
  "options": {
    "size": 20,
    "sort_by": "created_at",
    "sort_order": "desc"
  }
}
```

2. Next Page:
```json
{
  "options": {
    "size": 20,
    "sort_by": "created_at",
    "sort_order": "desc",
    "cursor": "eyJjcmVhdGVkX2F0IjoiMjAyNC0wMy0xNFQwOTowMTowMFoiLCJpZCI6InJ1bGUxMjMifQ=="
  }
}
```

3. Previous Page:
```json
{
  "options": {
    "size": 20,
    "sort_by": "created_at",
    "sort_order": "desc",
    "cursor": "eyJjcmVhdGVkX2F0IjoiMjAyNC0wMy0xNFQwOTowMTowMFoiLCJpZCI6InJ1bGUxMjMifQ==",
    "direction": "backward"
  }
}
```

#### Cursor Format
The cursor is a base64-encoded string containing information about the last item in the current page. It includes:
- Sort field values
- Document ID
- Sort field and order

#### Pagination Flow
1. Make initial request without a cursor
2. Use `next_cursor` from response to get the next page
3. Use `prev_cursor` from response to navigate backward
4. When either cursor is null, you've reached the end/beginning of the results

#### Hybrid Pagination

The Search API now supports a hybrid pagination approach, combining the efficiency of cursor-based pagination with the usability of traditional page-based navigation.

**Key Features:**
- **Direct Page Navigation**: Jump directly to any page (e.g., page 5)
- **Efficient Implementation**: Still uses cursor-based pagination for optimal performance
- **Automatic Cursor Caching**: Cursors are cached as users navigate, making subsequent visits to the same page faster
- **Backwards Compatible**: Existing cursor-based navigation still works

**Usage:**

1. Page-Based Navigation:
```http
GET /api/v1/search?q=detection&page=3&size=20
```

2. Traditional Cursor-Based Navigation:
```http
GET /api/v1/search?q=detection&cursor=eyJzb3J0X3ZhbHVlcyI6WzEuNSwicnVsZTEyMyJdLCJpZCI6InJ1bGUxMjMifQ==
```

**How It Works:**
- When requesting a specific page, the system checks if it has a cached cursor for that page
- If not found, it finds the closest cached page and navigates from there
- Cursors are cached for 30 minutes and are specific to each unique search (query + filters + sort)
- Navigation is optimized by choosing the shortest path

**Response Format:**
```json
{
  "status": "success",
  "meta": {
    "total": 100,
    "size": 20,
    "current_page": 3,
    "total_pages": 5,
    "page_size": 20,
    "has_next_page": true,
    "has_prev_page": true,
    "next_cursor": "eyJzb3J0X3ZhbHVlcyI6WzEuNSwicnVsZTEyMyJdLCJpZCI6InJ1bGUxMjMifQ==",
    "prev_cursor": "eyJzb3J0X3ZhbHVlcyI6WzIuNSwicnVsZTEyMyJdLCJpZCI6InJ1bGUxMjMifQ=="
  },
  "data": [
    // Results
  ]
}
```

#### Similar Rules Search

```http
GET /api/v1/search/similar/:ruleId
```

Find rules similar to a specific rule by ID.

**Parameters:**
- `ruleId` (path): ID of the reference rule
- `min_similarity` (query, optional): Minimum similarity score (0.0-1.0), defaults to 0.7
- `size` (query, optional): Number of similar rules to return

**Example:**
```bash
# Find rules similar to rule with ID 'rule123'
curl "http://localhost:3000/api/v1/search/similar/rule123?min_similarity=0.7&size=5"
```

#### Search Suggestions

```http
GET /api/v1/search/suggestions
```

Get search suggestions based on a prefix.

**Query Parameters:**
- `q` (string): Search prefix
- `size` (number, optional): Number of suggestions to return

**Example:**
```bash
# Get suggestions for "proc"
curl "http://localhost:3000/api/v1/search/suggestions?q=proc&size=5"
```

#### Search Metadata

```http
GET /api/v1/search/metadata
```

Get metadata about available search fields and options.

**Response Example:**
```json
{
  "fields": {
    "title": "text",
    "content": "text",
    "rule_type": "keyword",
    "severity": "keyword"
  },
  "aggregations": {
    "rule_types": ["SIGMA", "SPL", "KQL"],
    "severities": ["LOW", "MEDIUM", "HIGH", "CRITICAL"]
  }
}
```

## Development

### Project Structure

```
src/
├── app.controller.ts
├── app.module.ts
├── app.service.ts
├── auth/
├── decompress/
├── dynamodb/
├── health/
├── main.ts
├── opensearch/
├── parsing/
├── rule-archive/
├── rules/
└── s3/
```

### Git Hooks

The project uses [Husky](https://typicode.github.io/husky/) to manage Git hooks:

- **Pre-commit Hook**: Automatically runs before each commit to ensure code quality
  - Formats code using Prettier
  - Runs all unit tests (with --passWithNoTests flag to prevent failures if no tests exist)

To skip the pre-commit hook (not recommended for normal workflow):

```bash
git commit -m "Your message" --no-verify
```

For more information, see the [Git Hooks documentation](.husky/README.md).

### Running Tests

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:cov
```

### OpenSearch Integration

The application uses OpenSearch for advanced search capabilities. The OpenSearch integration includes:

- **Index Versioning**: Indices are versioned to support schema evolution
- **Aliases**: Aliases are used to provide a stable interface to the indices
- **Migrations**: Automatic migrations to update indices when the schema changes
- **Search Optimization**: Optimized search queries for performance and relevance

#### Index Structure

The OpenSearch index for detection rules includes the following fields:

- `id`: Rule ID
- `title`: Rule title
- `description`: Rule description
- `content`: Rule content
- `rule_type`: Rule type (SIGMA, SPL, KQL, etc.)
- `version`: Rule version
- `stage`: Rule stage
- `status`: Rule status
- `validation_status`: Rule validation status
- `created_by`: Rule creator
- `created_at`: Creation timestamp
- `updated_at`: Update timestamp
- `published_at`: When the rule was published or when it was created if it is a DRAFT
- `owner_id`: Group ID
- `metadata`: Rule metadata
- `all_text`: Concatenated text for full-text search

#### Search Capabilities

The application provides the following search capabilities:

- **Basic Search**: Search rules by query string
- **Advanced Search**: Search with filters and sorting
- **Similar Rule Finding**: Find rules similar to a given rule
- **Autocomplete**: Get suggestions for rule titles

## Deployment

### Docker

The application can be deployed using Docker:

```bash
docker build -t detections-ai-rule-management .
docker run -p 3003:3003 detections-ai-rule-management
```

### AWS

The application can be deployed to AWS using the following services:

- **ECS**: For running the application
- **DynamoDB**: For data persistence
- **OpenSearch Service**: For search capabilities
- **S3**: For file storage
- **IAM**: For access control

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/my-feature`
3. Commit your changes: `git commit -am 'Add my feature'`
4. Push to the branch: `git push origin feature/my-feature`
5. Submit a pull request

## License

This project is licensed under the [MIT License](LICENSE).

## GitHub Packages Integration

This project uses private packages from GitHub Packages, specifically the `@systemtwosecurity/agent-kit-node` package.

### Local Development

For local development, you need to:

1. Set up a GitHub Personal Access Token (Classic) with `read:packages` scope
2. Add it to your `.env` file as `GITHUB_TOKEN=your_token_here`
3. Run the installation script: `./install-agent-kit.sh`

### CI/CD Pipeline

The CI/CD pipeline automatically handles GitHub Packages authentication using a repository secret. For more details, see [GitHub Packages Setup](./docs/github-packages-setup.md).
